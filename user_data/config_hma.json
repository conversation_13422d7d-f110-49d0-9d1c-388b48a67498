{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 3, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "timeframe": "1h", "dry_run": true, "dry_run_wallet": 10000, "cancel_open_orders_on_exit": false, "strategy": "HMACrossoverATRStrategy", "trading_mode": "futures", "margin_mode": "isolated", "minimal_roi": {"0": 0.08, "30": 0.05, "60": 0.03, "120": 0.01, "240": 0.0}, "stoploss": -0.15, "trailing_stop": true, "trailing_stop_positive": 0.02, "trailing_stop_positive_offset": 0.03, "trailing_only_offset_is_reached": true, "use_exit_signal": true, "exit_profit_only": false, "ignore_roi_if_entry_signal": false, "position_adjustment_enable": false, "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "order_types": {"entry": "market", "exit": "market", "stoploss": "market", "stoploss_on_exchange": true, "stoploss_price_type": "last"}, "order_time_in_force": {"entry": "GTC", "exit": "GTC"}, "exchange": {"name": "binance", "key": "4WAy2gwqtbjDyoP716wTLKGLPgSL9EwAQtULH4Ab7RKKJenToYTYaj5KxAJgmJpP", "secret": "5wlN34C2EIKQ6bnyE7qNp8aW2NeIOuCYjH8hxY8wwaTnnwR7G4q5qnmTtD1Gl2U5", "ccxt_config": {"httpsProxy": "http://*************:12334", "wsProxy": "http://*************:12334"}, "ccxt_async_config": {}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT"], "pair_blacklist": [".*_.*"]}, "pairlists": [{"method": "StaticPairList"}], "telegram": {"enabled": true, "token": "**********************************************", "chat_id": "8092782083", "notification_settings": {"status": "on", "warning": "on", "startup": "on", "entry": "on", "entry_fill": "on", "exit": {"roi": "on", "emergency_exit": "on", "force_exit": "on", "exit_signal": "on", "trailing_stop_loss": "on", "stop_loss": "on", "stoploss_on_exchange": "on", "custom_exit": "on"}, "exit_fill": "on", "entry_cancel": "on", "exit_cancel": "on", "protection_trigger": "on", "protection_trigger_global": "on", "show_candle": "off"}}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8084, "verbosity": "error", "jwt_secret_key": "hma_strategy_secret_key", "CORS_origins": [], "username": "hma_bot", "password": "hma_admin"}, "bot_name": "HMA_Crossover_ATR_Bot", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 10}, "leverage_tiers": {"BTC/USDT:USDT": {"min_leverage": 2, "max_leverage": 18}, "ETH/USDT:USDT": {"min_leverage": 2, "max_leverage": 18}, "SOL/USDT:USDT": {"min_leverage": 2, "max_leverage": 18}}}