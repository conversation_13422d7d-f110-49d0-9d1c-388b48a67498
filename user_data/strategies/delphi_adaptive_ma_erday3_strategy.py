# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class DelphiAdaptiveMAERDay3Strategy(IStrategy):
    """
    Delphi自适应移动平均策略（ERDay3模式）
    
    策略逻辑：
    1. 使用自适应移动平均线作为主要信号
    2. ERDay3模式：ERDay的第三个版本，参数进一步优化
    3. 价格穿越均线时产生交易信号
    4. 使用ATR计算动态止损和目标
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表 - ERDay3模式更精细
    minimal_roi = {
        "0": 0.10,      # 10%止盈
        "30": 0.05,     # 30分钟后5%止盈
        "60": 0.03,     # 1小时后3%止盈
        "120": 0.02,    # 2小时后2%止盈
        "240": 0.008,   # 4小时后0.8%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.06  # 6%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.035
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 150

    # 策略参数 - ERDay3模式参数（优化后）
    variable1 = DecimalParameter(1.3, 2.1, default=1.858, space="buy", optimize=True)   # ATR倍数1
    variable2 = DecimalParameter(0.6, 1.0, default=0.837, space="buy", optimize=True)   # ATR倍数2
    variable3 = DecimalParameter(0.1, 0.25, default=0.194, space="buy", optimize=True) # 部分止盈倍数
    variable4 = DecimalParameter(2.2, 3.5, default=3.443, space="buy", optimize=True)   # 目标倍数
    variable5 = DecimalParameter(0.25, 0.5, default=0.26, space="buy", optimize=True) # 止损倍数

    # ATR参数
    atr_long_period = IntParameter(30, 40, default=36, space="buy", optimize=True)
    atr_short_period = IntParameter(10, 16, default=10, space="buy", optimize=True)

    # 自适应移动平均参数
    ama_period = IntParameter(8, 15, default=8, space="buy", optimize=True)
    ama_fast = IntParameter(6, 12, default=11, space="buy", optimize=True)
    ama_slow = IntParameter(100, 140, default=126, space="buy", optimize=True)

    # ERDay3特有参数
    efficiency_ratio_period = IntParameter(7, 15, default=11, space="buy", optimize=True)
    er_threshold = DecimalParameter(0.2, 0.45, default=0.258, space="buy", optimize=True)
    day_multiplier = DecimalParameter(1.4, 2.4, default=2.352, space="buy", optimize=True)
    intraday_filter = BooleanParameter(default=False, space="buy", optimize=True)
    volatility_filter = BooleanParameter(default=False, space="buy", optimize=True)
    momentum_filter = BooleanParameter(default=True, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算ATR
        dataframe['atr_long'] = ta.ATR(dataframe, timeperiod=self.atr_long_period.value)
        dataframe['atr_short'] = ta.ATR(dataframe, timeperiod=self.atr_short_period.value)
        
        # 计算效率比率（Efficiency Ratio）
        dataframe['er'] = self.calculate_efficiency_ratio(dataframe)
        
        # 计算自适应移动平均线
        dataframe['adaptive_ma'] = ta.KAMA(
            dataframe['close'], 
            timeperiod=self.ama_period.value
        )
        
        # 如果KAMA不可用，使用EMA作为备选
        if dataframe['adaptive_ma'].isna().all():
            dataframe['adaptive_ma'] = ta.EMA(dataframe['close'], timeperiod=self.ama_period.value)
        
        # ERDay3模式：基于效率比率和日内特征调整通道
        er_day3_atr = dataframe['atr_short'] * (1 + dataframe['er'] * self.day_multiplier.value)
        dataframe['erday3_upper'] = dataframe['adaptive_ma'] + er_day3_atr
        dataframe['erday3_lower'] = dataframe['adaptive_ma'] - er_day3_atr
        
        # 波动率过滤器
        if self.volatility_filter.value:
            dataframe['volatility'] = ta.STDDEV(dataframe['close'], timeperiod=20)
            dataframe['volatility_ma'] = ta.EMA(dataframe['volatility'], timeperiod=10)
            dataframe['high_volatility'] = dataframe['volatility'] > dataframe['volatility_ma']
        else:
            dataframe['high_volatility'] = True
        
        # 动量过滤器
        if self.momentum_filter.value:
            dataframe['momentum'] = ta.MOM(dataframe['close'], timeperiod=10)
            dataframe['momentum_ma'] = ta.EMA(dataframe['momentum'], timeperiod=5)
            dataframe['positive_momentum'] = dataframe['momentum'] > dataframe['momentum_ma']
            dataframe['negative_momentum'] = dataframe['momentum'] < dataframe['momentum_ma']
        else:
            dataframe['positive_momentum'] = True
            dataframe['negative_momentum'] = True
        
        # 日内过滤器
        if self.intraday_filter.value:
            # 简化的日内过滤：基于小时
            dataframe['hour'] = dataframe.index.hour if hasattr(dataframe.index, 'hour') else 12
            dataframe['intraday_active'] = True  # 简化为始终激活
        else:
            dataframe['intraday_active'] = True
        
        # 效率比率过滤
        dataframe['high_efficiency'] = dataframe['er'] > self.er_threshold.value
        
        # 计算交易信号
        dataframe['close_above_ma'] = dataframe['close'] > dataframe['adaptive_ma']
        dataframe['close_below_ma'] = dataframe['close'] < dataframe['adaptive_ma']
        
        # 穿越信号
        dataframe['cross_above_ma'] = qtpylib.crossed_above(dataframe['close'], dataframe['adaptive_ma'])
        dataframe['cross_below_ma'] = qtpylib.crossed_below(dataframe['close'], dataframe['adaptive_ma'])
        
        # ERDay3入场条件
        dataframe['erday3_long_signal'] = (
            (dataframe['cross_above_ma']) &
            (dataframe['high_efficiency']) &
            (dataframe['intraday_active']) &
            (dataframe['high_volatility']) &
            (dataframe['positive_momentum']) &
            (dataframe['high'] >= dataframe['erday3_upper'])
        )
        
        dataframe['erday3_short_signal'] = (
            (dataframe['cross_below_ma']) &
            (dataframe['high_efficiency']) &
            (dataframe['intraday_active']) &
            (dataframe['high_volatility']) &
            (dataframe['negative_momentum']) &
            (dataframe['low'] <= dataframe['erday3_lower'])
        )
        
        # 计算动态止损和目标
        # 多头止损和目标
        dataframe['long_stop_loss'] = (
            dataframe['adaptive_ma'] - 
            dataframe['atr_short'] * self.variable5.value
        )
        dataframe['long_target1'] = (
            dataframe['adaptive_ma'] + 
            dataframe['atr_short'] * self.variable3.value
        )
        dataframe['long_target2'] = (
            dataframe['adaptive_ma'] + 
            dataframe['atr_short'] * self.variable4.value
        )
        
        # 空头止损和目标
        dataframe['short_stop_loss'] = (
            dataframe['adaptive_ma'] + 
            dataframe['atr_short'] * self.variable5.value
        )
        dataframe['short_target1'] = (
            dataframe['adaptive_ma'] - 
            dataframe['atr_short'] * self.variable3.value
        )
        dataframe['short_target2'] = (
            dataframe['adaptive_ma'] - 
            dataframe['atr_short'] * self.variable4.value
        )

        return dataframe

    def calculate_efficiency_ratio(self, dataframe: DataFrame) -> DataFrame:
        """
        计算效率比率（Efficiency Ratio）
        ER = Direction / Volatility
        """
        period = self.efficiency_ratio_period.value
        
        # 计算方向（净价格变化）
        direction = abs(dataframe['close'] - dataframe['close'].shift(period))
        
        # 计算波动性（价格变化的总和）
        volatility = dataframe['close'].diff().abs().rolling(window=period).sum()
        
        # 计算效率比率
        efficiency_ratio = direction / volatility
        
        # 处理除零情况
        efficiency_ratio = efficiency_ratio.fillna(0)
        efficiency_ratio = efficiency_ratio.replace([np.inf, -np.inf], 0)
        
        return efficiency_ratio

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：ERDay3多头信号
        dataframe.loc[
            (
                (dataframe['erday3_long_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：ERDay3空头信号
        dataframe.loc[
            (
                (dataframe['erday3_short_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：价格下穿自适应均线、达到止损或条件不满足
        dataframe.loc[
            (
                (dataframe['cross_below_ma']) |
                (dataframe['low'] <= dataframe['long_stop_loss']) |
                (dataframe['high'] >= dataframe['long_target2']) |  # 达到目标2
                (~dataframe['high_efficiency']) |  # 效率比率下降
                (~dataframe['intraday_active']) |  # 日内时间结束
                (~dataframe['high_volatility']) |  # 波动率下降
                (~dataframe['positive_momentum'])  # 动量转负
            ),
            'exit_long'] = 1

        # 空头出场：价格上穿自适应均线、达到止损或条件不满足
        dataframe.loc[
            (
                (dataframe['cross_above_ma']) |
                (dataframe['high'] >= dataframe['short_stop_loss']) |
                (dataframe['low'] <= dataframe['short_target2']) |   # 达到目标2
                (~dataframe['high_efficiency']) |  # 效率比率下降
                (~dataframe['intraday_active']) |  # 日内时间结束
                (~dataframe['high_volatility']) |  # 波动率下降
                (~dataframe['negative_momentum'])  # 动量转正
            ),
            'exit_short'] = 1

        return dataframe
