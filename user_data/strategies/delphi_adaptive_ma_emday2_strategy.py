# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class DelphiAdaptiveMAEMDay2Strategy(IStrategy):
    """
    Delphi自适应移动平均策略（EMDay2模式）
    
    策略逻辑：
    1. 使用自适应移动平均线作为主要信号
    2. EMDay2模式：EMDay的改进版本
    3. 价格穿越均线时产生交易信号
    4. 使用ATR计算动态止损和目标
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表 - EMDay2模式更保守
    minimal_roi = {
        "0": 0.07,      # 7%止盈
        "60": 0.035,    # 1小时后3.5%止盈
        "120": 0.02,    # 2小时后2%止盈
        "240": 0.01,    # 4小时后1%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.05  # 5%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.018
    trailing_stop_positive_offset = 0.028
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 150

    # 策略参数 - EMDay2模式参数（相比EMDay有所调整）
    variable1 = DecimalParameter(1.0, 1.7, default=1.35, space="buy", optimize=True)   # ATR倍数1
    variable2 = DecimalParameter(0.4, 0.8, default=0.6, space="buy", optimize=True)    # ATR倍数2
    variable3 = DecimalParameter(0.06, 0.18, default=0.12, space="buy", optimize=True) # 部分止盈倍数
    variable4 = DecimalParameter(1.6, 2.6, default=2.1, space="buy", optimize=True)    # 目标倍数
    variable5 = DecimalParameter(0.18, 0.38, default=0.28, space="buy", optimize=True) # 止损倍数
    
    # ATR参数
    atr_long_period = IntParameter(30, 40, default=34, space="buy", optimize=True)
    atr_short_period = IntParameter(10, 16, default=13, space="buy", optimize=True)
    
    # 自适应移动平均参数
    ama_period = IntParameter(8, 15, default=10, space="buy", optimize=True)
    ama_fast = IntParameter(6, 12, default=8, space="buy", optimize=True)
    ama_slow = IntParameter(100, 140, default=120, space="buy", optimize=True)
    
    # EMDay2特有参数
    em_multiplier = DecimalParameter(0.6, 1.2, default=0.9, space="buy", optimize=True)
    day_filter = BooleanParameter(default=True, space="buy", optimize=True)
    trend_filter = BooleanParameter(default=True, space="buy", optimize=True)
    volume_filter = BooleanParameter(default=True, space="buy", optimize=True)
    volatility_filter = BooleanParameter(default=True, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算ATR
        dataframe['atr_long'] = ta.ATR(dataframe, timeperiod=self.atr_long_period.value)
        dataframe['atr_short'] = ta.ATR(dataframe, timeperiod=self.atr_short_period.value)
        
        # 计算自适应移动平均线
        dataframe['adaptive_ma'] = ta.KAMA(
            dataframe['close'], 
            timeperiod=self.ama_period.value
        )
        
        # 如果KAMA不可用，使用EMA作为备选
        if dataframe['adaptive_ma'].isna().all():
            dataframe['adaptive_ma'] = ta.EMA(dataframe['close'], timeperiod=self.ama_period.value)
        
        # EMDay2模式：计算EM调整的日内通道
        em_adjusted_atr = dataframe['atr_short'] * self.em_multiplier.value
        dataframe['emday2_upper'] = dataframe['adaptive_ma'] + em_adjusted_atr
        dataframe['emday2_lower'] = dataframe['adaptive_ma'] - em_adjusted_atr
        
        # 趋势过滤器
        if self.trend_filter.value:
            dataframe['trend_ema'] = ta.EMA(dataframe['close'], timeperiod=50)
            dataframe['bullish_trend'] = dataframe['close'] > dataframe['trend_ema']
            dataframe['bearish_trend'] = dataframe['close'] < dataframe['trend_ema']
        else:
            dataframe['bullish_trend'] = True
            dataframe['bearish_trend'] = True
        
        # 成交量过滤器
        if self.volume_filter.value:
            dataframe['volume_ma'] = ta.EMA(dataframe['volume'], timeperiod=20)
            dataframe['high_volume'] = dataframe['volume'] > dataframe['volume_ma']
        else:
            dataframe['high_volume'] = True
        
        # 波动率过滤器
        if self.volatility_filter.value:
            dataframe['volatility'] = ta.STDDEV(dataframe['close'], timeperiod=20)
            dataframe['volatility_ma'] = ta.EMA(dataframe['volatility'], timeperiod=10)
            dataframe['high_volatility'] = dataframe['volatility'] > dataframe['volatility_ma']
        else:
            dataframe['high_volatility'] = True
        
        # 日内过滤器
        if self.day_filter.value:
            # 简化的日内过滤：基于小时
            dataframe['hour'] = dataframe.index.hour if hasattr(dataframe.index, 'hour') else 12
            dataframe['day_active'] = True  # 简化为始终激活
        else:
            dataframe['day_active'] = True
        
        # 计算交易信号
        dataframe['close_above_ma'] = dataframe['close'] > dataframe['adaptive_ma']
        dataframe['close_below_ma'] = dataframe['close'] < dataframe['adaptive_ma']
        
        # 穿越信号
        dataframe['cross_above_ma'] = qtpylib.crossed_above(dataframe['close'], dataframe['adaptive_ma'])
        dataframe['cross_below_ma'] = qtpylib.crossed_below(dataframe['close'], dataframe['adaptive_ma'])
        
        # EMDay2入场条件
        dataframe['emday2_long_signal'] = (
            (dataframe['cross_above_ma']) &
            (dataframe['bullish_trend']) &
            (dataframe['high_volume']) &
            (dataframe['high_volatility']) &
            (dataframe['day_active']) &
            (dataframe['high'] >= dataframe['emday2_upper'])
        )
        
        dataframe['emday2_short_signal'] = (
            (dataframe['cross_below_ma']) &
            (dataframe['bearish_trend']) &
            (dataframe['high_volume']) &
            (dataframe['high_volatility']) &
            (dataframe['day_active']) &
            (dataframe['low'] <= dataframe['emday2_lower'])
        )
        
        # 计算动态止损和目标
        # 多头止损和目标
        dataframe['long_stop_loss'] = (
            dataframe['adaptive_ma'] - 
            dataframe['atr_short'] * self.variable5.value
        )
        dataframe['long_target1'] = (
            dataframe['adaptive_ma'] + 
            dataframe['atr_short'] * self.variable3.value
        )
        dataframe['long_target2'] = (
            dataframe['adaptive_ma'] + 
            dataframe['atr_short'] * self.variable4.value
        )
        
        # 空头止损和目标
        dataframe['short_stop_loss'] = (
            dataframe['adaptive_ma'] + 
            dataframe['atr_short'] * self.variable5.value
        )
        dataframe['short_target1'] = (
            dataframe['adaptive_ma'] - 
            dataframe['atr_short'] * self.variable3.value
        )
        dataframe['short_target2'] = (
            dataframe['adaptive_ma'] - 
            dataframe['atr_short'] * self.variable4.value
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：EMDay2多头信号
        dataframe.loc[
            (
                (dataframe['emday2_long_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：EMDay2空头信号
        dataframe.loc[
            (
                (dataframe['emday2_short_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：价格下穿自适应均线、达到止损或条件不满足
        dataframe.loc[
            (
                (dataframe['cross_below_ma']) |
                (dataframe['low'] <= dataframe['long_stop_loss']) |
                (dataframe['high'] >= dataframe['long_target2']) |  # 达到目标2
                (~dataframe['bullish_trend']) |  # 趋势转空
                (~dataframe['high_volume']) |    # 成交量下降
                (~dataframe['high_volatility']) | # 波动率下降
                (~dataframe['day_active'])       # 日内时间结束
            ),
            'exit_long'] = 1

        # 空头出场：价格上穿自适应均线、达到止损或条件不满足
        dataframe.loc[
            (
                (dataframe['cross_above_ma']) |
                (dataframe['high'] >= dataframe['short_stop_loss']) |
                (dataframe['low'] <= dataframe['short_target2']) |   # 达到目标2
                (~dataframe['bearish_trend']) |  # 趋势转多
                (~dataframe['high_volume']) |    # 成交量下降
                (~dataframe['high_volatility']) | # 波动率下降
                (~dataframe['day_active'])       # 日内时间结束
            ),
            'exit_short'] = 1

        return dataframe
