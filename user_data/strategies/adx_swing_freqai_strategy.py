# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union

from freqtrade.strategy import (BooleanParameter, CategoricalParameter, DecimalParameter,
                                IntParameter, IStrategy, merge_informative_pair)
from functools import reduce

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib


class ADXSwingFreqAIStrategy(IStrategy):
    """
    ADX摆动高低点突破策略 - FreqAI增强版本
    
    策略逻辑：
    1. 使用ADX指标动态调整摆动点识别周期
    2. 识别摆动高低点
    3. 突破摆动高点时做多做空
    4. 集成FreqAI进行价格预测和信号过滤
    
    FreqAI增强功能:
    - AI价格预测: 预测未来价格走势
    - 信号过滤: 使用AI过滤交易信号
    - 动态参数: 根据市场条件调整策略参数
    - 风险评估: AI评估交易风险
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '30m'
    can_short = True

    # ROI表 - 使用最佳优化结果
    minimal_roi = {
        "0": 0.389,     # 38.9%止盈
        "100": 0.152,   # 100分钟后15.2%止盈
        "337": 0.046,   # 337分钟后4.6%止盈
        "949": 0        # 949分钟后0%止盈
    }

    # 止损设置 - 使用最佳优化结果
    stoploss = -0.23  # 23%止损

    # 追踪止损 - 使用最佳优化结果
    trailing_stop = True
    trailing_stop_positive = 0.019  # 1.9%正向追踪
    trailing_stop_positive_offset = 0.061  # 6.1%偏移
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 48

    # 策略参数 - 使用最佳优化结果
    adx_length = IntParameter(10, 20, default=12, space="buy", optimize=False)
    adx_base_level = IntParameter(10, 20, default=18, space="buy", optimize=False)
    adx_increment = IntParameter(5, 15, default=9, space="buy", optimize=False)
    swing_length_max = IntParameter(3, 8, default=8, space="buy", optimize=False)
    swing_length_min = IntParameter(1, 3, default=1, space="buy", optimize=False)

    # 杠杆参数
    leverage_optimize = IntParameter(10, 100, default=43, space="buy", optimize=False)

    # 出场参数
    exit_adx_threshold = IntParameter(15, 35, default=16, space="sell", optimize=False)
    profit_target = DecimalParameter(0.02, 0.08, default=0.037, space="sell", optimize=False)
    
    # FreqAI参数
    freqai_enabled = True
    freqai_prediction_threshold = DecimalParameter(0.5, 0.9, default=0.7, space="buy", optimize=True)
    freqai_signal_filter = BooleanParameter(default=True, space="buy", optimize=True)

    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def feature_engineering_expand_all(self, dataframe: DataFrame, period: int,
                                        metadata: dict, **kwargs) -> DataFrame:
        """
        FreqAI特征工程 - 扩展所有特征
        """

        # 基础技术指标
        dataframe[f"rsi_{period}"] = ta.RSI(dataframe, timeperiod=period)
        dataframe[f"adx_{period}"] = ta.ADX(dataframe, timeperiod=period)
        dataframe[f"plus_di_{period}"] = ta.PLUS_DI(dataframe, timeperiod=period)
        dataframe[f"minus_di_{period}"] = ta.MINUS_DI(dataframe, timeperiod=period)

        # 移动平均线
        dataframe[f"ema_{period}"] = ta.EMA(dataframe, timeperiod=period)
        dataframe[f"sma_{period}"] = ta.SMA(dataframe, timeperiod=period)

        # 布林带
        bb_lower, bb_middle, bb_upper = ta.BBANDS(dataframe, timeperiod=period)
        dataframe[f"bb_lower_{period}"] = bb_lower
        dataframe[f"bb_middle_{period}"] = bb_middle
        dataframe[f"bb_upper_{period}"] = bb_upper
        dataframe[f"bb_width_{period}"] = (bb_upper - bb_lower) / bb_middle

        # MACD
        macd, macdsignal, macdhist = ta.MACD(dataframe)
        dataframe[f"macd_{period}"] = macd
        dataframe[f"macdsignal_{period}"] = macdsignal
        dataframe[f"macdhist_{period}"] = macdhist

        # 价格相关特征
        dataframe[f"price_change_{period}"] = dataframe['close'].pct_change(period)
        dataframe[f"high_low_ratio_{period}"] = dataframe['high'] / dataframe['low']
        dataframe[f"volume_sma_{period}"] = dataframe['volume'].rolling(period).mean()

        # 波动率特征
        dataframe[f"atr_{period}"] = ta.ATR(dataframe, timeperiod=period)
        dataframe[f"volatility_{period}"] = dataframe['close'].rolling(period).std()

        return dataframe

    def feature_engineering_expand_basic(self, dataframe: DataFrame, metadata: dict, **kwargs) -> DataFrame:
        """
        FreqAI特征工程 - 基础特征
        """

        # 价格位置特征
        dataframe["%-price_position"] = (dataframe['close'] - dataframe['low'].rolling(20).min()) / \
                                       (dataframe['high'].rolling(20).max() - dataframe['low'].rolling(20).min())

        # 成交量特征
        dataframe["%-volume_ratio"] = dataframe['volume'] / dataframe['volume'].rolling(20).mean()

        # 趋势强度
        dataframe["%-trend_strength"] = (dataframe['close'] - dataframe['close'].shift(10)) / dataframe['close'].shift(10)

        # ADX相关特征
        dataframe["%-adx_strength"] = dataframe['adx'] / 100
        dataframe["%-di_diff"] = (dataframe['plus_di'] - dataframe['minus_di']) / 100

        return dataframe

    def feature_engineering_standard(self, dataframe: DataFrame, metadata: dict, **kwargs) -> DataFrame:
        """
        FreqAI特征工程 - 标准特征
        """

        # 摆动点特征
        dataframe['swing_high_distance'] = 0
        dataframe['swing_low_distance'] = 0

        # 计算距离最近摆动点的距离
        for i in range(len(dataframe)):
            # 找到最近的摆动高点
            recent_high_idx = None
            for j in range(i, -1, -1):
                if dataframe.iloc[j]['swing_high']:
                    recent_high_idx = j
                    break

            if recent_high_idx is not None:
                dataframe.iloc[i, dataframe.columns.get_loc('swing_high_distance')] = i - recent_high_idx

            # 找到最近的摆动低点
            recent_low_idx = None
            for j in range(i, -1, -1):
                if dataframe.iloc[j]['swing_low']:
                    recent_low_idx = j
                    break

            if recent_low_idx is not None:
                dataframe.iloc[i, dataframe.columns.get_loc('swing_low_distance')] = i - recent_low_idx

        # 标准化特征
        dataframe["%-swing_high_distance"] = dataframe['swing_high_distance'] / 100
        dataframe["%-swing_low_distance"] = dataframe['swing_low_distance'] / 100

        return dataframe

    def set_freqai_targets(self, dataframe: DataFrame, metadata: dict, **kwargs) -> DataFrame:
        """
        设置FreqAI预测目标
        """

        # 预测未来价格变化
        dataframe["&-s_close"] = (
            dataframe["close"]
            .shift(-self.freqai_info["feature_parameters"]["label_period_candles"])
            .rolling(self.freqai_info["feature_parameters"]["label_period_candles"])
            .mean()
            / dataframe["close"]
            - 1
        )

        return dataframe

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标和FreqAI特征
        """
        
        # 计算ADX指标
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_length.value)
        dataframe['plus_di'] = ta.PLUS_DI(dataframe, timeperiod=self.adx_length.value)
        dataframe['minus_di'] = ta.MINUS_DI(dataframe, timeperiod=self.adx_length.value)
        
        # 添加更多技术指标作为FreqAI特征
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        dataframe['bb_lowerband'], dataframe['bb_middleband'], dataframe['bb_upperband'] = ta.BBANDS(dataframe, timeperiod=20)
        dataframe['macd'], dataframe['macdsignal'], dataframe['macdhist'] = ta.MACD(dataframe)
        dataframe['ema_12'] = ta.EMA(dataframe, timeperiod=12)
        dataframe['ema_26'] = ta.EMA(dataframe, timeperiod=26)
        dataframe['sma_50'] = ta.SMA(dataframe, timeperiod=50)
        
        # 价格变化率
        dataframe['price_change'] = dataframe['close'].pct_change()
        dataframe['volume_change'] = dataframe['volume'].pct_change()
        
        # 波动率指标
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        dataframe['volatility'] = dataframe['close'].rolling(window=20).std()
        
        # 动态摆动识别周期
        dataframe['swing_period'] = np.where(
            dataframe['adx'] > self.adx_base_level.value + self.adx_increment.value,
            self.swing_length_min.value,
            self.swing_length_max.value
        )
        
        # 识别摆动高低点
        dataframe['swing_high'] = False
        dataframe['swing_low'] = False
        dataframe['swing_high_price'] = np.nan
        dataframe['swing_low_price'] = np.nan
        
        # 摆动高点识别
        for i in range(len(dataframe)):
            swing_period = int(dataframe.iloc[i]['swing_period'])
            
            if i >= swing_period and i < len(dataframe) - swing_period:
                is_swing_high = True
                current_high = dataframe.iloc[i]['high']
                
                for j in range(max(0, i - swing_period), min(len(dataframe), i + swing_period + 1)):
                    if j != i and dataframe.iloc[j]['high'] >= current_high:
                        is_swing_high = False
                        break
                
                if is_swing_high:
                    dataframe.iloc[i, dataframe.columns.get_loc('swing_high')] = True
                    dataframe.iloc[i, dataframe.columns.get_loc('swing_high_price')] = current_high
        
        # 摆动低点识别
        for i in range(len(dataframe)):
            swing_period = int(dataframe.iloc[i]['swing_period'])
            
            if i >= swing_period and i < len(dataframe) - swing_period:
                is_swing_low = True
                current_low = dataframe.iloc[i]['low']
                
                for j in range(max(0, i - swing_period), min(len(dataframe), i + swing_period + 1)):
                    if j != i and dataframe.iloc[j]['low'] <= current_low:
                        is_swing_low = False
                        break
                
                if is_swing_low:
                    dataframe.iloc[i, dataframe.columns.get_loc('swing_low')] = True
                    dataframe.iloc[i, dataframe.columns.get_loc('swing_low_price')] = current_low
        
        # 向前填充最近的摆动高低点
        dataframe['last_swing_high'] = dataframe['swing_high_price'].ffill()
        dataframe['last_swing_low'] = dataframe['swing_low_price'].ffill()
        
        # 计算入场价格
        dataframe['long_entry_price'] = dataframe['last_swing_high'] * 1.001
        dataframe['short_entry_price'] = dataframe['last_swing_low'] * 0.999
        
        # 基础突破条件
        dataframe['long_breakout'] = (
            (dataframe['high'] >= dataframe['long_entry_price']) &
            (dataframe['last_swing_high'].notna()) &
            (dataframe['adx'] > 20)  # 使用固定阈值
        )

        dataframe['short_breakout'] = (
            (dataframe['low'] <= dataframe['short_entry_price']) &
            (dataframe['last_swing_low'].notna()) &
            (dataframe['adx'] > 20)  # 使用固定阈值
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号 - 集成FreqAI预测
        """
        
        # 基础多头条件
        long_conditions = [
            dataframe['long_breakout'],
            dataframe['volume'] > 0
        ]
        
        # 基础空头条件
        short_conditions = [
            dataframe['short_breakout'],
            dataframe['volume'] > 0
        ]
        
        # 如果启用FreqAI信号过滤
        if self.freqai_signal_filter.value:
            # 添加FreqAI预测条件
            long_conditions.append(dataframe.get('&-prediction', 0) > self.freqai_prediction_threshold.value)
            short_conditions.append(dataframe.get('&-prediction', 0) < -self.freqai_prediction_threshold.value)
        
        # 多头入场
        dataframe.loc[
            reduce(lambda x, y: x & y, long_conditions),
            'enter_long'] = 1
        
        # 空头入场
        dataframe.loc[
            reduce(lambda x, y: x & y, short_conditions),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号 - 集成FreqAI预测
        """
        
        # 多头出场条件
        long_exit_conditions = [
            (dataframe['adx'] < self.exit_adx_threshold.value) |
            (dataframe['swing_low'])
        ]
        
        # 空头出场条件
        short_exit_conditions = [
            (dataframe['adx'] < self.exit_adx_threshold.value) |
            (dataframe['swing_high'])
        ]
        
        # 如果启用FreqAI信号过滤
        if self.freqai_signal_filter.value:
            long_exit_conditions.append(dataframe.get('&-prediction', 0) < -self.freqai_prediction_threshold.value)
            short_exit_conditions.append(dataframe.get('&-prediction', 0) > self.freqai_prediction_threshold.value)
        
        # 多头出场
        dataframe.loc[
            reduce(lambda x, y: x | y, long_exit_conditions),
            'exit_long'] = 1
        
        # 空头出场
        dataframe.loc[
            reduce(lambda x, y: x | y, short_exit_conditions),
            'exit_short'] = 1

        return dataframe

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str | None,
                 side: str, **kwargs) -> float:
        """
        自定义杠杆设置
        """
        return self.leverage_optimize.value
