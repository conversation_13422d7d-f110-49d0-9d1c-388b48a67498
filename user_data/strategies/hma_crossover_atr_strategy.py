# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file

import numpy as np
import pandas as pd
from datetime import datetime
from pandas import DataFrame
from typing import Optional, Union

from freqtrade.strategy import (
    IStrategy,
    Trade,
    Order,
    PairLocks,
    informative,
    BooleanParameter,
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    RealParameter,
    timeframe_to_minutes,
    timeframe_to_next_date,
    timeframe_to_prev_date,
    merge_informative_pair,
    stoploss_from_absolute,
    stoploss_from_open,
)

import talib.abstract as ta
from technical import qtpylib


class HMACrossoverATRStrategy(IStrategy):
    """
    HMA Crossover + ATR + Curvature Strategy
    
    Based on Pine Script: HMA Crossover + ATR + Curvature (Long & Short)
    
    Strategy Logic:
    - Uses fast and slow Hull Moving Averages for trend detection
    - Calculates curvature (second derivative) for acceleration
    - Entry: HMA crossover + curvature threshold
    - Risk Management: ATR-based stop loss and trailing stop
    - Supports both long and short positions
    """

    INTERFACE_VERSION = 3

    # Can this strategy go short?
    can_short: bool = True

    # Optimal timeframe for the strategy
    timeframe = "1h"

    # Run "populate_indicators()" only for new candle
    process_only_new_candles = True

    # These values can be overridden in the config
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 100

    # Hyperoptable parameters
    fast_length = IntParameter(10, 25, default=15, space="buy", optimize=True, load=True)
    slow_length = IntParameter(25, 50, default=34, space="buy", optimize=True, load=True)
    atr_length = IntParameter(10, 20, default=14, space="buy", optimize=True, load=True)
    risk_percent = RealParameter(0.5, 3.0, default=1.0, space="buy", optimize=True, load=True)
    atr_mult = RealParameter(1.0, 3.0, default=1.5, space="sell", optimize=True, load=True)
    trail_mult = RealParameter(0.5, 2.0, default=1.0, space="sell", optimize=True, load=True)
    curv_thresh = RealParameter(-0.05, 0.05, default=0.0, space="buy", optimize=True, load=True)
    
    # Leverage parameter for futures trading
    leverage_multiplier = RealParameter(2.0, 18.0, default=5.0, space="buy", optimize=True, load=True)

    # Optional order type mapping
    order_types = {
        "entry": "market",
        "exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": True,
    }

    # Optional order time in force
    order_time_in_force = {
        "entry": "GTC",
        "exit": "GTC",
    }

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        """
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several different TA indicators to the given DataFrame
        """
        
        # Hull Moving Averages
        dataframe['fast_hma'] = qtpylib.hull_moving_average(dataframe['close'], window=self.fast_length.value)
        dataframe['slow_hma'] = qtpylib.hull_moving_average(dataframe['close'], window=self.slow_length.value)
        
        # ATR for risk management
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_length.value)
        
        # Curvature calculation (second derivative approximation)
        dataframe['hma_change'] = dataframe['fast_hma'].diff()
        dataframe['curvature'] = dataframe['hma_change'].diff()
        
        # Additional indicators for confirmation
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the entry signal for the given dataframe
        """
        
        # Long entry conditions
        dataframe.loc[
            (
                # HMA crossover: fast crosses above slow
                (qtpylib.crossed_above(dataframe['fast_hma'], dataframe['slow_hma']))
                &
                # Curvature condition: acceleration above threshold
                (dataframe['curvature'] > self.curv_thresh.value)
                &
                # Volume confirmation
                (dataframe['volume'] > dataframe['volume_sma'])
                &
                # Make sure we have valid data
                (dataframe['fast_hma'].notna())
                & (dataframe['slow_hma'].notna())
                & (dataframe['curvature'].notna())
                & (dataframe['atr'].notna())
            ),
            'enter_long'] = 1

        # Short entry conditions
        dataframe.loc[
            (
                # HMA crossover: fast crosses below slow
                (qtpylib.crossed_below(dataframe['fast_hma'], dataframe['slow_hma']))
                &
                # Curvature condition: acceleration below negative threshold
                (dataframe['curvature'] < -self.curv_thresh.value)
                &
                # Volume confirmation
                (dataframe['volume'] > dataframe['volume_sma'])
                &
                # Make sure we have valid data
                (dataframe['fast_hma'].notna())
                & (dataframe['slow_hma'].notna())
                & (dataframe['curvature'].notna())
                & (dataframe['atr'].notna())
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the exit signal for the given dataframe
        """
        
        # Long exit conditions
        dataframe.loc[
            (
                # HMA crossover: fast crosses below slow
                (qtpylib.crossed_below(dataframe['fast_hma'], dataframe['slow_hma']))
                |
                # Curvature reversal
                (dataframe['curvature'] < -self.curv_thresh.value)
            ),
            'exit_long'] = 1

        # Short exit conditions  
        dataframe.loc[
            (
                # HMA crossover: fast crosses above slow
                (qtpylib.crossed_above(dataframe['fast_hma'], dataframe['slow_hma']))
                |
                # Curvature reversal
                (dataframe['curvature'] > self.curv_thresh.value)
            ),
            'exit_short'] = 1

        return dataframe

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str | None,
        side: str,
        **kwargs,
    ) -> float:
        """
        Customize leverage for each new trade. This method is only called in futures mode.
        """
        return min(self.leverage_multiplier.value, max_leverage)

    def custom_stoploss(
        self,
        pair: str,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        after_fill: bool,
        **kwargs,
    ) -> Optional[float]:
        """
        Custom stoploss logic using ATR-based trailing stop
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        if last_candle['atr'] is None or pd.isna(last_candle['atr']):
            return None
            
        atr_value = last_candle['atr']
        trail_distance = atr_value * self.trail_mult.value
        
        if trade.is_short:
            # For short trades, stoploss is above entry
            new_stoploss = (current_rate + trail_distance) / trade.open_rate - 1
        else:
            # For long trades, stoploss is below entry  
            new_stoploss = (current_rate - trail_distance) / trade.open_rate - 1
            
        # Only update if the new stoploss is better (closer to current price)
        if trade.stop_loss_pct is None:
            return new_stoploss
        elif trade.is_short and new_stoploss < trade.stop_loss_pct:
            return new_stoploss
        elif not trade.is_short and new_stoploss > trade.stop_loss_pct:
            return new_stoploss
            
        return None
