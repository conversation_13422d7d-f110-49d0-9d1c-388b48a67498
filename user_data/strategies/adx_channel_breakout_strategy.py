# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import pandas as pd
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class AdxChannelBreakoutStrategy(IStrategy):
    """
    ADX通道突破策略
    
    策略逻辑：
    1. 计算ADX指标及其变化率
    2. 当ADX变化率转弱或由负转正时生成通道
    3. 通道基于收盘价±平均波幅的一半构建
    4. 突破上轨做多，跌破下轨做空
    5. 使用动态追踪止损管理持仓
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True

    # 杠杆参数
    leverage_optimize = IntParameter(2, 18, default=5, space="buy", optimize=True)
    
    # ROI表
    minimal_roi = {
        "0": 0.10,      # 10%止盈
        "60": 0.05,     # 1小时后5%止盈
        "120": 0.03,    # 2小时后3%止盈
        "240": 0.015,   # 4小时后1.5%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.08  # 8%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.035
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 50

    # 策略参数
    adx_len = IntParameter(10, 18, default=14, space="buy", optimize=True)      # ADX指标计算周期
    avg_len = IntParameter(2, 6, default=4, space="buy", optimize=True)         # ADX变化率平均周期
    ch = IntParameter(6, 12, default=8, space="buy", optimize=True)             # 通道保持周期
    range_period = IntParameter(3, 6, default=4, space="buy", optimize=True)    # 波幅计算周期
    
    # 止损参数
    stop_lookback = IntParameter(4, 8, default=6, space="sell", optimize=True)   # 止损回看周期
    trail_factor = DecimalParameter(0.2, 0.5, default=0.33, space="sell", optimize=True)  # 追踪止损因子

    # 出场参数
    exit_adx_threshold = IntParameter(15, 35, default=25, space="sell", optimize=True)  # 出场ADX阈值
    exit_rsi_high = IntParameter(65, 85, default=75, space="sell", optimize=True)       # 出场RSI高位
    exit_rsi_low = IntParameter(15, 35, default=25, space="sell", optimize=True)        # 出场RSI低位
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算ADX指标
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_len.value)

        # 计算RSI指标
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)

        # 计算ADX变化值
        dataframe['delta_adx'] = dataframe['adx'] - dataframe['adx'].shift(1)
        
        # 计算ADX变化率的加权平均
        dataframe['delta_avg'] = ta.WMA(dataframe['delta_adx'], timeperiod=self.avg_len.value)
        
        # 计算平均波幅
        dataframe['avg_range'] = ta.SMA(dataframe['high'] - dataframe['low'], timeperiod=self.range_period.value)
        
        # 通道生成条件：ADX变化率转弱或由负转正
        dataframe['channel_trigger'] = (
            (dataframe['delta_avg'] < 0) |
            (qtpylib.crossed_above(dataframe['delta_avg'], 0))
        )
        
        # 初始化通道变量
        dataframe['up_ch'] = np.nan
        dataframe['dn_ch'] = np.nan
        dataframe['channel_bar'] = np.nan
        
        # 计算通道上下轨
        for i in range(1, len(dataframe)):
            if dataframe['channel_trigger'].iloc[i] and not pd.isna(dataframe['avg_range'].iloc[i]):
                # 生成新通道
                dataframe.loc[dataframe.index[i], 'up_ch'] = (
                    dataframe['close'].iloc[i] + dataframe['avg_range'].iloc[i] / 2
                )
                dataframe.loc[dataframe.index[i], 'dn_ch'] = (
                    dataframe['close'].iloc[i] - dataframe['avg_range'].iloc[i] / 2
                )
                dataframe.loc[dataframe.index[i], 'channel_bar'] = i
            else:
                # 保持前一个通道值
                if i > 0:
                    dataframe.loc[dataframe.index[i], 'up_ch'] = dataframe['up_ch'].iloc[i-1]
                    dataframe.loc[dataframe.index[i], 'dn_ch'] = dataframe['dn_ch'].iloc[i-1]
                    dataframe.loc[dataframe.index[i], 'channel_bar'] = dataframe['channel_bar'].iloc[i-1]
        
        # 前向填充通道值
        dataframe['up_ch'] = dataframe['up_ch'].ffill()
        dataframe['dn_ch'] = dataframe['dn_ch'].ffill()
        dataframe['channel_bar'] = dataframe['channel_bar'].ffill()
        
        # 计算通道有效性（生成后CH根K线内有效）
        dataframe['channel_valid'] = (
            (dataframe.index - dataframe['channel_bar']) < self.ch.value
        ) & (~pd.isna(dataframe['channel_bar']))
        
        # 突破信号
        dataframe['breakout_long'] = (
            (dataframe['close'] > dataframe['up_ch']) &
            (dataframe['channel_valid'])
        )
        
        dataframe['breakout_short'] = (
            (dataframe['close'] < dataframe['dn_ch']) &
            (dataframe['channel_valid'])
        )
        
        # 计算止损位
        dataframe['lowest_low'] = ta.MIN(dataframe['low'], timeperiod=self.stop_lookback.value)
        dataframe['highest_high'] = ta.MAX(dataframe['high'], timeperiod=self.stop_lookback.value)
        
        # 动态止损价格
        dataframe['long_stop'] = dataframe['low'] - dataframe['avg_range']
        dataframe['short_stop'] = dataframe['high'] + dataframe['avg_range']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：突破上轨
        dataframe.loc[
            (
                (dataframe['breakout_long']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：跌破下轨
        dataframe.loc[
            (
                (dataframe['breakout_short']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """

        # 多头出场：RSI超买或ADX走弱
        dataframe.loc[
            (
                (dataframe['rsi'] > self.exit_rsi_high.value) |
                (dataframe['adx'] < self.exit_adx_threshold.value) |
                (dataframe['low'] <= dataframe['lowest_low'])
            ),
            'exit_long'] = 1

        # 空头出场：RSI超卖或ADX走弱
        dataframe.loc[
            (
                (dataframe['rsi'] < self.exit_rsi_low.value) |
                (dataframe['adx'] < self.exit_adx_threshold.value) |
                (dataframe['high'] >= dataframe['highest_high'])
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义动态追踪止损
        """
        
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        if trade.is_short:
            # 空头动态止损
            if hasattr(trade, 'stop_price'):
                # 更新止损价格（向下调整）
                new_stop = trade.stop_price - (trade.stop_price - current_candle['high']) * self.trail_factor.value
                trade.stop_price = min(trade.stop_price, new_stop)
                return (trade.stop_price - current_rate) / current_rate
            else:
                # 初始止损价格
                trade.stop_price = current_candle['short_stop']
                return (trade.stop_price - current_rate) / current_rate
        else:
            # 多头动态止损
            if hasattr(trade, 'stop_price'):
                # 更新止损价格（向上调整）
                new_stop = trade.stop_price + (current_candle['low'] - trade.stop_price) * self.trail_factor.value
                trade.stop_price = max(trade.stop_price, new_stop)
                return (trade.stop_price - current_rate) / current_rate
            else:
                # 初始止损价格
                trade.stop_price = current_candle['long_stop']
                return (trade.stop_price - current_rate) / current_rate
        
        return self.stoploss

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str | None,
                 side: str, **kwargs) -> float:
        """
        自定义杠杆设置
        """
        return self.leverage_optimize.value
