# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class ADXDynamicStopStrategy(IStrategy):
    """
    ADX动态止损突破策略
    
    策略逻辑：
    1. 使用ADX指标判断趋势强度
    2. 根据ADX强度动态调整止损位置
    3. ADX越强，使用越小周期的高低点作为止损
    4. 支持双向交易
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.10,      # 10%止盈
        "60": 0.05,     # 1小时后5%止盈
        "120": 0.03,    # 2小时后3%止盈
        "240": 0.02,    # 4小时后2%止盈
        "480": 0.01,    # 8小时后1%止盈
        "960": 0        # 16小时后0%止盈
    }

    # 止损设置
    stoploss = -0.08  # 8%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.03
    trailing_stop_positive_offset = 0.05
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 100

    # 策略参数
    adx_length = IntParameter(10, 20, default=13, space="buy", optimize=True)
    adx_base_level = IntParameter(10, 20, default=13, space="buy", optimize=True)
    adx_increment = IntParameter(5, 15, default=13, space="buy", optimize=True)
    
    # 卖出止损周期参数
    sell_stop_length1 = IntParameter(6, 12, default=8, space="sell", optimize=True)   # 最大周期
    sell_stop_length2 = IntParameter(4, 8, default=5, space="sell", optimize=True)    # 第二大周期
    sell_stop_length3 = IntParameter(2, 5, default=3, space="sell", optimize=True)    # 第三大周期
    sell_stop_length4 = IntParameter(1, 3, default=1, space="sell", optimize=True)    # 最小周期
    
    # 买入止损周期参数
    buy_stop_length1 = IntParameter(10, 16, default=13, space="buy", optimize=True)   # 最大周期
    buy_stop_length2 = IntParameter(6, 12, default=8, space="buy", optimize=True)     # 第二大周期
    buy_stop_length3 = IntParameter(3, 8, default=5, space="buy", optimize=True)      # 第三大周期
    buy_stop_length4 = IntParameter(1, 4, default=2, space="buy", optimize=True)      # 最小周期
    
    # 缓冲参数
    buffer_points = DecimalParameter(0.001, 0.01, default=0.005, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算ADX
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_length.value)
        
        # 计算ADX强度分级
        adx_level1 = self.adx_base_level.value
        adx_level2 = self.adx_base_level.value + self.adx_increment.value
        adx_level3 = self.adx_base_level.value + 2 * self.adx_increment.value
        adx_level4 = self.adx_base_level.value + 3 * self.adx_increment.value
        
        # ADX强度条件
        dataframe['adx_condition1'] = (dataframe['adx'] >= adx_level1) & (dataframe['adx'] < adx_level2)
        dataframe['adx_condition2'] = (dataframe['adx'] >= adx_level2) & (dataframe['adx'] < adx_level3)
        dataframe['adx_condition3'] = (dataframe['adx'] >= adx_level3) & (dataframe['adx'] < adx_level4)
        dataframe['adx_condition4'] = dataframe['adx'] >= adx_level4
        dataframe['adx_weak'] = dataframe['adx'] < adx_level1
        
        # 计算不同周期的卖出止损位（最低价减去缓冲）
        dataframe['sell_stop1'] = dataframe['low'].rolling(window=self.sell_stop_length1.value).min() - self.buffer_points.value
        dataframe['sell_stop2'] = dataframe['low'].rolling(window=self.sell_stop_length2.value).min() - self.buffer_points.value
        dataframe['sell_stop3'] = dataframe['low'].rolling(window=self.sell_stop_length3.value).min() - self.buffer_points.value
        dataframe['sell_stop4'] = dataframe['low'].rolling(window=self.sell_stop_length4.value).min() - self.buffer_points.value
        
        # 计算不同周期的买入止损位（最高价加上缓冲）
        dataframe['buy_stop1'] = dataframe['high'].rolling(window=self.buy_stop_length1.value).max() + self.buffer_points.value
        dataframe['buy_stop2'] = dataframe['high'].rolling(window=self.buy_stop_length2.value).max() + self.buffer_points.value
        dataframe['buy_stop3'] = dataframe['high'].rolling(window=self.buy_stop_length3.value).max() + self.buffer_points.value
        dataframe['buy_stop4'] = dataframe['high'].rolling(window=self.buy_stop_length4.value).max() + self.buffer_points.value
        
        # 根据ADX强度选择对应的止损位
        dataframe['dynamic_sell_stop'] = np.nan
        dataframe['dynamic_buy_stop'] = np.nan
        
        # 卖出止损位选择
        dataframe.loc[dataframe['adx_weak'] | dataframe['adx_condition1'], 'dynamic_sell_stop'] = dataframe['sell_stop1']
        dataframe.loc[dataframe['adx_condition2'], 'dynamic_sell_stop'] = dataframe['sell_stop2']
        dataframe.loc[dataframe['adx_condition3'], 'dynamic_sell_stop'] = dataframe['sell_stop3']
        dataframe.loc[dataframe['adx_condition4'], 'dynamic_sell_stop'] = dataframe['sell_stop4']
        
        # 买入止损位选择
        dataframe.loc[dataframe['adx_weak'] | dataframe['adx_condition1'], 'dynamic_buy_stop'] = dataframe['buy_stop1']
        dataframe.loc[dataframe['adx_condition2'], 'dynamic_buy_stop'] = dataframe['buy_stop2']
        dataframe.loc[dataframe['adx_condition3'], 'dynamic_buy_stop'] = dataframe['buy_stop3']
        dataframe.loc[dataframe['adx_condition4'], 'dynamic_buy_stop'] = dataframe['buy_stop4']
        
        # 入场条件：价格突破动态止损位
        dataframe['long_entry_signal'] = dataframe['high'] >= dataframe['dynamic_buy_stop']
        dataframe['short_entry_signal'] = dataframe['low'] <= dataframe['dynamic_sell_stop']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：价格突破买入止损位
        dataframe.loc[
            (
                (dataframe['long_entry_signal']) &
                (dataframe['adx'] > 15) &  # ADX过滤，确保有一定趋势
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：价格跌破卖出止损位
        dataframe.loc[
            (
                (dataframe['short_entry_signal']) &
                (dataframe['adx'] > 15) &  # ADX过滤，确保有一定趋势
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：ADX下降或价格跌破相应止损位
        dataframe.loc[
            (
                (dataframe['adx'] < 12) |  # ADX过低，趋势减弱
                (dataframe['low'] <= dataframe['dynamic_sell_stop'])  # 跌破动态止损
            ),
            'exit_long'] = 1

        # 空头出场：ADX下降或价格突破相应止损位
        dataframe.loc[
            (
                (dataframe['adx'] < 12) |  # ADX过低，趋势减弱
                (dataframe['high'] >= dataframe['dynamic_buy_stop'])  # 突破动态止损
            ),
            'exit_short'] = 1

        return dataframe
