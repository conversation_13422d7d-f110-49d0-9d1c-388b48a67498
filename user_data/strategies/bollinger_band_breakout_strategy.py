# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class BollingerBandBreakoutStrategy(IStrategy):
    """
    布林带通道突破策略
    
    策略逻辑：
    1. 使用35周期均线作为基准线
    2. 以±2倍标准差构建布林带通道
    3. 突破上轨做多，跌破下轨做空
    4. 持仓期间价格回归均线时平仓
    5. 简单有效的趋势突破策略
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.08,      # 8%止盈
        "60": 0.04,     # 1小时后4%止盈
        "120": 0.025,   # 2小时后2.5%止盈
        "240": 0.01,    # 4小时后1%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.06  # 6%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.03
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 50

    # 策略参数
    bb_period = IntParameter(25, 45, default=35, space="buy", optimize=True)        # 布林带周期
    bb_std = DecimalParameter(1.5, 2.5, default=2.0, space="buy", optimize=True)   # 布林带标准差倍数
    volume_period = IntParameter(15, 25, default=20, space="buy", optimize=True)    # 成交量均线周期

    # 过滤器参数
    volume_filter = BooleanParameter(default=True, space="buy", optimize=True)      # 成交量过滤器
    trend_filter = BooleanParameter(default=True, space="buy", optimize=True)       # 趋势过滤器

    # 杠杆参数
    leverage_optimize = IntParameter(2, 18, default=5, space="buy", optimize=True)

    # 出场参数
    exit_bb_std = DecimalParameter(0.5, 1.5, default=1.0, space="sell", optimize=True)  # 出场布林带标准差
    profit_target = DecimalParameter(0.02, 0.08, default=0.05, space="sell", optimize=True)  # 利润目标
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算布林带指标
        bollinger = qtpylib.bollinger_bands(
            dataframe['close'], 
            window=self.bb_period.value, 
            stds=self.bb_std.value
        )
        dataframe['bb_lower'] = bollinger['lower']
        dataframe['bb_middle'] = bollinger['mid']
        dataframe['bb_upper'] = bollinger['upper']
        
        # 也可以使用TA-Lib计算布林带
        dataframe['bb_upper_ta'], dataframe['bb_middle_ta'], dataframe['bb_lower_ta'] = ta.BBANDS(
            dataframe['close'], 
            timeperiod=self.bb_period.value, 
            nbdevup=self.bb_std.value, 
            nbdevdn=self.bb_std.value, 
            matype=0
        )
        
        # 使用TA-Lib的结果（更准确）
        dataframe['bb_upper'] = dataframe['bb_upper_ta']
        dataframe['bb_middle'] = dataframe['bb_middle_ta']
        dataframe['bb_lower'] = dataframe['bb_lower_ta']
        
        # 计算布林带宽度（衡量波动性）
        dataframe['bb_width'] = (dataframe['bb_upper'] - dataframe['bb_lower']) / dataframe['bb_middle']
        
        # 计算价格在布林带中的位置（%B指标）
        dataframe['bb_percent'] = (dataframe['close'] - dataframe['bb_lower']) / (dataframe['bb_upper'] - dataframe['bb_lower'])
        
        # 成交量过滤器
        if self.volume_filter.value:
            dataframe['volume_ma'] = ta.SMA(dataframe['volume'], timeperiod=self.volume_period.value)
            dataframe['high_volume'] = dataframe['volume'] > dataframe['volume_ma']
        else:
            dataframe['high_volume'] = True
        
        # 趋势过滤器
        if self.trend_filter.value:
            # 使用布林带中线的斜率判断趋势
            dataframe['bb_middle_slope'] = (dataframe['bb_middle'] - dataframe['bb_middle'].shift(5)) / 5
            dataframe['uptrend'] = dataframe['bb_middle_slope'] > 0
            dataframe['downtrend'] = dataframe['bb_middle_slope'] < 0
        else:
            dataframe['uptrend'] = True
            dataframe['downtrend'] = True
        
        # 突破信号
        dataframe['breakout_upper'] = dataframe['close'] > dataframe['bb_upper']
        dataframe['breakout_lower'] = dataframe['close'] < dataframe['bb_lower']
        
        # 回归信号
        dataframe['return_to_middle_from_upper'] = (
            (dataframe['close'] < dataframe['bb_middle']) &
            (dataframe['close'].shift(1) >= dataframe['bb_middle'].shift(1))
        )
        
        dataframe['return_to_middle_from_lower'] = (
            (dataframe['close'] > dataframe['bb_middle']) &
            (dataframe['close'].shift(1) <= dataframe['bb_middle'].shift(1))
        )
        
        # 入场信号
        dataframe['long_signal'] = (
            (dataframe['breakout_upper']) &
            (dataframe['uptrend']) &
            (dataframe['high_volume'])
        )
        
        dataframe['short_signal'] = (
            (dataframe['breakout_lower']) &
            (dataframe['downtrend']) &
            (dataframe['high_volume'])
        )
        
        # 出场信号
        dataframe['long_exit_signal'] = dataframe['return_to_middle_from_upper']
        dataframe['short_exit_signal'] = dataframe['return_to_middle_from_lower']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：收盘价突破布林带上轨
        dataframe.loc[
            (
                (dataframe['long_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：收盘价跌破布林带下轨
        dataframe.loc[
            (
                (dataframe['short_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：收盘价回落至布林带中线
        dataframe.loc[
            (
                (dataframe['long_exit_signal'])
            ),
            'exit_long'] = 1

        # 空头出场：收盘价回升至布林带中线
        dataframe.loc[
            (
                (dataframe['short_exit_signal'])
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义止损：使用布林带中线作为动态止损
        """
        
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        if trade.is_short:
            # 空头止损：布林带中线上方
            stop_price = current_candle['bb_middle'] * 1.01  # 中线上方1%
            if stop_price > 0:
                return (stop_price - current_rate) / current_rate
        else:
            # 多头止损：布林带中线下方
            stop_price = current_candle['bb_middle'] * 0.99  # 中线下方1%
            if stop_price > 0:
                return (stop_price - current_rate) / current_rate
        
        return self.stoploss

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str | None,
                 side: str, **kwargs) -> float:
        """
        自定义杠杆设置
        """
        return self.leverage_optimize.value
