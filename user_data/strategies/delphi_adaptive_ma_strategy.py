# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class DelphiAdaptiveMAStrategy(IStrategy):
    """
    Delphi自适应移动平均策略
    
    策略逻辑：
    1. 使用自适应移动平均线作为主要信号
    2. 价格穿越均线时产生交易信号
    3. 使用ATR计算动态止损和目标
    4. 支持双向交易
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表 - 使用动态目标，这里设置保守值
    minimal_roi = {
        "0": 0.12,      # 12%止盈
        "60": 0.06,     # 1小时后6%止盈
        "120": 0.04,    # 2小时后4%止盈
        "240": 0.02,    # 4小时后2%止盈
        "480": 0.01,    # 8小时后1%止盈
        "960": 0        # 16小时后0%止盈
    }

    # 止损设置 - 将使用动态止损
    stoploss = -0.08  # 8%止损作为备用

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.03
    trailing_stop_positive_offset = 0.05
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 150

    # 策略参数 - 对应ERDay模式的参数
    variable1 = DecimalParameter(1.5, 2.5, default=1.8, space="buy", optimize=True)  # ATR倍数1
    variable2 = DecimalParameter(0.8, 1.2, default=0.99, space="buy", optimize=True)  # ATR倍数2
    variable3 = DecimalParameter(0.1, 0.3, default=0.16, space="buy", optimize=True)  # 部分止盈倍数
    variable4 = DecimalParameter(2.5, 4.0, default=3.16, space="buy", optimize=True)  # 目标倍数
    variable5 = DecimalParameter(0.3, 0.6, default=0.42, space="buy", optimize=True)  # 止损倍数
    
    # ATR参数
    atr_long_period = IntParameter(30, 40, default=34, space="buy", optimize=True)
    atr_short_period = IntParameter(10, 16, default=13, space="buy", optimize=True)
    
    # 自适应移动平均参数
    ama_period = IntParameter(8, 15, default=10, space="buy", optimize=True)
    ama_fast = IntParameter(6, 12, default=8, space="buy", optimize=True)
    ama_slow = IntParameter(100, 140, default=120, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算ATR
        dataframe['atr_long'] = ta.ATR(dataframe, timeperiod=self.atr_long_period.value)
        dataframe['atr_short'] = ta.ATR(dataframe, timeperiod=self.atr_short_period.value)
        
        # 计算自适应移动平均线 (使用KAMA作为替代)
        dataframe['adaptive_ma'] = ta.KAMA(
            dataframe['close'], 
            timeperiod=self.ama_period.value
        )
        
        # 如果KAMA不可用，使用EMA作为备选
        if dataframe['adaptive_ma'].isna().all():
            dataframe['adaptive_ma'] = ta.EMA(dataframe['close'], timeperiod=self.ama_period.value)
        
        # 计算通道
        dataframe['upper_band'] = (
            dataframe['adaptive_ma'] + 
            dataframe['atr_short'] * self.variable1.value
        )
        dataframe['lower_band'] = (
            dataframe['adaptive_ma'] - 
            dataframe['atr_short'] * self.variable1.value
        )
        
        # 计算交易信号
        dataframe['close_cross_above_ma'] = qtpylib.crossed_above(dataframe['close'], dataframe['adaptive_ma'])
        dataframe['close_cross_below_ma'] = qtpylib.crossed_below(dataframe['close'], dataframe['adaptive_ma'])
        
        # 多头信号条件
        dataframe['long_signal'] = False
        dataframe['long_entry_price'] = np.nan
        
        # 空头信号条件
        dataframe['short_signal'] = False
        dataframe['short_entry_price'] = np.nan
        
        # 设置信号和入场价格
        for i in range(1, len(dataframe)):
            # 多头信号：收盘价上穿自适应均线
            if dataframe.iloc[i]['close_cross_above_ma']:
                dataframe.iloc[i, dataframe.columns.get_loc('long_signal')] = True
                dataframe.iloc[i, dataframe.columns.get_loc('long_entry_price')] = dataframe.iloc[i]['upper_band']
            
            # 空头信号：收盘价下穿自适应均线
            if dataframe.iloc[i]['close_cross_below_ma']:
                dataframe.iloc[i, dataframe.columns.get_loc('short_signal')] = True
                dataframe.iloc[i, dataframe.columns.get_loc('short_entry_price')] = dataframe.iloc[i]['lower_band']
        
        # 计算动态止损和目标
        # 多头止损和目标
        dataframe['long_stop_loss'] = (
            dataframe['adaptive_ma'] - 
            dataframe['atr_short'] * self.variable5.value
        )
        dataframe['long_target1'] = (
            dataframe['adaptive_ma'] + 
            dataframe['atr_short'] * self.variable3.value
        )
        dataframe['long_target2'] = (
            dataframe['adaptive_ma'] + 
            dataframe['atr_short'] * self.variable4.value
        )
        
        # 空头止损和目标
        dataframe['short_stop_loss'] = (
            dataframe['adaptive_ma'] + 
            dataframe['atr_short'] * self.variable5.value
        )
        dataframe['short_target1'] = (
            dataframe['adaptive_ma'] - 
            dataframe['atr_short'] * self.variable3.value
        )
        dataframe['short_target2'] = (
            dataframe['adaptive_ma'] - 
            dataframe['atr_short'] * self.variable4.value
        )
        
        # 入场条件
        dataframe['long_entry_condition'] = (
            (dataframe['long_signal']) &
            (dataframe['high'] >= dataframe['long_entry_price'])
        )
        
        dataframe['short_entry_condition'] = (
            (dataframe['short_signal']) &
            (dataframe['low'] <= dataframe['short_entry_price'])
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场
        dataframe.loc[
            (
                (dataframe['long_entry_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场
        dataframe.loc[
            (
                (dataframe['short_entry_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：价格下穿自适应均线或达到止损
        dataframe.loc[
            (
                (dataframe['close_cross_below_ma']) |
                (dataframe['low'] <= dataframe['long_stop_loss'])
            ),
            'exit_long'] = 1

        # 空头出场：价格上穿自适应均线或达到止损
        dataframe.loc[
            (
                (dataframe['close_cross_above_ma']) |
                (dataframe['high'] >= dataframe['short_stop_loss'])
            ),
            'exit_short'] = 1

        return dataframe
