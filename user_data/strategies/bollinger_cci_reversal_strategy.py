# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import pandas as pd
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class BollingerCciReversalStrategy(IStrategy):
    """
    布林带+CCI反转策略
    
    策略逻辑：
    1. 使用布林带识别价格是否超出正常交易范围
    2. 使用CCI指标确认超买超卖状态和反转信号
    3. 当价格跌破布林带下轨且CCI在超卖区域反转向上时买入
    4. 当价格突破布林带上轨且CCI在超买区域反转向下时卖出
    5. 使用动态止损管理风险
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.12,      # 12%止盈
        "60": 0.06,     # 1小时后6%止盈
        "120": 0.04,    # 2小时后4%止盈
        "240": 0.02,    # 4小时后2%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.08  # 8%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.025
    trailing_stop_positive_offset = 0.04
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 60

    # 策略参数
    bb_len = IntParameter(20, 35, default=27, space="buy", optimize=True)           # 布林带周期长度
    bb_stdv = DecimalParameter(0.4, 0.8, default=0.56, space="buy", optimize=True) # 布林带标准差倍数
    cci_len = IntParameter(25, 40, default=33, space="buy", optimize=True)          # CCI指标周期长度
    cci_avg_ln = IntParameter(6, 12, default=8, space="buy", optimize=True)         # CCI均线平滑周期
    up_band = DecimalParameter(150, 180, default=165, space="buy", optimize=True)   # CCI超买阈值
    down_band = DecimalParameter(170, 200, default=185, space="buy", optimize=True) # CCI超卖阈值
    
    # 摆动点检测参数
    swing_lookback_min = IntParameter(1, 2, default=1, space="buy", optimize=True)
    swing_lookback_max = IntParameter(2, 4, default=3, space="buy", optimize=True)
    
    # 止损参数
    stop_atr_multiplier = DecimalParameter(0.5, 1.0, default=0.67, space="buy", optimize=True)
    trail_factor = DecimalParameter(0.2, 0.4, default=0.3, space="buy", optimize=True)

    # 杠杆参数
    leverage_optimize = IntParameter(2, 18, default=5, space="buy", optimize=True)

    # 出场参数
    exit_cci_threshold = DecimalParameter(50, 100, default=75, space="sell", optimize=True)  # CCI出场阈值
    profit_target = DecimalParameter(0.02, 0.08, default=0.05, space="sell", optimize=True)  # 利润目标
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算布林带指标
        dataframe['bb_upper'], dataframe['bb_middle'], dataframe['bb_lower'] = ta.BBANDS(
            dataframe['close'], 
            timeperiod=self.bb_len.value, 
            nbdevup=self.bb_stdv.value, 
            nbdevdn=self.bb_stdv.value, 
            matype=0
        )
        
        # 计算CCI指标
        dataframe['cci'] = ta.CCI(dataframe, timeperiod=self.cci_len.value)
        
        # 计算CCI的指数移动平均
        dataframe['cci_avg'] = ta.EMA(dataframe['cci'], timeperiod=self.cci_avg_ln.value)
        
        # 计算价格在布林带中的相对位置
        dataframe['bar_position'] = np.where(
            dataframe['bb_upper'] != dataframe['bb_lower'],
            (dataframe['close'] - dataframe['bb_lower']) / (dataframe['bb_upper'] - dataframe['bb_lower']),
            0.5
        )
        
        # 布林带状态判断
        dataframe['bb_status'] = 0
        
        # 价格突破布林带边界时改变状态
        dataframe.loc[dataframe['low'] < dataframe['bb_lower'], 'bb_status'] = -1  # 跌破下轨进入卖方环境
        dataframe.loc[dataframe['high'] > dataframe['bb_upper'], 'bb_status'] = 1   # 突破上轨进入买方环境
        
        # 在中线附近时维持观望
        dataframe.loc[
            (dataframe['bar_position'].shift(1) <= 0.5) & (dataframe['bar_position'] > 0.5), 
            'bb_status'
        ] = 0  # 价格上穿中线
        dataframe.loc[
            (dataframe['bar_position'].shift(1) >= 0.5) & (dataframe['bar_position'] < 0.5), 
            'bb_status'
        ] = 0  # 价格下穿中线
        
        # 前向填充状态
        dataframe['bb_status'] = dataframe['bb_status'].replace(0, np.nan).fillna(method='ffill').fillna(0)
        
        # CCI反转信号检测
        # 多头信号：CCI在超卖区域(-185以下)出现低点反转
        dataframe['cci_turns_up'] = (
            (dataframe['cci_avg'] < -self.down_band.value) &
            (dataframe['cci_avg'] > dataframe['cci_avg'].shift(1)) &
            (dataframe['cci_avg'].shift(1) > dataframe['cci_avg'].shift(2))
        )
        
        # 空头信号：CCI在超买区域(165以上)出现高点反转
        dataframe['cci_turns_down'] = (
            (dataframe['cci_avg'] > self.up_band.value) &
            (dataframe['cci_avg'] < dataframe['cci_avg'].shift(1)) &
            (dataframe['cci_avg'].shift(1) < dataframe['cci_avg'].shift(2))
        )
        
        # 计算入场价格
        dataframe['long_entry_price'] = ta.MAX(dataframe['high'], timeperiod=3)
        dataframe['short_entry_price'] = ta.MIN(dataframe['low'], timeperiod=3)
        
        # 计算ATR用于止损
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=4)
        
        # 入场信号
        dataframe['long_signal'] = (
            (dataframe['bb_status'] == -1) &
            (dataframe['cci_turns_up'])
        )
        
        dataframe['short_signal'] = (
            (dataframe['bb_status'] == 1) &
            (dataframe['cci_turns_down'])
        )
        
        # 记录信号触发的K线
        dataframe['long_signal_bar'] = np.where(dataframe['long_signal'], dataframe.index, np.nan)
        dataframe['short_signal_bar'] = np.where(dataframe['short_signal'], dataframe.index, np.nan)
        
        # 前向填充信号K线
        dataframe['long_signal_bar'] = dataframe['long_signal_bar'].fillna(method='ffill')
        dataframe['short_signal_bar'] = dataframe['short_signal_bar'].fillna(method='ffill')
        
        # 信号有效期（5根K线内）
        dataframe['long_signal_valid'] = (
            (dataframe.index - dataframe['long_signal_bar']) < 5
        ) & (~pd.isna(dataframe['long_signal_bar']))
        
        dataframe['short_signal_valid'] = (
            (dataframe.index - dataframe['short_signal_bar']) < 5
        ) & (~pd.isna(dataframe['short_signal_bar']))

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：布林带下轨突破+CCI超卖反转
        dataframe.loc[
            (
                (dataframe['long_signal_valid']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：布林带上轨突破+CCI超买反转
        dataframe.loc[
            (
                (dataframe['short_signal_valid']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号 - 主要依靠动态止损，这里设置基本的反向信号
        """
        
        # 多头出场：CCI转为超买
        dataframe.loc[
            (
                (dataframe['cci_avg'] > self.up_band.value)
            ),
            'exit_long'] = 1

        # 空头出场：CCI转为超卖
        dataframe.loc[
            (
                (dataframe['cci_avg'] < -self.down_band.value)
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义动态止损
        """
        
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        if trade.is_short:
            # 空头动态止损逻辑
            if not hasattr(trade, 'stop_price'):
                # 初始止损：当前最高价+2/3近期平均波幅
                trade.stop_price = current_candle['high'] + current_candle['atr'] * self.stop_atr_multiplier.value
            else:
                # 移动止损：每根K线向下调整30%的止损位
                new_stop = trade.stop_price - (trade.stop_price - current_candle['high']) * self.trail_factor.value
                trade.stop_price = min(trade.stop_price, new_stop)
            
            return (trade.stop_price - current_rate) / current_rate
        else:
            # 多头动态止损逻辑
            if not hasattr(trade, 'stop_price'):
                # 初始止损：当前最低价-2/3近期平均波幅
                trade.stop_price = current_candle['low'] - current_candle['atr'] * self.stop_atr_multiplier.value
            else:
                # 移动止损：每根K线向上调整30%的止损位
                new_stop = trade.stop_price + (current_candle['low'] - trade.stop_price) * self.trail_factor.value
                trade.stop_price = max(trade.stop_price, new_stop)
            
            return (trade.stop_price - current_rate) / current_rate
        
        return self.stoploss

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str | None,
                 side: str, **kwargs) -> float:
        """
        自定义杠杆设置
        """
        return self.leverage_optimize.value
