# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class SwingPointBreakoutStrategy(IStrategy):
    """
    摆动点突破交易策略
    
    策略逻辑：
    1. 识别摆动高低点
    2. 在突破摆动点时入场
    3. 使用EMA作为趋势过滤器
    4. 包含盈亏平衡止损和追踪止损
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.12,      # 12%止盈
        "60": 0.06,     # 1小时后6%止盈
        "120": 0.04,    # 2小时后4%止盈
        "240": 0.02,    # 4小时后2%止盈
        "480": 0.01,    # 8小时后1%止盈
        "960": 0        # 16小时后0%止盈
    }

    # 止损设置
    stoploss = -0.08  # 8%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.03
    trailing_stop_positive_offset = 0.05
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 100

    # 策略参数
    hilo_len = IntParameter(6, 12, default=8, space="buy", optimize=True)
    atr_len = IntParameter(8, 15, default=10, space="buy", optimize=True)
    setup_int = IntParameter(3, 6, default=4, space="buy", optimize=True)
    xavg_len = IntParameter(7, 12, default=9, space="buy", optimize=True)
    setup_len = IntParameter(20, 30, default=24, space="buy", optimize=True)
    risk_mult = DecimalParameter(1.5, 3.0, default=2.0, space="buy", optimize=True)
    hilo_stop = IntParameter(40, 50, default=44, space="sell", optimize=True)
    profit_mult = DecimalParameter(1.5, 3.0, default=2.0, space="sell", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算ATR
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_len.value)
        
        # 计算EMA
        dataframe['ema'] = ta.EMA(dataframe['close'], timeperiod=self.xavg_len.value)
        
        # 识别摆动高低点
        dataframe['swing_low'] = False
        dataframe['swing_high'] = False
        
        # 简化的摆动点识别
        for i in range(2, len(dataframe) - 2):
            # 摆动低点：当前低点低于前后2根K线的低点
            if (dataframe.iloc[i]['low'] < dataframe.iloc[i-1]['low'] and
                dataframe.iloc[i]['low'] < dataframe.iloc[i-2]['low'] and
                dataframe.iloc[i]['low'] < dataframe.iloc[i+1]['low'] and
                dataframe.iloc[i]['low'] < dataframe.iloc[i+2]['low']):
                dataframe.iloc[i, dataframe.columns.get_loc('swing_low')] = True
            
            # 摆动高点：当前高点高于前后2根K线的高点
            if (dataframe.iloc[i]['high'] > dataframe.iloc[i-1]['high'] and
                dataframe.iloc[i]['high'] > dataframe.iloc[i-2]['high'] and
                dataframe.iloc[i]['high'] > dataframe.iloc[i+1]['high'] and
                dataframe.iloc[i]['high'] > dataframe.iloc[i+2]['high']):
                dataframe.iloc[i, dataframe.columns.get_loc('swing_high')] = True
        
        # 计算周期内最高最低价
        dataframe['period_high'] = dataframe['high'].rolling(window=self.hilo_len.value).max()
        dataframe['period_low'] = dataframe['low'].rolling(window=self.hilo_len.value).min()
        
        # 检测是否为周期内极值
        dataframe['is_period_high'] = dataframe['high'] == dataframe['period_high'].shift(1)
        dataframe['is_period_low'] = dataframe['low'] == dataframe['period_low'].shift(1)
        
        # 真实波幅条件
        dataframe['tr_condition'] = dataframe['atr'] > dataframe['atr'].rolling(window=self.atr_len.value).mean()
        
        # 买入设置条件
        dataframe['buy_setup_condition'] = (
            (dataframe['swing_low']) &
            (dataframe['is_period_low']) &
            (dataframe['tr_condition']) &
            (dataframe['close'] > np.maximum.reduce([
                dataframe['close'].shift(1),
                dataframe['open'],
                dataframe['low'] + (2 * dataframe['atr'] / 3)
            ]))
        )
        
        # 卖出设置条件
        dataframe['sell_setup_condition'] = (
            (dataframe['swing_high']) &
            (dataframe['is_period_high']) &
            (dataframe['tr_condition']) &
            (dataframe['close'] < np.minimum.reduce([
                dataframe['close'].shift(1),
                dataframe['open'],
                dataframe['high'] - (dataframe['atr'] / 3)
            ]))
        )
        
        # 设置买入和卖出价格
        dataframe['buy_setup_high'] = np.nan
        dataframe['buy_setup_low'] = np.nan
        dataframe['sell_setup_high'] = np.nan
        dataframe['sell_setup_low'] = np.nan
        dataframe['buy_setup_count'] = 100
        dataframe['sell_setup_count'] = 100
        
        # 当满足买入设置条件时记录价格
        mask = dataframe['buy_setup_condition']
        dataframe.loc[mask, 'buy_setup_high'] = dataframe.loc[mask, 'high']
        dataframe.loc[mask, 'buy_setup_low'] = dataframe.loc[mask, 'low']
        dataframe.loc[mask, 'buy_setup_count'] = 0
        
        # 当满足卖出设置条件时记录价格
        mask = dataframe['sell_setup_condition']
        dataframe.loc[mask, 'sell_setup_high'] = dataframe.loc[mask, 'high']
        dataframe.loc[mask, 'sell_setup_low'] = dataframe.loc[mask, 'low']
        dataframe.loc[mask, 'sell_setup_count'] = 0
        
        # 向前填充设置价格
        dataframe['buy_setup_high'] = dataframe['buy_setup_high'].fillna(method='ffill')
        dataframe['buy_setup_low'] = dataframe['buy_setup_low'].fillna(method='ffill')
        dataframe['sell_setup_high'] = dataframe['sell_setup_high'].fillna(method='ffill')
        dataframe['sell_setup_low'] = dataframe['sell_setup_low'].fillna(method='ffill')
        
        # 计算设置计数器
        for i in range(1, len(dataframe)):
            if not dataframe.iloc[i]['buy_setup_condition']:
                if dataframe.iloc[i-1]['buy_setup_count'] < self.setup_len.value:
                    dataframe.iloc[i, dataframe.columns.get_loc('buy_setup_count')] = dataframe.iloc[i-1]['buy_setup_count'] + 1
                else:
                    dataframe.iloc[i, dataframe.columns.get_loc('buy_setup_count')] = 100
            
            if not dataframe.iloc[i]['sell_setup_condition']:
                if dataframe.iloc[i-1]['sell_setup_count'] < self.setup_len.value:
                    dataframe.iloc[i, dataframe.columns.get_loc('sell_setup_count')] = dataframe.iloc[i-1]['sell_setup_count'] + 1
                else:
                    dataframe.iloc[i, dataframe.columns.get_loc('sell_setup_count')] = 100
        
        # 入场条件
        dataframe['long_entry_condition'] = (
            (dataframe['buy_setup_count'] <= self.setup_len.value) &
            (dataframe['buy_setup_high'] + 0.0001 > dataframe['ema']) &
            (dataframe['buy_setup_high'].notna())
        )
        
        dataframe['short_entry_condition'] = (
            (dataframe['sell_setup_count'] <= self.setup_len.value) &
            (dataframe['sell_setup_low'] - 0.0001 < dataframe['ema']) &
            (dataframe['sell_setup_low'].notna())
        )
        
        # 系统退出条件
        dataframe['long_exit_high'] = dataframe['high'].rolling(window=self.hilo_stop.value).max()
        dataframe['short_exit_low'] = dataframe['low'].rolling(window=self.hilo_stop.value).min()

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场
        dataframe.loc[
            (
                (dataframe['long_entry_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场
        dataframe.loc[
            (
                (dataframe['short_entry_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：达到系统退出高点
        dataframe.loc[
            (
                (dataframe['high'] >= dataframe['long_exit_high']) &
                (dataframe['long_exit_high'].notna())
            ),
            'exit_long'] = 1

        # 空头出场：达到系统退出低点
        dataframe.loc[
            (
                (dataframe['low'] <= dataframe['short_exit_low']) &
                (dataframe['short_exit_low'].notna())
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义止损逻辑：盈亏平衡止损
        """
        
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 盈亏平衡止损逻辑
        if current_profit >= 0.02:  # 当盈利超过2%时
            if trade.is_short:
                # 空头盈亏平衡止损
                return -(current_profit - 0.01)  # 锁定1%利润
            else:
                # 多头盈亏平衡止损
                return -(current_profit - 0.01)  # 锁定1%利润
        
        return self.stoploss
