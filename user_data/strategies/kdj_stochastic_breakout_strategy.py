# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import pandas as pd
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class KDJStochasticBreakoutStrategy(IStrategy):
    """
    KDJ随机指标突破策略
    
    策略逻辑：
    1. 使用12周期慢速随机指标（KDJ）
    2. 超买区域（>70）K值下穿D值时做空
    3. 超卖区域（<30）K值上穿D值时做多
    4. 设置6个周期的信号有效期
    5. 使用保护性止损
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.08,      # 8%止盈
        "60": 0.04,     # 1小时后4%止盈
        "120": 0.025,   # 2小时后2.5%止盈
        "240": 0.01,    # 4小时后1%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.06  # 6%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.025
    trailing_stop_positive_offset = 0.04
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 50

    # 策略参数
    kdj_length = IntParameter(10, 16, default=12, space="buy", optimize=True)
    overbought = IntParameter(65, 80, default=70, space="buy", optimize=True)
    oversold = IntParameter(20, 35, default=30, space="buy", optimize=True)
    setup_len = IntParameter(4, 8, default=6, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算KDJ指标
        stoch = ta.STOCH(dataframe, 
                        fastk_period=self.kdj_length.value,
                        slowk_period=3,
                        slowd_period=3)
        
        dataframe['k_val'] = stoch['slowk']
        dataframe['d_val'] = stoch['slowd']
        
        # 计算金叉死叉
        dataframe['k_cross_above_d'] = qtpylib.crossed_above(dataframe['k_val'], dataframe['d_val'])
        dataframe['k_cross_below_d'] = qtpylib.crossed_below(dataframe['k_val'], dataframe['d_val'])
        
        # 超买超卖区域判断
        dataframe['k_oversold'] = dataframe['k_val'] < self.oversold.value
        dataframe['k_overbought'] = dataframe['k_val'] > self.overbought.value
        
        # 买入设置信号：K值超卖且金叉
        dataframe['buy_setup_signal'] = (
            (dataframe['k_oversold']) &
            (dataframe['k_cross_above_d'])
        )
        
        # 卖出设置信号：K值超买且死叉
        dataframe['sell_setup_signal'] = (
            (dataframe['k_overbought']) &
            (dataframe['k_cross_below_d'])
        )
        
        # 初始化设置价格和计数器
        dataframe['buy_setup'] = np.nan
        dataframe['sell_setup'] = np.nan
        dataframe['buy_stop'] = np.nan
        dataframe['sell_stop'] = np.nan
        dataframe['buy_period'] = 99
        dataframe['sell_period'] = 99
        
        # 当出现买入设置信号时
        buy_mask = dataframe['buy_setup_signal']
        dataframe.loc[buy_mask, 'buy_setup'] = dataframe.loc[buy_mask, 'high'].rolling(window=2).max()
        dataframe.loc[buy_mask, 'buy_stop'] = dataframe.loc[buy_mask, 'low']
        dataframe.loc[buy_mask, 'buy_period'] = 0
        
        # 当出现卖出设置信号时
        sell_mask = dataframe['sell_setup_signal']
        dataframe.loc[sell_mask, 'sell_setup'] = dataframe.loc[sell_mask, 'low'].rolling(window=2).min()
        dataframe.loc[sell_mask, 'sell_stop'] = dataframe.loc[sell_mask, 'high']
        dataframe.loc[sell_mask, 'sell_period'] = 0
        
        # 向前填充设置价格
        dataframe['buy_setup'] = dataframe['buy_setup'].ffill()
        dataframe['sell_setup'] = dataframe['sell_setup'].ffill()
        dataframe['buy_stop'] = dataframe['buy_stop'].ffill()
        dataframe['sell_stop'] = dataframe['sell_stop'].ffill()
        
        # 计算信号有效期计数器
        for i in range(1, len(dataframe)):
            # 买入信号计数器
            if not dataframe.iloc[i]['buy_setup_signal']:
                if dataframe.iloc[i-1]['buy_period'] < self.setup_len.value:
                    dataframe.iloc[i, dataframe.columns.get_loc('buy_period')] = dataframe.iloc[i-1]['buy_period'] + 1
                else:
                    dataframe.iloc[i, dataframe.columns.get_loc('buy_period')] = 99
            
            # 卖出信号计数器
            if not dataframe.iloc[i]['sell_setup_signal']:
                if dataframe.iloc[i-1]['sell_period'] < self.setup_len.value:
                    dataframe.iloc[i, dataframe.columns.get_loc('sell_period')] = dataframe.iloc[i-1]['sell_period'] + 1
                else:
                    dataframe.iloc[i, dataframe.columns.get_loc('sell_period')] = 99
        
        # 入场条件
        dataframe['long_entry_condition'] = (
            (dataframe['buy_period'] <= self.setup_len.value) &
            (dataframe['buy_setup'].notna())
        )
        
        dataframe['short_entry_condition'] = (
            (dataframe['sell_period'] <= self.setup_len.value) &
            (dataframe['sell_setup'].notna())
        )
        
        # 系统退出条件
        dataframe['long_exit_condition'] = (
            (dataframe['k_cross_below_d']) &
            (dataframe['k_val'] > self.oversold.value)
        )
        
        dataframe['short_exit_condition'] = (
            (dataframe['k_cross_above_d']) &
            (dataframe['k_val'] < self.overbought.value)
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：在有效期内且价格突破买入设置价格
        dataframe.loc[
            (
                (dataframe['long_entry_condition']) &
                (dataframe['high'] >= dataframe['buy_setup'] + 0.0001) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：在有效期内且价格跌破卖出设置价格
        dataframe.loc[
            (
                (dataframe['short_entry_condition']) &
                (dataframe['low'] <= dataframe['sell_setup'] - 0.0001) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：K值死叉但未达超卖
        dataframe.loc[
            (
                (dataframe['long_exit_condition'])
            ),
            'exit_long'] = 1

        # 空头出场：K值金叉但未达超买
        dataframe.loc[
            (
                (dataframe['short_exit_condition'])
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义止损逻辑：保护性止损
        """
        
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 保护性止损逻辑
        if trade.is_short:
            # 空头保护性止损：使用设置高点
            if 'sell_stop' in current_candle and not pd.isna(current_candle['sell_stop']):
                stop_price = current_candle['sell_stop']
                stop_loss_pct = (stop_price - trade.open_rate) / trade.open_rate
                return max(stop_loss_pct, self.stoploss)
        else:
            # 多头保护性止损：使用设置低点
            if 'buy_stop' in current_candle and not pd.isna(current_candle['buy_stop']):
                stop_price = current_candle['buy_stop']
                stop_loss_pct = (stop_price - trade.open_rate) / trade.open_rate
                return max(stop_loss_pct, self.stoploss)
        
        return self.stoploss
