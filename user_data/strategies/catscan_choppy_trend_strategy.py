# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class CatScanChoppyTrendStrategy(IStrategy):
    """
    CatScan振荡趋势识别策略
    
    策略逻辑：
    1. 使用ChoppyIndex判断市场状态（振荡/趋势）
    2. 振荡市场：使用较大的ATR倍数进行突破交易
    3. 趋势市场：使用较小的ATR倍数跟随趋势
    4. 区间突破：使用40周期高低点突破
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.12,      # 12%止盈
        "60": 0.06,     # 1小时后6%止盈
        "120": 0.04,    # 2小时后4%止盈
        "240": 0.02,    # 4小时后2%止盈
        "480": 0.01,    # 8小时后1%止盈
        "960": 0        # 16小时后0%止盈
    }

    # 止损设置 - 使用固定金额止损的概念
    stoploss = -0.10  # 10%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.03
    trailing_stop_positive_offset = 0.05
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 100

    # 策略参数
    choppy_period = IntParameter(25, 35, default=30, space="buy", optimize=True)
    chop_limits = DecimalParameter(50, 150, default=100, space="buy", optimize=True)
    atr_period = IntParameter(8, 15, default=10, space="buy", optimize=True)
    
    # ATR倍数参数
    choppy_atr_multiplier = DecimalParameter(1.0, 2.0, default=1.25, space="buy", optimize=True)
    trend_atr_multiplier = DecimalParameter(0.3, 0.8, default=0.5, space="buy", optimize=True)
    
    # 区间突破参数
    channel_period = IntParameter(35, 45, default=40, space="buy", optimize=True)
    
    # 指数加权移动平均参数
    ema_alpha = DecimalParameter(0.15, 0.25, default=0.182, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算真实波幅
        dataframe['tr'] = ta.TRANGE(dataframe)
        
        # 计算指数加权移动平均ATR（模拟XMA1和XMA2）
        dataframe['xma1'] = dataframe['tr'].ewm(alpha=self.ema_alpha.value).mean()
        dataframe['xma2'] = dataframe['tr'].ewm(alpha=self.ema_alpha.value).mean()
        
        # 计算ChoppyIndex
        dataframe['choppy_index'] = self.calculate_choppy_index(dataframe)
        
        # 判断市场状态
        dataframe['choppy_market'] = (
            (dataframe['choppy_index'] >= -self.chop_limits.value) & 
            (dataframe['choppy_index'] <= self.chop_limits.value)
        )
        dataframe['bull_trend'] = dataframe['choppy_index'] > self.chop_limits.value
        dataframe['bear_trend'] = dataframe['choppy_index'] < -self.chop_limits.value
        
        # 计算区间突破位
        dataframe['channel_high'] = dataframe['high'].rolling(window=self.channel_period.value).max()
        dataframe['channel_low'] = dataframe['low'].rolling(window=self.channel_period.value).min()
        
        # 计算入场价位
        # 振荡市场入场价位
        dataframe['choppy_buy_price'] = dataframe['open'] + self.choppy_atr_multiplier.value * dataframe['xma1']
        dataframe['choppy_sell_price'] = dataframe['open'] - self.choppy_atr_multiplier.value * dataframe['xma1']
        
        # 趋势市场入场价位
        dataframe['trend_buy_price'] = dataframe['open'] + self.trend_atr_multiplier.value * dataframe['xma2']
        dataframe['trend_sell_price'] = dataframe['open'] - self.trend_atr_multiplier.value * dataframe['xma2']
        
        # 区间突破入场价位
        dataframe['channel_buy_price'] = dataframe['channel_high'] + 0.001  # 加1个点
        dataframe['channel_sell_price'] = dataframe['channel_low'] - 0.001  # 减1个点
        
        # 入场条件
        # 振荡市场条件
        dataframe['choppy_buy_signal'] = (
            dataframe['choppy_market'] & 
            (dataframe['high'] >= dataframe['choppy_buy_price'])
        )
        dataframe['choppy_sell_signal'] = (
            dataframe['choppy_market'] & 
            (dataframe['low'] <= dataframe['choppy_sell_price'])
        )
        
        # 趋势市场条件
        dataframe['bull_buy_signal'] = (
            dataframe['bull_trend'] & 
            (dataframe['high'] >= dataframe['trend_buy_price'])
        )
        dataframe['bear_sell_signal'] = (
            dataframe['bear_trend'] & 
            (dataframe['low'] <= dataframe['trend_sell_price'])
        )
        
        # 区间突破条件
        dataframe['channel_buy_signal'] = dataframe['high'] >= dataframe['channel_buy_price']
        dataframe['channel_sell_signal'] = dataframe['low'] <= dataframe['channel_sell_price']

        return dataframe

    def calculate_choppy_index(self, dataframe: DataFrame) -> DataFrame:
        """
        计算ChoppyIndex振荡指数
        """
        period = self.choppy_period.value
        
        # 计算周期内最高价和最低价
        highest_high = dataframe['high'].rolling(window=period).max()
        lowest_low = dataframe['low'].rolling(window=period).min()
        
        # 计算最高价和最低价出现的位置
        highest_bar = dataframe['high'].rolling(window=period).apply(lambda x: period - 1 - x.argmax(), raw=True)
        lowest_bar = dataframe['low'].rolling(window=period).apply(lambda x: period - 1 - x.argmin(), raw=True)
        
        # 计算ChoppyIndex
        choppy_index = np.where(
            highest_bar <= lowest_bar,
            10 * (highest_high - lowest_low),
            -10 * (highest_high - lowest_low)
        )
        
        return choppy_index

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场条件（多个条件的OR组合）
        dataframe.loc[
            (
                (
                    (dataframe['choppy_buy_signal']) |      # 振荡市场买入
                    (dataframe['bull_buy_signal']) |        # 看涨趋势买入
                    (dataframe['channel_buy_signal'])       # 区间突破买入
                ) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场条件（多个条件的OR组合）
        dataframe.loc[
            (
                (
                    (dataframe['choppy_sell_signal']) |     # 振荡市场卖出
                    (dataframe['bear_sell_signal']) |       # 看跌趋势卖出
                    (dataframe['channel_sell_signal'])      # 区间突破卖出
                ) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：市场转为看跌趋势或振荡转为看跌
        dataframe.loc[
            (
                (dataframe['bear_trend']) |  # 转为看跌趋势
                (dataframe['choppy_index'] < -self.chop_limits.value * 0.8)  # 接近看跌区域
            ),
            'exit_long'] = 1

        # 空头出场：市场转为看涨趋势或振荡转为看涨
        dataframe.loc[
            (
                (dataframe['bull_trend']) |  # 转为看涨趋势
                (dataframe['choppy_index'] > self.chop_limits.value * 0.8)   # 接近看涨区域
            ),
            'exit_short'] = 1

        return dataframe
