# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime, time
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class IntradayRetracementMomentumStrategy(IStrategy):
    """
    日内回撤动量交易策略
    
    策略逻辑：
    1. 基于基准价格的回撤和动量交易
    2. 日内交易时段限制
    3. 包含回撤交易和动量突破交易
    4. 动态止损和盈利保护机制
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表 - 日内交易相对激进
    minimal_roi = {
        "0": 0.08,      # 8%止盈
        "30": 0.04,     # 30分钟后4%止盈
        "60": 0.02,     # 1小时后2%止盈
        "120": 0.01,    # 2小时后1%止盈
        "240": 0        # 4小时后0%止盈
    }

    # 止损设置
    stoploss = -0.05  # 5%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.03
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 50

    # 策略参数
    time_offset = IntParameter(0, 60, default=0, space="buy", optimize=True)
    lock_points = DecimalParameter(0.005, 0.02, default=0.01, space="buy", optimize=True)
    retracement_threshold = DecimalParameter(0.01, 0.02, default=0.013, space="buy", optimize=True)
    momentum_threshold = DecimalParameter(0.01, 0.02, default=0.013, space="buy", optimize=True)
    profit_protection_threshold = DecimalParameter(0.005, 0.015, default=0.008, space="buy", optimize=True)
    
    # 止损参数
    retracement_stop_pct = DecimalParameter(0.005, 0.015, default=0.009, space="sell", optimize=True)
    momentum_stop_pct = DecimalParameter(0.001, 0.005, default=0.001, space="sell", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算基准价格（使用前一日收盘价作为VALUE11的替代）
        dataframe['base_price'] = dataframe['close'].shift(24)  # 假设1小时K线，24根为一日
        dataframe['base_price'] = dataframe['base_price'].fillna(method='ffill')
        
        # 计算日内高低点（ID High/Low）
        dataframe['id_high'] = dataframe['high'].rolling(window=24, min_periods=1).max()
        dataframe['id_low'] = dataframe['low'].rolling(window=24, min_periods=1).min()
        dataframe['id_mid'] = (dataframe['id_high'] + dataframe['id_low']) * 0.5
        
        # 计算DATA2的高低点（使用滞后数据模拟）
        dataframe['data2_high'] = dataframe['high'].shift(1)
        dataframe['data2_low'] = dataframe['low'].shift(1)
        dataframe['data2_high_prev'] = dataframe['high'].shift(2)
        dataframe['data2_low_prev'] = dataframe['low'].shift(2)
        
        # 时间过滤（简化版本）
        dataframe['hour'] = dataframe.index.hour if hasattr(dataframe.index, 'hour') else 13
        dataframe['trading_time'] = (dataframe['hour'] >= 13) & (dataframe['hour'] < 15)
        dataframe['momentum_time'] = (dataframe['hour'] >= 13) & (dataframe['hour'] < 15)  # 简化为相同时间
        dataframe['closing_time'] = (dataframe['hour'] >= 15) & (dataframe['hour'] <= 15)
        
        # 回撤条件计算
        # 卖出回撤条件
        dataframe['sell_retracement_condition'] = (
            (dataframe['data2_low'] >= dataframe['data2_low_prev']) &  # DATA2低点不创新低
            (dataframe['data2_high'] < dataframe['id_mid']) &  # 高点低于ID中点
            (dataframe['base_price'] - dataframe['id_low'] >= self.retracement_threshold.value * dataframe['base_price'])  # 跌幅超阈值
        )
        
        # 买入回撤条件
        dataframe['buy_retracement_condition'] = (
            (dataframe['data2_high'] <= dataframe['data2_high_prev']) &  # DATA2高点不创新高
            (dataframe['data2_low'] > dataframe['id_mid']) &  # 低点高于ID中点
            (dataframe['id_high'] - dataframe['base_price'] >= self.retracement_threshold.value * dataframe['base_price'])  # 涨幅超阈值
        )
        
        # 动量条件计算
        dataframe['buy_momentum_condition'] = (
            (dataframe['momentum_time']) &
            (dataframe['id_high'] - dataframe['base_price'] >= self.momentum_threshold.value * dataframe['base_price'])
        )
        
        dataframe['sell_momentum_condition'] = (
            (dataframe['momentum_time']) &
            (dataframe['base_price'] - dataframe['id_low'] >= self.momentum_threshold.value * dataframe['base_price'])
        )
        
        # 入场价格计算
        dataframe['retracement_sell_price'] = dataframe['low'] - 0.0005  # 模拟0.05点
        dataframe['retracement_buy_price'] = dataframe['high'] + 0.0005
        dataframe['momentum_buy_price'] = dataframe['id_high'] + 0.0005
        dataframe['momentum_sell_price'] = dataframe['id_low'] - 0.0005
        
        # 止损价格计算
        dataframe['retracement_long_stop'] = np.minimum(
            dataframe['data2_low'] - 0.001,
            dataframe['close'] * (1 - self.retracement_stop_pct.value)
        )
        
        dataframe['retracement_short_stop'] = np.maximum(
            dataframe['data2_high'] + 0.001,
            dataframe['close'] * (1 + self.retracement_stop_pct.value)
        )
        
        dataframe['momentum_long_stop'] = np.minimum(
            dataframe['data2_low'] - 0.001,
            dataframe['close'] * (1 - self.momentum_stop_pct.value)
        )
        
        dataframe['momentum_short_stop'] = np.maximum(
            dataframe['data2_high'] + 0.001,
            dataframe['close'] * (1 + self.momentum_stop_pct.value)
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 回撤多头入场（L1）
        dataframe.loc[
            (
                (dataframe['trading_time']) &
                (dataframe['buy_retracement_condition']) &
                (dataframe['high'] < dataframe['id_high']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 回撤空头入场（S1）
        dataframe.loc[
            (
                (dataframe['trading_time']) &
                (dataframe['sell_retracement_condition']) &
                (dataframe['low'] > dataframe['id_low']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        # 动量多头入场（L2）
        dataframe.loc[
            (
                (dataframe['momentum_time']) &
                (dataframe['buy_momentum_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 动量空头入场（S2）
        dataframe.loc[
            (
                (dataframe['momentum_time']) &
                (dataframe['sell_momentum_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：收盘时间或止损
        dataframe.loc[
            (
                (dataframe['closing_time']) |
                (dataframe['low'] <= dataframe['retracement_long_stop']) |
                (dataframe['low'] <= dataframe['momentum_long_stop'])
            ),
            'exit_long'] = 1

        # 空头出场：收盘时间或止损
        dataframe.loc[
            (
                (dataframe['closing_time']) |
                (dataframe['high'] >= dataframe['retracement_short_stop']) |
                (dataframe['high'] >= dataframe['momentum_short_stop'])
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义止损逻辑：盈利保护
        """
        
        # 盈利保护：浮盈超过阈值后锁定利润
        if current_profit >= self.profit_protection_threshold.value:
            # 锁定1个点的利润（简化为0.1%）
            if trade.is_short:
                return -(self.lock_points.value)  # 空头锁定利润
            else:
                return -(self.lock_points.value)  # 多头锁定利润
        
        return self.stoploss
