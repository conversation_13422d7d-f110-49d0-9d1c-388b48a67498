# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class TrendRangeBreakoutStrategy(IStrategy):
    """
    趋势区间突破策略
    
    策略逻辑：
    1. 计算20周期的交易区间
    2. 根据价格突破判断趋势方向
    3. 在趋势方向确定后，在特定价位入场
    4. 支持双向交易
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.08,      # 8%止盈
        "60": 0.04,     # 1小时后4%止盈
        "120": 0.02,    # 2小时后2%止盈
        "240": 0.01,    # 4小时后1%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.05  # 5%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.03
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 50

    # 策略参数
    trade_length = IntParameter(15, 30, default=20, space="buy", optimize=True)
    long_percent = DecimalParameter(20, 80, default=40, space="buy", optimize=True)
    short_percent = DecimalParameter(20, 80, default=60, space="buy", optimize=True)
    exit_long_percent = DecimalParameter(70, 90, default=80, space="sell", optimize=True)
    exit_short_percent = DecimalParameter(10, 30, default=20, space="sell", optimize=True)
    top_range_multiplier = DecimalParameter(0.8, 1.2, default=1.0, space="buy", optimize=True)
    bottom_range_multiplier = DecimalParameter(0.8, 1.2, default=1.0, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算20周期高低点
        dataframe['highest_high'] = dataframe['high'].rolling(window=self.trade_length.value).max()
        dataframe['lowest_low'] = dataframe['low'].rolling(window=self.trade_length.value).min()
        
        # 计算交易区间
        dataframe['trade_range'] = (dataframe['highest_high'] - dataframe['lowest_low']) / 100
        
        # 计算入场价格
        # Long_Entry_Price = 20周期低点 + 交易区间 * 多头因子
        dataframe['long_entry_price'] = (
            dataframe['lowest_low'] + 
            dataframe['trade_range'] * self.long_percent.value
        )
        
        # Short_Entry_Price = 20周期低点 + 交易区间 * 空头因子
        dataframe['short_entry_price'] = (
            dataframe['lowest_low'] + 
            dataframe['trade_range'] * self.short_percent.value
        )
        
        # 计算出场价格
        # LongExit = 20周期低点 + 交易区间 * 多头出场因子
        dataframe['long_exit_price'] = (
            dataframe['lowest_low'] + 
            dataframe['trade_range'] * self.exit_long_percent.value
        )
        
        # ShortExit = 20周期低点 + 交易区间 * 空头出场因子
        dataframe['short_exit_price'] = (
            dataframe['lowest_low'] + 
            dataframe['trade_range'] * self.exit_short_percent.value
        )
        
        # 计算顶部和底部区间
        dataframe['top_range'] = dataframe['highest_high'] * self.top_range_multiplier.value
        dataframe['bottom_range'] = dataframe['lowest_low'] * self.bottom_range_multiplier.value
        
        # 趋势方向判断
        dataframe['trend_direction'] = 0
        
        # 如果高点突破顶部区间，趋势方向为1（做多）
        dataframe.loc[dataframe['high'] > dataframe['top_range'], 'trend_direction'] = 1
        
        # 如果低点跌破底部区间，趋势方向为-1（做空）
        dataframe.loc[dataframe['low'] < dataframe['bottom_range'], 'trend_direction'] = -1
        
        # 向前填充趋势方向
        dataframe['trend_direction'] = dataframe['trend_direction'].replace(0, np.nan).fillna(method='ffill').fillna(0)
        
        # 入场条件
        # 趋势方向为1且价格达到多头入场价格
        dataframe['long_condition'] = (
            (dataframe['trend_direction'] == 1) &
            (dataframe['high'] >= dataframe['long_entry_price'])
        )
        
        # 趋势方向为-1且价格达到空头入场价格
        dataframe['short_condition'] = (
            (dataframe['trend_direction'] == -1) &
            (dataframe['low'] <= dataframe['short_entry_price'])
        )
        
        # 出场条件
        # 多头出场：价格达到多头出场价格
        dataframe['long_exit_condition'] = dataframe['low'] <= dataframe['long_exit_price']
        
        # 空头出场：价格达到空头出场价格
        dataframe['short_exit_condition'] = dataframe['high'] >= dataframe['short_exit_price']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场
        dataframe.loc[
            (
                (dataframe['long_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场
        dataframe.loc[
            (
                (dataframe['short_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场
        dataframe.loc[
            (
                (dataframe['long_exit_condition']) &
                (dataframe['volume'] > 0)
            ),
            'exit_long'] = 1

        # 空头出场
        dataframe.loc[
            (
                (dataframe['short_exit_condition']) &
                (dataframe['volume'] > 0)
            ),
            'exit_short'] = 1

        return dataframe
