# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class TripleMAAdxTrendStrategy(IStrategy):
    """
    三重移动平均+ADX趋势策略
    
    策略逻辑：
    1. 使用快速和慢速三重移动平均线判断趋势方向
    2. 使用ADX指标确认趋势强度
    3. 快速线上穿慢速线且ADX增强时做多
    4. 快速线下穿慢速线且ADX增强时做空
    5. 支持金字塔加仓和动态止损
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.12,      # 12%止盈
        "60": 0.06,     # 1小时后6%止盈
        "120": 0.04,    # 2小时后4%止盈
        "240": 0.02,    # 4小时后2%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.08  # 8%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.04
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 100

    # 策略参数
    fast_length = IntParameter(3, 8, default=5, space="buy", optimize=True)     # 快速三重均线周期
    slow_length = IntParameter(20, 40, default=30, space="buy", optimize=True)  # 慢速三重均线周期
    adx_length = IntParameter(10, 16, default=12, space="buy", optimize=True)   # ADX指标周期
    
    # 金字塔参数
    max_pyramid_positions = IntParameter(1, 3, default=3, space="buy", optimize=True)
    pyramid_enabled = BooleanParameter(default=True, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算三重移动平均线（使用TEMA作为三重均线的近似）
        dataframe['fast_ma'] = ta.TEMA(dataframe['close'], timeperiod=self.fast_length.value)
        dataframe['slow_ma'] = ta.TEMA(dataframe['close'], timeperiod=self.slow_length.value)
        
        # 如果TEMA不可用，使用EMA的三重平滑
        if dataframe['fast_ma'].isna().all():
            ema1 = ta.EMA(dataframe['close'], timeperiod=self.fast_length.value)
            ema2 = ta.EMA(ema1, timeperiod=self.fast_length.value)
            dataframe['fast_ma'] = ta.EMA(ema2, timeperiod=self.fast_length.value)
            
        if dataframe['slow_ma'].isna().all():
            ema1 = ta.EMA(dataframe['close'], timeperiod=self.slow_length.value)
            ema2 = ta.EMA(ema1, timeperiod=self.slow_length.value)
            dataframe['slow_ma'] = ta.EMA(ema2, timeperiod=self.slow_length.value)
        
        # 计算ADX指标
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_length.value)
        
        # 计算ADX趋势强度（当前ADX > N周期前的ADX）
        dataframe['adx_prev'] = dataframe['adx'].shift(self.fast_length.value)
        dataframe['trending'] = dataframe['adx'] > dataframe['adx_prev']
        
        # 生成交易信号
        dataframe['go_long'] = dataframe['fast_ma'] > dataframe['slow_ma']
        dataframe['go_short'] = dataframe['fast_ma'] < dataframe['slow_ma']
        
        # 穿越信号
        dataframe['fast_cross_above_slow'] = qtpylib.crossed_above(dataframe['fast_ma'], dataframe['slow_ma'])
        dataframe['fast_cross_below_slow'] = qtpylib.crossed_below(dataframe['fast_ma'], dataframe['slow_ma'])
        
        # 设置入场价位
        dataframe['buy_stop'] = dataframe['high'] + 0.0001  # 高点上方1个点
        dataframe['sell_stop'] = dataframe['low'] - 0.0001  # 低点下方1个点
        
        # 金字塔加仓条件
        if self.pyramid_enabled.value:
            dataframe['pyramid_long_condition'] = (
                (dataframe['high'] < dataframe['fast_ma']) &
                (dataframe['trending'])
            )
            dataframe['pyramid_short_condition'] = (
                (dataframe['low'] > dataframe['fast_ma']) &
                (dataframe['trending'])
            )
        else:
            dataframe['pyramid_long_condition'] = False
            dataframe['pyramid_short_condition'] = False
        
        # 动态止损位
        dataframe['long_stop'] = dataframe['slow_ma'] - 0.0001
        dataframe['short_stop'] = dataframe['slow_ma'] + 0.0001

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：快速线上穿慢速线
        dataframe.loc[
            (
                (dataframe['fast_cross_above_slow']) &
                (dataframe['go_long']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：快速线下穿慢速线
        dataframe.loc[
            (
                (dataframe['fast_cross_below_slow']) &
                (dataframe['go_short']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：快速线下穿慢速线
        dataframe.loc[
            (
                (dataframe['fast_cross_below_slow']) |
                (~dataframe['go_long'])
            ),
            'exit_long'] = 1

        # 空头出场：快速线上穿慢速线
        dataframe.loc[
            (
                (dataframe['fast_cross_above_slow']) |
                (~dataframe['go_short'])
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义动态止损
        """
        
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        if trade.is_short:
            # 空头止损：慢速线上方
            stop_price = current_candle['short_stop']
            if stop_price > 0:
                return (stop_price - current_rate) / current_rate
        else:
            # 多头止损：慢速线下方
            stop_price = current_candle['long_stop']
            if stop_price > 0:
                return (stop_price - current_rate) / current_rate
        
        return self.stoploss

    def adjust_trade_position(self, trade: 'Trade', current_time: datetime,
                            current_rate: float, current_profit: float,
                            min_stake: float, max_stake: float, **kwargs) -> float:
        """
        金字塔加仓逻辑
        """
        
        if not self.pyramid_enabled.value:
            return None
            
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 检查当前持仓数量
        if trade.nr_of_successful_entries >= self.max_pyramid_positions.value:
            return None
        
        if trade.is_short:
            # 空头金字塔加仓：低点在快速线上方且ADX增强
            if current_candle['pyramid_short_condition']:
                return min_stake
        else:
            # 多头金字塔加仓：高点在快速线下方且ADX增强
            if current_candle['pyramid_long_condition']:
                return min_stake
        
        return None
