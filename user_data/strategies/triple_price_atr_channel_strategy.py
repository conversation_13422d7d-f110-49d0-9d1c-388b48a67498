# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class TriplePriceAtrChannelStrategy(IStrategy):
    """
    三价均线+ATR通道策略
    
    策略逻辑：
    1. 基于最高价、最低价、收盘价三者平均值计算三价均线
    2. 基于三价均线加减真实波幅(ATR)计算通道上下轨
    3. 入场条件：三价均线向上且价格上破通道上轨开多单；三价均线向下且价格下破通道下轨开空单
    4. 出场条件：持有多单时价格下破三价均线平多单；持有空单时价格上破三价均线平空单
    5. 使用线性回归角度判断趋势强度
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.10,      # 10%止盈
        "60": 0.05,     # 1小时后5%止盈
        "120": 0.03,    # 2小时后3%止盈
        "240": 0.015,   # 4小时后1.5%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.07  # 7%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.035
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 60

    # 策略参数
    avg_length = IntParameter(30, 50, default=40, space="buy", optimize=True)       # 移动平均计算周期长度
    atr_length = IntParameter(30, 50, default=40, space="buy", optimize=True)       # 平均真实波幅(ATR)计算周期长度
    p_angle = DecimalParameter(30, 70, default=50, space="buy", optimize=True)      # 线性回归角度阈值(用于判断趋势强度)
    
    # ATR倍数参数
    atr_multiplier = DecimalParameter(0.8, 1.5, default=1.0, space="buy", optimize=True)
    
    # 趋势过滤器
    trend_filter_enabled = BooleanParameter(default=True, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算三价平均值 (高+低+收)/3
        dataframe['hlc3'] = (dataframe['high'] + dataframe['low'] + dataframe['close']) / 3
        
        # 计算三价移动平均
        dataframe['movavgval'] = ta.SMA(dataframe['hlc3'], timeperiod=self.avg_length.value)
        
        # 计算ATR
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_length.value)
        
        # 计算通道上下轨(基于ATR波动幅度)
        dataframe['up_band'] = dataframe['movavgval'] + dataframe['atr'] * self.atr_multiplier.value
        dataframe['down_band'] = dataframe['movavgval'] - dataframe['atr'] * self.atr_multiplier.value
        
        # 计算移动平均值的线性回归指标
        # 使用简化的线性回归斜率计算
        period = 2
        dataframe['lr_slope'] = (dataframe['movavgval'] - dataframe['movavgval'].shift(period)) / period
        
        # 将斜率转换为角度（近似）
        dataframe['lr_angle'] = np.arctan(dataframe['lr_slope']) * 180 / np.pi
        
        # 趋势强度判断
        if self.trend_filter_enabled.value:
            dataframe['strong_trend'] = (
                (abs(dataframe['lr_angle']) > self.p_angle.value)
            )
        else:
            dataframe['strong_trend'] = True
        
        # 趋势方向判断
        dataframe['uptrend'] = dataframe['movavgval'] > dataframe['movavgval'].shift(1)
        dataframe['downtrend'] = dataframe['movavgval'] < dataframe['movavgval'].shift(1)
        
        # 突破信号
        dataframe['breakout_upper'] = dataframe['close'] > dataframe['up_band']
        dataframe['breakout_lower'] = dataframe['close'] < dataframe['down_band']
        
        # 回归信号
        dataframe['return_to_ma_from_upper'] = (
            (dataframe['close'] < dataframe['movavgval']) &
            (dataframe['close'].shift(1) >= dataframe['movavgval'].shift(1))
        )
        
        dataframe['return_to_ma_from_lower'] = (
            (dataframe['close'] > dataframe['movavgval']) &
            (dataframe['close'].shift(1) <= dataframe['movavgval'].shift(1))
        )
        
        # 入场信号
        dataframe['long_signal'] = (
            (dataframe['uptrend']) &
            (dataframe['strong_trend']) &
            (dataframe['breakout_upper'])
        )
        
        dataframe['short_signal'] = (
            (dataframe['downtrend']) &
            (dataframe['strong_trend']) &
            (dataframe['breakout_lower'])
        )
        
        # 出场信号
        dataframe['long_exit_signal'] = dataframe['return_to_ma_from_upper']
        dataframe['short_exit_signal'] = dataframe['return_to_ma_from_lower']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：三价均线向上且价格上破通道上轨
        dataframe.loc[
            (
                (dataframe['long_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：三价均线向下且价格下破通道下轨
        dataframe.loc[
            (
                (dataframe['short_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：价格下破三价均线
        dataframe.loc[
            (
                (dataframe['long_exit_signal'])
            ),
            'exit_long'] = 1

        # 空头出场：价格上破三价均线
        dataframe.loc[
            (
                (dataframe['short_exit_signal'])
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义止损：使用三价移动平均线作为动态止损
        """
        
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        if trade.is_short:
            # 空头止损：三价移动平均线上方
            stop_price = current_candle['movavgval'] * 1.005  # 均线上方0.5%
            if stop_price > 0:
                return (stop_price - current_rate) / current_rate
        else:
            # 多头止损：三价移动平均线下方
            stop_price = current_candle['movavgval'] * 0.995  # 均线下方0.5%
            if stop_price > 0:
                return (stop_price - current_rate) / current_rate
        
        return self.stoploss
