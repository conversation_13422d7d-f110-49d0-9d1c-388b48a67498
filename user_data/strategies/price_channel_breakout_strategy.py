# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class PriceChannelBreakoutStrategy(IStrategy):
    """
    价格通道突破策略
    
    策略逻辑：
    1. 计算10周期价格通道（基于收盘价）
    2. 收盘价突破上轨且出现新低时做多
    3. 收盘价跌破下轨且出现新高时做空
    4. 使用8周期追踪止损
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.10,      # 10%止盈
        "60": 0.05,     # 1小时后5%止盈
        "120": 0.03,    # 2小时后3%止盈
        "240": 0.015,   # 4小时后1.5%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.08  # 8%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.03
    trailing_stop_positive_offset = 0.05
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 50

    # 策略参数
    channel_period = IntParameter(8, 15, default=10, space="buy", optimize=True)
    retr_bars = IntParameter(8, 15, default=10, space="buy", optimize=True)
    new_lo_hi = IntParameter(3, 6, default=4, space="buy", optimize=True)
    trail_stp = IntParameter(6, 12, default=8, space="sell", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算价格通道（基于收盘价）
        dataframe['hi_channel'] = dataframe['close'].rolling(window=self.channel_period.value).max().shift(1)
        dataframe['lo_channel'] = dataframe['close'].rolling(window=self.channel_period.value).min().shift(1)
        
        # 计算新高新低
        dataframe['new_low'] = dataframe['low'].rolling(window=self.new_lo_hi.value).min().shift(1)
        dataframe['new_high'] = dataframe['high'].rolling(window=self.new_lo_hi.value).max().shift(1)
        
        # 计算MRO（Most Recent Occurrence）函数的简化版本
        # 检查最近retr_bars周期内是否出现过突破
        dataframe['close_above_channel'] = dataframe['close'] > dataframe['hi_channel']
        dataframe['close_below_channel'] = dataframe['close'] < dataframe['lo_channel']
        
        # 计算突破信号的最近出现
        dataframe['recent_break_up'] = False
        dataframe['recent_break_down'] = False
        
        for i in range(self.retr_bars.value, len(dataframe)):
            # 检查最近retr_bars周期内是否有向上突破
            recent_up = dataframe['close_above_channel'].iloc[i-self.retr_bars.value:i+1].any()
            dataframe.iloc[i, dataframe.columns.get_loc('recent_break_up')] = recent_up
            
            # 检查最近retr_bars周期内是否有向下突破
            recent_down = dataframe['close_below_channel'].iloc[i-self.retr_bars.value:i+1].any()
            dataframe.iloc[i, dataframe.columns.get_loc('recent_break_down')] = recent_down
        
        # 多头入场条件
        dataframe['long_entry_condition'] = (
            (dataframe['recent_break_up']) &
            (dataframe['low'] < dataframe['new_low'])
        )
        
        # 空头入场条件
        dataframe['short_entry_condition'] = (
            (dataframe['recent_break_down']) &
            (dataframe['high'] > dataframe['new_high'])
        )
        
        # 计算追踪止损退出条件
        dataframe['trail_high'] = dataframe['high'].rolling(window=self.trail_stp.value).max().shift(1)
        dataframe['trail_low'] = dataframe['low'].rolling(window=self.trail_stp.value).min().shift(1)
        
        # 多头追踪止损退出条件
        dataframe['long_trail_exit'] = dataframe['high'] > dataframe['trail_high']
        
        # 空头追踪止损退出条件
        dataframe['short_trail_exit'] = dataframe['low'] < dataframe['trail_low']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场
        dataframe.loc[
            (
                (dataframe['long_entry_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场
        dataframe.loc[
            (
                (dataframe['short_entry_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：追踪止损退出
        dataframe.loc[
            (
                (dataframe['long_trail_exit'])
            ),
            'exit_long'] = 1

        # 空头出场：追踪止损退出
        dataframe.loc[
            (
                (dataframe['short_trail_exit'])
            ),
            'exit_short'] = 1

        return dataframe
