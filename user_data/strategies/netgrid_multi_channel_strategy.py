# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class NetGridMultiChannelStrategy(IStrategy):
    """
    NetGrid多级通道网格策略
    
    策略逻辑：
    1. 计算1000周期移动平均线作为基准线
    2. 计算200周期平均波动幅度(High-Low)
    3. 基于20倍波动幅度构建4级上轨和4级下轨
    4. 价格穿越基准线时开仓，在各级通道进行金字塔加仓
    5. 极端通道触发时平仓
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表 - 网格策略需要较大的ROI空间
    minimal_roi = {
        "0": 0.15,      # 15%止盈
        "120": 0.08,    # 2小时后8%止盈
        "240": 0.05,    # 4小时后5%止盈
        "480": 0.02,    # 8小时后2%止盈
        "960": 0        # 16小时后0%止盈
    }

    # 止损设置
    stoploss = -0.12  # 12%止损

    # 追踪止损
    trailing_stop = False  # 网格策略不使用追踪止损

    # 启动蜡烛数量
    startup_candle_count: int = 1200

    # 策略参数
    n1 = IntParameter(800, 1200, default=1000, space="buy", optimize=True)    # 长期移动平均线周期
    n2 = IntParameter(150, 250, default=200, space="buy", optimize=True)      # 波动幅度平滑周期
    n3 = IntParameter(15, 25, default=20, space="buy", optimize=True)         # 通道宽度乘数
    
    # 金字塔参数
    max_pyramid_long = IntParameter(1, 4, default=3, space="buy", optimize=True)
    max_pyramid_short = IntParameter(1, 4, default=3, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算长期移动平均线
        dataframe['ma1'] = ta.SMA(dataframe['close'], timeperiod=self.n1.value)
        
        # 计算单根K线波动幅度
        dataframe['bd'] = dataframe['high'] - dataframe['low']
        
        # 计算波动幅度的移动平均
        dataframe['bdma'] = ta.SMA(dataframe['bd'], timeperiod=self.n2.value)
        
        # 计算多级通道上轨（阻力位）
        dataframe['up1'] = dataframe['ma1'] + self.n3.value * dataframe['bdma'] / 4    # 上轨1/4
        dataframe['up2'] = dataframe['ma1'] + self.n3.value * dataframe['bdma'] / 2    # 上轨1/2
        dataframe['up3'] = dataframe['ma1'] + self.n3.value * dataframe['bdma'] * 3/4  # 上轨3/4
        dataframe['up4'] = dataframe['ma1'] + self.n3.value * dataframe['bdma']        # 完整上轨
        
        # 计算多级通道下轨（支撑位）
        dataframe['dw1'] = dataframe['ma1'] - self.n3.value * dataframe['bdma'] / 4    # 下轨1/4
        dataframe['dw2'] = dataframe['ma1'] - self.n3.value * dataframe['bdma'] / 2    # 下轨1/2
        dataframe['dw3'] = dataframe['ma1'] - self.n3.value * dataframe['bdma'] * 3/4  # 下轨3/4
        dataframe['dw4'] = dataframe['ma1'] - self.n3.value * dataframe['bdma']        # 完整下轨
        
        # 计算穿越信号
        dataframe['cross_above_ma1'] = qtpylib.crossed_above(dataframe['close'], dataframe['ma1'])
        dataframe['cross_below_ma1'] = qtpylib.crossed_below(dataframe['close'], dataframe['ma1'])
        
        # 计算通道触及信号
        dataframe['touch_up1'] = dataframe['high'] >= dataframe['up1']
        dataframe['touch_up2'] = dataframe['high'] >= dataframe['up2']
        dataframe['touch_up3'] = dataframe['high'] >= dataframe['up3']
        dataframe['touch_up4'] = dataframe['high'] >= dataframe['up4']
        
        dataframe['touch_dw1'] = dataframe['low'] <= dataframe['dw1']
        dataframe['touch_dw2'] = dataframe['low'] <= dataframe['dw2']
        dataframe['touch_dw3'] = dataframe['low'] <= dataframe['dw3']
        dataframe['touch_dw4'] = dataframe['low'] <= dataframe['dw4']
        
        # 基础入场信号
        dataframe['basic_long_signal'] = dataframe['cross_above_ma1']
        dataframe['basic_short_signal'] = dataframe['cross_below_ma1']
        
        # 金字塔加仓信号
        dataframe['pyramid_long_dw1'] = dataframe['touch_dw1']
        dataframe['pyramid_long_dw2'] = dataframe['touch_dw2']
        dataframe['pyramid_long_dw3'] = dataframe['touch_dw3']
        
        dataframe['pyramid_short_up1'] = dataframe['touch_up1']
        dataframe['pyramid_short_up2'] = dataframe['touch_up2']
        dataframe['pyramid_short_up3'] = dataframe['touch_up3']
        
        # 极端平仓信号
        dataframe['extreme_long_exit'] = dataframe['touch_up4']
        dataframe['extreme_short_exit'] = dataframe['touch_dw4']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 基础多头入场：价格上穿ma1
        dataframe.loc[
            (
                (dataframe['basic_long_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 基础空头入场：价格下穿ma1
        dataframe.loc[
            (
                (dataframe['basic_short_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：价格突破完整上轨up4
        dataframe.loc[
            (
                (dataframe['extreme_long_exit'])
            ),
            'exit_long'] = 1

        # 空头出场：价格突破完整下轨dw4
        dataframe.loc[
            (
                (dataframe['extreme_short_exit'])
            ),
            'exit_short'] = 1

        return dataframe

    def adjust_trade_position(self, trade: 'Trade', current_time: datetime,
                            current_rate: float, current_profit: float,
                            min_stake: float, max_stake: float, **kwargs) -> float:
        """
        金字塔加仓逻辑
        """
        
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        # 获取当前持仓数量
        current_position = trade.amount
        
        if trade.is_short:
            # 空头金字塔加仓逻辑
            if (current_candle['pyramid_short_up1'] and 
                current_position < self.max_pyramid_short.value):
                # 在上轨1位置加空仓
                return min_stake
            elif (current_candle['pyramid_short_up2'] and 
                  current_position < self.max_pyramid_short.value * 2):
                # 在上轨2位置加空仓
                return min_stake * 2
            elif (current_candle['pyramid_short_up3'] and 
                  current_position < self.max_pyramid_short.value * 3):
                # 在上轨3位置加空仓
                return min_stake * 3
        else:
            # 多头金字塔加仓逻辑
            if (current_candle['pyramid_long_dw1'] and 
                current_position < self.max_pyramid_long.value):
                # 在下轨1位置加多仓
                return min_stake
            elif (current_candle['pyramid_long_dw2'] and 
                  current_position < self.max_pyramid_long.value * 2):
                # 在下轨2位置加多仓
                return min_stake * 2
            elif (current_candle['pyramid_long_dw3'] and 
                  current_position < self.max_pyramid_long.value * 3):
                # 在下轨3位置加多仓
                return min_stake * 3
        
        return None
