# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class DynamicBollingerBreakoutStrategy(IStrategy):
    """
    动态布林带突破策略（DBS）
    
    策略逻辑：
    1. 基于波动率动态调整布林带周期
    2. 突破布林带上轨做多，突破下轨做空
    3. 使用均线作为平仓信号
    4. 支持两种模式：价格通道和布林带通道
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.10,      # 10%止盈
        "60": 0.05,     # 1小时后5%止盈
        "120": 0.03,    # 2小时后3%止盈
        "240": 0.02,    # 4小时后2%止盈
        "480": 0.01,    # 8小时后1%止盈
        "960": 0        # 16小时后0%止盈
    }

    # 止损设置
    stoploss = -0.08  # 8%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.03
    trailing_stop_positive_offset = 0.05
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 100

    # 策略参数
    # 第一代DBS参数
    ceiling_period = IntParameter(25, 35, default=30, space="buy", optimize=True)
    floor_period = IntParameter(8, 15, default=10, space="buy", optimize=True)
    stddev_period = IntParameter(25, 35, default=30, space="buy", optimize=True)
    
    # 第二代DBS参数
    ceiling_amt = IntParameter(50, 70, default=60, space="buy", optimize=True)
    floor_amt = IntParameter(15, 25, default=20, space="buy", optimize=True)
    bol_band_trig = DecimalParameter(1.5, 2.5, default=2.0, space="buy", optimize=True)
    
    # 策略模式选择
    use_bollinger_mode = BooleanParameter(default=True, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算标准差和波动率变化
        dataframe['volatility'] = ta.STDDEV(dataframe['close'], timeperiod=self.stddev_period.value)
        dataframe['volatility_prev'] = dataframe['volatility'].shift(1)
        
        # 计算波动率变化率（避免除零）
        dataframe['volatility_change'] = 0.0
        mask = dataframe['volatility_prev'] != 0
        dataframe.loc[mask, 'volatility_change'] = (
            (dataframe.loc[mask, 'volatility'] - dataframe.loc[mask, 'volatility_prev']) / 
            dataframe.loc[mask, 'volatility_prev']
        )
        
        # 动态调整周期N
        if self.use_bollinger_mode.value:
            # 第二代DBS：使用更大的周期范围
            initial_period = (self.ceiling_amt.value + self.floor_amt.value) / 2
            ceiling = self.ceiling_amt.value
            floor = self.floor_amt.value
        else:
            # 第一代DBS：使用较小的周期范围
            initial_period = (self.ceiling_period.value + self.floor_period.value) / 2
            ceiling = self.ceiling_period.value
            floor = self.floor_period.value
        
        # 初始化动态周期
        dataframe['dynamic_period'] = initial_period
        
        # 动态调整周期
        for i in range(1, len(dataframe)):
            prev_period = dataframe.iloc[i-1]['dynamic_period']
            volatility_change = dataframe.iloc[i]['volatility_change']
            
            # 根据波动率变化调整周期
            new_period = prev_period * (1 + volatility_change)
            
            # 限制在上下限范围内
            new_period = max(floor, min(ceiling, new_period))
            dataframe.iloc[i, dataframe.columns.get_loc('dynamic_period')] = new_period
        
        # 将周期转换为整数
        dataframe['dynamic_period_int'] = dataframe['dynamic_period'].round().astype(int)
        
        # 计算动态指标
        if self.use_bollinger_mode.value:
            # 第二代DBS：布林带模式
            dataframe['bb_upper'] = np.nan
            dataframe['bb_lower'] = np.nan
            dataframe['bb_middle'] = np.nan
            
            # 计算动态布林带
            for i in range(len(dataframe)):
                period = int(dataframe.iloc[i]['dynamic_period_int'])
                if i >= period:
                    close_data = dataframe['close'].iloc[i-period+1:i+1]
                    mean = close_data.mean()
                    std = close_data.std()
                    
                    dataframe.iloc[i, dataframe.columns.get_loc('bb_middle')] = mean
                    dataframe.iloc[i, dataframe.columns.get_loc('bb_upper')] = mean + self.bol_band_trig.value * std
                    dataframe.iloc[i, dataframe.columns.get_loc('bb_lower')] = mean - self.bol_band_trig.value * std
            
            # 突破点位
            dataframe['buy_point'] = dataframe['bb_upper']
            dataframe['sell_point'] = dataframe['bb_lower']
            dataframe['long_exit_point'] = dataframe['bb_middle']
            dataframe['short_exit_point'] = dataframe['bb_middle']
            
            # 入场条件：价格突破布林带
            dataframe['long_condition'] = (
                (dataframe['close'] > dataframe['bb_upper']) &
                (dataframe['high'] >= dataframe['buy_point'])
            )
            dataframe['short_condition'] = (
                (dataframe['close'] < dataframe['bb_lower']) &
                (dataframe['low'] <= dataframe['sell_point'])
            )
            
        else:
            # 第一代DBS：价格通道模式
            dataframe['channel_high'] = np.nan
            dataframe['channel_low'] = np.nan
            
            # 计算动态价格通道
            for i in range(len(dataframe)):
                period = int(dataframe.iloc[i]['dynamic_period_int'])
                if i >= period:
                    high_data = dataframe['high'].iloc[i-period+1:i+1]
                    low_data = dataframe['low'].iloc[i-period+1:i+1]
                    
                    dataframe.iloc[i, dataframe.columns.get_loc('channel_high')] = high_data.max()
                    dataframe.iloc[i, dataframe.columns.get_loc('channel_low')] = low_data.min()
            
            # 突破点位
            dataframe['buy_point'] = dataframe['channel_high']
            dataframe['sell_point'] = dataframe['channel_low']
            
            # 入场条件：价格突破通道
            dataframe['long_condition'] = dataframe['high'] >= dataframe['buy_point']
            dataframe['short_condition'] = dataframe['low'] <= dataframe['sell_point']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场
        dataframe.loc[
            (
                (dataframe['long_condition']) &
                (dataframe['buy_point'].notna()) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场
        dataframe.loc[
            (
                (dataframe['short_condition']) &
                (dataframe['sell_point'].notna()) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        if self.use_bollinger_mode.value:
            # 布林带模式：回到中轨平仓
            dataframe.loc[
                (
                    (dataframe['low'] <= dataframe['long_exit_point']) &
                    (dataframe['long_exit_point'].notna())
                ),
                'exit_long'] = 1

            dataframe.loc[
                (
                    (dataframe['high'] >= dataframe['short_exit_point']) &
                    (dataframe['short_exit_point'].notna())
                ),
                'exit_short'] = 1
        else:
            # 价格通道模式：使用简单的反向突破出场
            dataframe.loc[
                (
                    (dataframe['low'] <= dataframe['sell_point']) &
                    (dataframe['sell_point'].notna())
                ),
                'exit_long'] = 1

            dataframe.loc[
                (
                    (dataframe['high'] >= dataframe['buy_point']) &
                    (dataframe['buy_point'].notna())
                ),
                'exit_short'] = 1

        return dataframe
