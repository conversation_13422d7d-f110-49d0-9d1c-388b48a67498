# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file

import numpy as np
import pandas as pd
from datetime import datetime
from pandas import DataFrame
from typing import Optional, Union

from freqtrade.strategy import (
    IStrategy,
    Trade,
    Order,
    PairLocks,
    informative,
    BooleanParameter,
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    RealParameter,
    timeframe_to_minutes,
    timeframe_to_next_date,
    timeframe_to_prev_date,
    merge_informative_pair,
    stoploss_from_absolute,
    stoploss_from_open,
)

import talib.abstract as ta
from technical import qtpylib


class RSIBreakoutStrategy(IStrategy):
    """
    RSI + Breakout + Dashboard Strategy
    
    Based on Pine Script: RSl+Breakout+Dashboard
    
    Strategy Logic:
    - Uses ADX for market regime detection (trending vs ranging)
    - EMA trend filter for bias determination
    - RSI mean reversion in ranging markets
    - Breakout entries in trending markets
    - ATR-based trailing stops
    - Long only strategy with adaptive approach
    """

    INTERFACE_VERSION = 3

    # Can this strategy go short?
    can_short: bool = False

    # Optimal timeframe for the strategy
    timeframe = "1h"

    # Run "populate_indicators()" only for new candle
    process_only_new_candles = True

    # These values can be overridden in the config
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 100

    # Hyperoptable parameters
    # ADX regime detection
    adx_length = IntParameter(10, 20, default=14, space="buy", optimize=True, load=True)
    adx_smooth = IntParameter(10, 20, default=14, space="buy", optimize=True, load=True)
    adx_threshold = RealParameter(15.0, 30.0, default=20.0, space="buy", optimize=True, load=True)
    
    # EMA trend filter
    ema_length = IntParameter(20, 60, default=38, space="buy", optimize=True, load=True)
    
    # RSI parameters
    rsi_length = IntParameter(10, 20, default=14, space="buy", optimize=True, load=True)
    rsi_buy_threshold = IntParameter(25, 45, default=40, space="buy", optimize=True, load=True)
    rsi_sell_threshold = IntParameter(55, 75, default=60, space="sell", optimize=True, load=True)
    rsi_exit_threshold = IntParameter(45, 55, default=50, space="sell", optimize=True, load=True)
    
    # Breakout parameters
    breakout_length = IntParameter(15, 30, default=20, space="buy", optimize=True, load=True)
    
    # ATR parameters
    atr_length = IntParameter(10, 20, default=14, space="buy", optimize=True, load=True)
    atr_multiplier = RealParameter(1.5, 3.0, default=2.0, space="sell", optimize=True, load=True)
    
    # Leverage parameter for futures trading
    leverage_multiplier = RealParameter(2.0, 18.0, default=4.0, space="buy", optimize=True, load=True)

    # Optional order type mapping
    order_types = {
        "entry": "market",
        "exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": True,
    }

    # Optional order time in force
    order_time_in_force = {
        "entry": "GTC",
        "exit": "GTC",
    }

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        """
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several different TA indicators to the given DataFrame
        """
        
        # ADX for regime detection
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=self.adx_length.value)
        dataframe['plus_di'] = ta.PLUS_DI(dataframe, timeperiod=self.adx_length.value)
        dataframe['minus_di'] = ta.MINUS_DI(dataframe, timeperiod=self.adx_length.value)
        
        # Market regime classification
        dataframe['is_trending'] = dataframe['adx'] > self.adx_threshold.value
        dataframe['is_ranging'] = ~dataframe['is_trending']
        
        # EMA trend filter
        dataframe['ema'] = ta.EMA(dataframe['close'], timeperiod=self.ema_length.value)
        dataframe['bullish_bias'] = dataframe['close'] > dataframe['ema']
        dataframe['bearish_bias'] = dataframe['close'] < dataframe['ema']
        
        # RSI for mean reversion
        dataframe['rsi'] = ta.RSI(dataframe['close'], timeperiod=self.rsi_length.value)
        
        # ATR for risk management
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_length.value)
        
        # Breakout levels (using previous candles to avoid lookahead bias)
        dataframe['highest_break'] = dataframe['close'].shift(1).rolling(window=self.breakout_length.value).max()
        dataframe['lowest_break'] = dataframe['close'].shift(1).rolling(window=self.breakout_length.value).min()
        
        # Volume confirmation
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the entry signal for the given dataframe
        """
        
        # RSI Long entry (ranging market)
        rsi_long_condition = (
            (dataframe['is_ranging'])
            & (dataframe['rsi'] < self.rsi_buy_threshold.value)
            & (dataframe['bullish_bias'])
            & (dataframe['volume_ratio'] > 1.0)
        )
        
        # Breakout Long entry (trending market)
        breakout_long_condition = (
            (dataframe['is_trending'])
            & (dataframe['bullish_bias'])
            & (dataframe['close'] > dataframe['highest_break'])
            & (dataframe['volume_ratio'] > 1.2)
        )
        
        # Combined long entry
        dataframe.loc[
            (
                (rsi_long_condition | breakout_long_condition)
                &
                # Make sure we have valid data
                (dataframe['adx'].notna())
                & (dataframe['ema'].notna())
                & (dataframe['rsi'].notna())
                & (dataframe['atr'].notna())
            ),
            'enter_long'] = 1

        # Add entry tags for tracking
        dataframe.loc[rsi_long_condition, 'enter_tag'] = 'rsi_long'
        dataframe.loc[breakout_long_condition, 'enter_tag'] = 'breakout_long'

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the exit signal for the given dataframe
        """
        
        # RSI exit condition
        rsi_exit_condition = (
            (dataframe['rsi'] > self.rsi_exit_threshold.value)
        )
        
        # Trend reversal exit
        trend_exit_condition = (
            (dataframe['bearish_bias'])
            & (dataframe['is_trending'])
        )
        
        # Combined exit conditions
        dataframe.loc[
            (
                (rsi_exit_condition | trend_exit_condition)
                &
                # Make sure we have valid data
                (dataframe['rsi'].notna())
                & (dataframe['ema'].notna())
            ),
            'exit_long'] = 1

        return dataframe

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str | None,
        side: str,
        **kwargs,
    ) -> float:
        """
        Customize leverage for each new trade. This method is only called in futures mode.
        """
        return min(self.leverage_multiplier.value, max_leverage)

    def custom_stoploss(
        self,
        pair: str,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        after_fill: bool,
        **kwargs,
    ) -> Optional[float]:
        """
        Custom stoploss logic using ATR-based trailing stop
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        if last_candle['atr'] is None or pd.isna(last_candle['atr']):
            return None
            
        atr_value = last_candle['atr']
        trail_distance = atr_value * self.atr_multiplier.value
        
        # For long trades, stoploss is below current price
        new_stoploss = (current_rate - trail_distance) / trade.open_rate - 1
            
        # Only update if the new stoploss is better (higher for long trades)
        if trade.stop_loss_pct is None:
            return new_stoploss
        elif new_stoploss > trade.stop_loss_pct:
            return new_stoploss
            
        return None

    def custom_exit(
        self,
        pair: str,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        **kwargs,
    ) -> Optional[Union[str, bool]]:
        """
        Custom exit logic based on entry type and market conditions
        """
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        # Different exit logic based on entry tag
        if hasattr(trade, 'enter_tag'):
            if trade.enter_tag == 'rsi_long':
                # RSI entries: exit when RSI reaches exit threshold
                if last_candle['rsi'] > self.rsi_exit_threshold.value:
                    return "rsi_exit"
                    
            elif trade.enter_tag == 'breakout_long':
                # Breakout entries: use ATR trailing stop (handled in custom_stoploss)
                # Additional exit on trend reversal
                if last_candle['bearish_bias'] and last_candle['is_trending']:
                    return "trend_reversal"
        
        return None
