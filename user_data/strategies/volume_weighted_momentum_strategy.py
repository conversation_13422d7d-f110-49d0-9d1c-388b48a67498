# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class VolumeWeightedMomentumStrategy(IStrategy):
    """
    成交量加权动量策略（VWMI）
    
    策略逻辑：
    1. 计算成交量加权动量指标 VWMI = XAverage(Volume*Momentum(close,4),22)
    2. 多头入场：VWMI上穿零轴，在收盘价+50%ATR处入场
    3. 空头入场：VWMI下穿零轴，在收盘价-50%ATR处入场
    4. 信号有效期：4个bar
    5. 使用ATR动态止损
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True

    # 杠杆参数
    leverage_optimize = IntParameter(2, 18, default=5, space="buy", optimize=True)
    
    # ROI表
    minimal_roi = {
        "0": 0.12,      # 12%止盈
        "60": 0.06,     # 1小时后6%止盈
        "120": 0.04,    # 2小时后4%止盈
        "240": 0.02,    # 4小时后2%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.08  # 8%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.03
    trailing_stop_positive_offset = 0.05
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 50

    # 策略参数
    mom_len = IntParameter(3, 6, default=4, space="buy", optimize=True)         # 动量计算周期
    avg_len = IntParameter(18, 26, default=22, space="buy", optimize=True)      # 成交量加权平均周期
    atr_len = IntParameter(4, 8, default=5, space="buy", optimize=True)         # ATR计算周期
    atr_pcnt = DecimalParameter(0.3, 0.7, default=0.5, space="buy", optimize=True)  # ATR百分比
    setup_len = IntParameter(3, 6, default=4, space="buy", optimize=True)       # 信号有效周期

    # 出场参数 - 保持原始策略逻辑，只添加优化参数
    exit_sensitivity = DecimalParameter(0.8, 1.2, default=1.0, space="sell", optimize=True)  # 出场敏感度调节
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算动量指标
        dataframe['momentum'] = ta.MOM(dataframe['close'], timeperiod=self.mom_len.value)
        
        # 计算成交量加权动量
        dataframe['volume_momentum'] = dataframe['volume'] * dataframe['momentum']
        
        # 计算成交量加权动量指标（VWMI）
        dataframe['vwmi'] = ta.EMA(dataframe['volume_momentum'], timeperiod=self.avg_len.value)
        
        # 计算ATR
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_len.value)
        
        # 计算VWMI穿越零轴的信号
        dataframe['vwmi_cross_above_zero'] = qtpylib.crossed_above(dataframe['vwmi'], 0)
        dataframe['vwmi_cross_below_zero'] = qtpylib.crossed_below(dataframe['vwmi'], 0)
        
        # 初始化设置变量
        dataframe['bull_setup'] = False
        dataframe['bear_setup'] = False
        dataframe['le_price'] = np.nan
        dataframe['se_price'] = np.nan
        dataframe['l_setup'] = 99
        dataframe['s_setup'] = 99
        
        # 计算设置信号和价格
        for i in range(1, len(dataframe)):
            # 多头设置信号
            if dataframe.iloc[i]['vwmi_cross_above_zero']:
                dataframe.iloc[i, dataframe.columns.get_loc('bull_setup')] = True
                dataframe.iloc[i, dataframe.columns.get_loc('le_price')] = (
                    dataframe.iloc[i]['close'] + 
                    dataframe.iloc[i]['atr'] * self.atr_pcnt.value
                )
                dataframe.iloc[i, dataframe.columns.get_loc('l_setup')] = 0
            else:
                # 继承前一个周期的设置
                if dataframe.iloc[i-1]['l_setup'] < self.setup_len.value:
                    dataframe.iloc[i, dataframe.columns.get_loc('l_setup')] = dataframe.iloc[i-1]['l_setup'] + 1
                    dataframe.iloc[i, dataframe.columns.get_loc('le_price')] = dataframe.iloc[i-1]['le_price']
                else:
                    dataframe.iloc[i, dataframe.columns.get_loc('l_setup')] = 99
            
            # 空头设置信号
            if dataframe.iloc[i]['vwmi_cross_below_zero']:
                dataframe.iloc[i, dataframe.columns.get_loc('bear_setup')] = True
                dataframe.iloc[i, dataframe.columns.get_loc('se_price')] = (
                    dataframe.iloc[i]['close'] - 
                    dataframe.iloc[i]['atr'] * self.atr_pcnt.value
                )
                dataframe.iloc[i, dataframe.columns.get_loc('s_setup')] = 0
            else:
                # 继承前一个周期的设置
                if dataframe.iloc[i-1]['s_setup'] < self.setup_len.value:
                    dataframe.iloc[i, dataframe.columns.get_loc('s_setup')] = dataframe.iloc[i-1]['s_setup'] + 1
                    dataframe.iloc[i, dataframe.columns.get_loc('se_price')] = dataframe.iloc[i-1]['se_price']
                else:
                    dataframe.iloc[i, dataframe.columns.get_loc('s_setup')] = 99
        
        # 入场条件
        dataframe['long_entry_condition'] = (
            (dataframe['l_setup'] <= self.setup_len.value) &
            (dataframe['le_price'].notna())
        )
        
        dataframe['short_entry_condition'] = (
            (dataframe['s_setup'] <= self.setup_len.value) &
            (dataframe['se_price'].notna())
        )
        
        # 出场条件 - 保持原始策略逻辑
        dataframe['long_exit_condition'] = dataframe['vwmi_cross_below_zero']
        dataframe['short_exit_condition'] = dataframe['vwmi_cross_above_zero']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：VWMI上穿零轴后在有效期内
        dataframe.loc[
            (
                (dataframe['long_entry_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：VWMI下穿零轴后在有效期内
        dataframe.loc[
            (
                (dataframe['short_entry_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：VWMI下穿零轴
        dataframe.loc[
            (
                (dataframe['long_exit_condition'])
            ),
            'exit_long'] = 1

        # 空头出场：VWMI上穿零轴
        dataframe.loc[
            (
                (dataframe['short_exit_condition'])
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        自定义止损逻辑：基于ATR的动态止损
        """
        
        # 获取当前数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()
        
        if 'atr' in current_candle:
            atr_value = current_candle['atr']
            
            if trade.is_short:
                # 空头止损：入场价格 + ATR * 50%
                stop_loss_pct = (trade.open_rate + atr_value * self.atr_pcnt.value - trade.open_rate) / trade.open_rate
                return max(stop_loss_pct, self.stoploss)
            else:
                # 多头止损：入场价格 - ATR * 50%
                stop_loss_pct = (trade.open_rate - atr_value * self.atr_pcnt.value - trade.open_rate) / trade.open_rate
                return max(stop_loss_pct, self.stoploss)
        
        return self.stoploss

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str | None,
                 side: str, **kwargs) -> float:
        """
        自定义杠杆设置
        """
        return self.leverage_optimize.value
