# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class DelphiAdaptiveMAERSwing4Strategy(IStrategy):
    """
    Delphi自适应移动平均策略（ERSwing4模式）
    
    策略逻辑：
    1. 使用自适应移动平均线作为主要信号
    2. ERSwing4模式：ERSwing的第四个版本，参数进一步优化
    3. 价格穿越均线时产生交易信号
    4. 使用ATR计算动态止损和目标
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表 - ERSwing4模式
    minimal_roi = {
        "0": 0.06,      # 6%止盈
        "60": 0.03,     # 1小时后3%止盈
        "120": 0.018,   # 2小时后1.8%止盈
        "240": 0.008,   # 4小时后0.8%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.045  # 4.5%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.015
    trailing_stop_positive_offset = 0.025
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 150

    # 策略参数 - ERSwing4模式参数
    variable1 = DecimalParameter(0.9, 1.6, default=1.25, space="buy", optimize=True)   # ATR倍数1
    variable2 = DecimalParameter(0.4, 0.8, default=0.6, space="buy", optimize=True)    # ATR倍数2
    variable3 = DecimalParameter(0.06, 0.16, default=0.11, space="buy", optimize=True) # 部分止盈倍数
    variable4 = DecimalParameter(1.6, 2.6, default=2.1, space="buy", optimize=True)    # 目标倍数
    variable5 = DecimalParameter(0.18, 0.38, default=0.28, space="buy", optimize=True) # 止损倍数
    
    # ATR参数
    atr_long_period = IntParameter(30, 40, default=34, space="buy", optimize=True)
    atr_short_period = IntParameter(10, 16, default=13, space="buy", optimize=True)
    
    # 自适应移动平均参数
    ama_period = IntParameter(8, 15, default=10, space="buy", optimize=True)
    
    # ERSwing4特有参数
    efficiency_ratio_period = IntParameter(10, 18, default=13, space="buy", optimize=True)
    er_threshold = DecimalParameter(0.2, 0.5, default=0.35, space="buy", optimize=True)
    swing_multiplier = DecimalParameter(0.7, 1.4, default=1.05, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算ATR
        dataframe['atr_long'] = ta.ATR(dataframe, timeperiod=self.atr_long_period.value)
        dataframe['atr_short'] = ta.ATR(dataframe, timeperiod=self.atr_short_period.value)
        
        # 计算效率比率（Efficiency Ratio）
        dataframe['er'] = self.calculate_efficiency_ratio(dataframe)
        
        # 计算自适应移动平均线
        dataframe['adaptive_ma'] = ta.KAMA(
            dataframe['close'], 
            timeperiod=self.ama_period.value
        )
        
        # 如果KAMA不可用，使用EMA作为备选
        if dataframe['adaptive_ma'].isna().all():
            dataframe['adaptive_ma'] = ta.EMA(dataframe['close'], timeperiod=self.ama_period.value)
        
        # ERSwing4模式：基于效率比率调整摆动通道
        er_adjusted_atr = dataframe['atr_short'] * (1 + dataframe['er'] * self.swing_multiplier.value)
        dataframe['erswing4_upper'] = dataframe['adaptive_ma'] + er_adjusted_atr
        dataframe['erswing4_lower'] = dataframe['adaptive_ma'] - er_adjusted_atr
        
        # 效率比率过滤
        dataframe['high_efficiency'] = dataframe['er'] > self.er_threshold.value
        
        # 计算交易信号
        dataframe['cross_above_ma'] = qtpylib.crossed_above(dataframe['close'], dataframe['adaptive_ma'])
        dataframe['cross_below_ma'] = qtpylib.crossed_below(dataframe['close'], dataframe['adaptive_ma'])
        
        # ERSwing4入场条件
        dataframe['erswing4_long_signal'] = (
            (dataframe['cross_above_ma']) &
            (dataframe['high_efficiency']) &
            (dataframe['high'] >= dataframe['erswing4_upper'])
        )
        
        dataframe['erswing4_short_signal'] = (
            (dataframe['cross_below_ma']) &
            (dataframe['high_efficiency']) &
            (dataframe['low'] <= dataframe['erswing4_lower'])
        )

        return dataframe

    def calculate_efficiency_ratio(self, dataframe: DataFrame) -> DataFrame:
        """
        计算效率比率（Efficiency Ratio）
        ER = Direction / Volatility
        """
        period = self.efficiency_ratio_period.value
        
        # 计算方向（净价格变化）
        direction = abs(dataframe['close'] - dataframe['close'].shift(period))
        
        # 计算波动性（价格变化的总和）
        volatility = dataframe['close'].diff().abs().rolling(window=period).sum()
        
        # 计算效率比率
        efficiency_ratio = direction / volatility
        
        # 处理除零情况
        efficiency_ratio = efficiency_ratio.fillna(0)
        efficiency_ratio = efficiency_ratio.replace([np.inf, -np.inf], 0)
        
        return efficiency_ratio

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：ERSwing4多头信号
        dataframe.loc[
            (
                (dataframe['erswing4_long_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：ERSwing4空头信号
        dataframe.loc[
            (
                (dataframe['erswing4_short_signal']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：价格下穿自适应均线或效率比率下降
        dataframe.loc[
            (
                (dataframe['cross_below_ma']) |
                (~dataframe['high_efficiency'])  # 效率比率下降
            ),
            'exit_long'] = 1

        # 空头出场：价格上穿自适应均线或效率比率下降
        dataframe.loc[
            (
                (dataframe['cross_above_ma']) |
                (~dataframe['high_efficiency'])  # 效率比率下降
            ),
            'exit_short'] = 1

        return dataframe
