# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class MultiMACrossoverStrategy(IStrategy):
    """
    多重移动平均线交叉策略
    
    策略逻辑：
    1. 使用4对不同周期的简单移动平均线
    2. 多头入场：SMA7上穿SMA23，在设置bar最高价+1点入场
    3. 多头出场：SMA4下穿SMA8时平仓
    4. 空头入场：SMA6下穿SMA18，在设置bar最低价-1点入场
    5. 空头出场：SMA2上穿SMA9时平仓
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表
    minimal_roi = {
        "0": 0.10,      # 10%止盈
        "60": 0.05,     # 1小时后5%止盈
        "120": 0.03,    # 2小时后3%止盈
        "240": 0.015,   # 4小时后1.5%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.08  # 8%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.03
    trailing_stop_positive_offset = 0.05
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 50

    # 策略参数 - 可优化的移动平均线周期
    le_fast = IntParameter(5, 10, default=7, space="buy", optimize=True)    # 多头入场快速均线
    le_slow = IntParameter(20, 30, default=23, space="buy", optimize=True)  # 多头入场慢速均线
    lx_fast = IntParameter(3, 6, default=4, space="sell", optimize=True)    # 多头出场快速均线
    lx_slow = IntParameter(6, 12, default=8, space="sell", optimize=True)   # 多头出场慢速均线
    
    se_fast = IntParameter(4, 8, default=6, space="buy", optimize=True)     # 空头入场快速均线
    se_slow = IntParameter(15, 25, default=18, space="buy", optimize=True)  # 空头入场慢速均线
    sx_fast = IntParameter(2, 4, default=2, space="sell", optimize=True)    # 空头出场快速均线
    sx_slow = IntParameter(7, 12, default=9, space="sell", optimize=True)   # 空头出场慢速均线
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算多头入场均线
        dataframe['sma_le_fast'] = ta.SMA(dataframe['close'], timeperiod=self.le_fast.value)
        dataframe['sma_le_slow'] = ta.SMA(dataframe['close'], timeperiod=self.le_slow.value)
        
        # 计算多头出场均线
        dataframe['sma_lx_fast'] = ta.SMA(dataframe['close'], timeperiod=self.lx_fast.value)
        dataframe['sma_lx_slow'] = ta.SMA(dataframe['close'], timeperiod=self.lx_slow.value)
        
        # 计算空头入场均线
        dataframe['sma_se_fast'] = ta.SMA(dataframe['close'], timeperiod=self.se_fast.value)
        dataframe['sma_se_slow'] = ta.SMA(dataframe['close'], timeperiod=self.se_slow.value)
        
        # 计算空头出场均线
        dataframe['sma_sx_fast'] = ta.SMA(dataframe['close'], timeperiod=self.sx_fast.value)
        dataframe['sma_sx_slow'] = ta.SMA(dataframe['close'], timeperiod=self.sx_slow.value)
        
        # 计算均线交叉信号
        # 多头入场信号：快速均线上穿慢速均线
        dataframe['long_entry_cross'] = qtpylib.crossed_above(
            dataframe['sma_le_fast'], dataframe['sma_le_slow']
        )
        
        # 多头出场信号：快速均线下穿慢速均线
        dataframe['long_exit_cross'] = qtpylib.crossed_below(
            dataframe['sma_lx_fast'], dataframe['sma_lx_slow']
        )
        
        # 空头入场信号：快速均线下穿慢速均线
        dataframe['short_entry_cross'] = qtpylib.crossed_below(
            dataframe['sma_se_fast'], dataframe['sma_se_slow']
        )
        
        # 空头出场信号：快速均线上穿慢速均线
        dataframe['short_exit_cross'] = qtpylib.crossed_above(
            dataframe['sma_sx_fast'], dataframe['sma_sx_slow']
        )
        
        # 计算入场价格
        # 多头入场价格：设置bar的最高价 + 1点（这里用0.01%代替1点）
        dataframe['long_entry_price'] = dataframe['high'] * 1.0001
        
        # 空头入场价格：设置bar的最低价 - 1点（这里用0.01%代替1点）
        dataframe['short_entry_price'] = dataframe['low'] * 0.9999
        
        # 趋势确认条件
        dataframe['long_trend_confirm'] = dataframe['sma_le_fast'] > dataframe['sma_le_slow']
        dataframe['short_trend_confirm'] = dataframe['sma_se_fast'] < dataframe['sma_se_slow']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场：SMA7上穿SMA23
        dataframe.loc[
            (
                (dataframe['long_entry_cross']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：SMA6下穿SMA18
        dataframe.loc[
            (
                (dataframe['short_entry_cross']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：SMA4下穿SMA8
        dataframe.loc[
            (
                (dataframe['long_exit_cross'])
            ),
            'exit_long'] = 1

        # 空头出场：SMA2上穿SMA9
        dataframe.loc[
            (
                (dataframe['short_exit_cross'])
            ),
            'exit_short'] = 1

        return dataframe
