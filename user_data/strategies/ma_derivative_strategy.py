# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class MADerivativeStrategy(IStrategy):
    """
    基于移动平均线导数的交易策略
    
    策略逻辑：
    1. 计算移动平均线的一阶导数和二阶导数
    2. 二阶导数上穿零轴时设置买入标志
    3. 一阶导数上穿零轴且买入标志为真时买入
    4. 二阶导数下穿零轴时设置卖出标志
    5. 一阶导数下穿零轴且卖出标志为真时卖出
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表 - 相对保守的设置
    minimal_roi = {
        "0": 0.08,      # 8%止盈
        "60": 0.04,     # 1小时后4%止盈
        "120": 0.02,    # 2小时后2%止盈
        "240": 0.01,    # 4小时后1%止盈
        "480": 0        # 8小时后0%止盈
    }

    # 止损设置
    stoploss = -0.05  # 5%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.03
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 50

    # 可优化参数
    ma_length = IntParameter(5, 20, default=9, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """

        # 计算移动平均线
        dataframe['ma'] = ta.SMA(dataframe['close'], timeperiod=self.ma_length.value)

        # 计算一阶导数 (当前MA - Length周期前的MA) / Length
        dataframe['deriv_ma1'] = (dataframe['ma'] - dataframe['ma'].shift(self.ma_length.value)) / self.ma_length.value

        # 计算二阶导数 (当前一阶导数 - 上一周期一阶导数) / 1
        dataframe['deriv_ma2'] = dataframe['deriv_ma1'] - dataframe['deriv_ma1'].shift(1)

        # 计算导数的零轴穿越条件
        dataframe['deriv1_cross_above_zero'] = qtpylib.crossed_above(dataframe['deriv_ma1'], 0)
        dataframe['deriv1_cross_below_zero'] = qtpylib.crossed_below(dataframe['deriv_ma1'], 0)
        dataframe['deriv2_cross_above_zero'] = qtpylib.crossed_above(dataframe['deriv_ma2'], 0)
        dataframe['deriv2_cross_below_zero'] = qtpylib.crossed_below(dataframe['deriv_ma2'], 0)

        # 简化的买入和卖出条件
        # 买入条件：二阶导数为正且一阶导数上穿零轴
        dataframe['long_condition'] = (
            (dataframe['deriv2_cross_above_zero'].shift(1) == True) &  # 前一根K线二阶导数上穿零轴
            (dataframe['deriv1_cross_above_zero'] == True)  # 当前K线一阶导数上穿零轴
        )

        # 卖出条件：二阶导数为负且一阶导数下穿零轴
        dataframe['short_condition'] = (
            (dataframe['deriv2_cross_below_zero'].shift(1) == True) &  # 前一根K线二阶导数下穿零轴
            (dataframe['deriv1_cross_below_zero'] == True)  # 当前K线一阶导数下穿零轴
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """

        # 多头入场：满足长仓条件
        dataframe.loc[
            (
                (dataframe['long_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场：满足短仓条件
        dataframe.loc[
            (
                (dataframe['short_condition']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号 - 使用相反的入场条件作为出场信号
        """

        # 多头出场：满足短仓条件时
        dataframe.loc[
            (
                (dataframe['short_condition']) &
                (dataframe['volume'] > 0)
            ),
            'exit_long'] = 1

        # 空头出场：满足长仓条件时
        dataframe.loc[
            (
                (dataframe['long_condition']) &
                (dataframe['volume'] > 0)
            ),
            'exit_short'] = 1

        return dataframe
