# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement

from datetime import datetime, time
import talib.abstract as ta
from pandas import DataFrame
import numpy as np

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import (
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    IStrategy,
    RealParameter,
)


class IntradayReversalStrategy(IStrategy):
    """
    日内反转突破策略
    
    策略逻辑：
    1. 基于前一日的高低点和收盘价计算支撑阻力位
    2. 突破阻力位时做空，突破支撑位时做多
    3. 包含反转逻辑和突破逻辑
    4. 仅在指定时间段内交易
    """

    INTERFACE_VERSION = 3

    # 基本参数设置
    timeframe = '1h'
    can_short = True
    
    # ROI表 - 日内交易相对激进
    minimal_roi = {
        "0": 0.06,      # 6%止盈
        "30": 0.03,     # 30分钟后3%止盈
        "60": 0.015,    # 1小时后1.5%止盈
        "120": 0.01,    # 2小时后1%止盈
        "240": 0        # 4小时后0%止盈
    }

    # 止损设置
    stoploss = -0.03  # 3%止损

    # 追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.015
    trailing_stop_positive_offset = 0.025
    trailing_only_offset_is_reached = True

    # 启动蜡烛数量
    startup_candle_count: int = 30

    # 策略参数
    f1 = DecimalParameter(0.2, 0.5, default=0.35, space="buy", optimize=True)
    f2 = DecimalParameter(0.05, 0.15, default=0.07, space="buy", optimize=True)
    f3 = DecimalParameter(0.15, 0.35, default=0.25, space="buy", optimize=True)
    reverse = DecimalParameter(1.5, 3.0, default=2.0, space="buy", optimize=True)
    rangemin = DecimalParameter(0.8, 1.5, default=1.15, space="buy", optimize=True)
    
    # 订单类型
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    def informative_pairs(self):
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 计算日内高低点和收盘价
        dataframe['prev_high'] = dataframe['high'].shift(24)  # 前一日高点（假设1小时K线，24根为一日）
        dataframe['prev_low'] = dataframe['low'].shift(24)    # 前一日低点
        dataframe['prev_close'] = dataframe['close'].shift(24) # 前一日收盘
        
        # 计算日内范围过滤
        dataframe['daily_range'] = dataframe['prev_high'] - dataframe['prev_low']
        dataframe['range_filter'] = dataframe['daily_range'] >= self.rangemin.value
        
        # 计算关键价位
        # ssetup = 昨日高点 + f1*(昨日收盘-昨日低点)
        dataframe['ssetup'] = dataframe['prev_high'] + self.f1.value * (dataframe['prev_close'] - dataframe['prev_low'])
        
        # bsetup = 昨日低点 - f1*(昨日高点-昨日收盘)  
        dataframe['bsetup'] = dataframe['prev_low'] - self.f1.value * (dataframe['prev_high'] - dataframe['prev_close'])
        
        # 入场价格计算
        # senter = ((1+f2)/2)*(昨日高点+昨日收盘)-(f2)*昨日低点
        dataframe['senter'] = ((1 + self.f2.value) / 2) * (dataframe['prev_high'] + dataframe['prev_close']) - self.f2.value * dataframe['prev_low']
        
        # benter = ((1+f2)/2)*(昨日低点+昨日收盘)-(f2)*昨日高点
        dataframe['benter'] = ((1 + self.f2.value) / 2) * (dataframe['prev_low'] + dataframe['prev_close']) - self.f2.value * dataframe['prev_high']
        
        # 突破价格计算
        # bbreak = ssetup + f3*(ssetup-bsetup)
        dataframe['bbreak'] = dataframe['ssetup'] + self.f3.value * (dataframe['ssetup'] - dataframe['bsetup'])
        
        # sbreak = bsetup - f3*(ssetup-bsetup)
        dataframe['sbreak'] = dataframe['bsetup'] - self.f3.value * (dataframe['ssetup'] - dataframe['bsetup'])
        
        # 当日高低点跟踪
        dataframe['today_high'] = dataframe['high'].rolling(window=24, min_periods=1).max()
        dataframe['today_low'] = dataframe['low'].rolling(window=24, min_periods=1).min()
        
        # 交易条件
        # 做空条件：当日高点>=ssetup
        dataframe['short_setup'] = (dataframe['today_high'] >= dataframe['ssetup']) & dataframe['range_filter']
        
        # 做多条件：当日低点<=bsetup  
        dataframe['long_setup'] = (dataframe['today_low'] <= dataframe['bsetup']) & dataframe['range_filter']
        
        # 突破条件
        dataframe['long_break'] = dataframe['high'] >= dataframe['bbreak']
        dataframe['short_break'] = dataframe['low'] <= dataframe['sbreak']
        
        # 时间过滤 (简化版本，使用所有时间)
        dataframe['time_filter'] = True  # 简化为始终允许交易

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        入场信号
        """
        
        # 多头入场条件
        dataframe.loc[
            (
                (
                    # 条件1：突破bsetup后在benter价格做多
                    (dataframe['long_setup']) |
                    # 条件2：突破bbreak做多
                    (dataframe['long_break'])
                ) &
                (dataframe['time_filter']) &
                (dataframe['volume'] > 0)
            ),
            'enter_long'] = 1

        # 空头入场条件
        dataframe.loc[
            (
                (
                    # 条件1：突破ssetup后在senter价格做空
                    (dataframe['short_setup']) |
                    # 条件2：突破sbreak做空
                    (dataframe['short_break'])
                ) &
                (dataframe['time_filter']) &
                (dataframe['volume'] > 0)
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        出场信号
        """
        
        # 多头出场：时间过滤结束或反转条件
        dataframe.loc[
            (
                (~dataframe['time_filter']) |  # 交易时间结束
                (dataframe['short_setup'])     # 出现做空信号时平多
            ),
            'exit_long'] = 1

        # 空头出场：时间过滤结束或反转条件
        dataframe.loc[
            (
                (~dataframe['time_filter']) |  # 交易时间结束
                (dataframe['long_setup'])      # 出现做多信号时平空
            ),
            'exit_short'] = 1

        return dataframe
