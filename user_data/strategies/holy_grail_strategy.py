# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file

import numpy as np
import pandas as pd
from datetime import datetime
from pandas import DataFrame
from typing import Optional, Union

from freqtrade.strategy import (
    IStrategy,
    Trade,
    Order,
    PairLocks,
    informative,
    BooleanParameter,
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    RealParameter,
    timeframe_to_minutes,
    timeframe_to_next_date,
    timeframe_to_prev_date,
    merge_informative_pair,
    stoploss_from_absolute,
    stoploss_from_open,
)

import talib.abstract as ta
from technical import qtpylib


class HolyGrailStrategy(IStrategy):
    """
    Holy Grail Strategy
    
    Based on Pine Script: Holy Grail
    
    Strategy Logic:
    - Uses Simple Moving Average as trend filter
    - Price action based entries and exits
    - Dynamic position sizing with DCA approach
    - Tracks ATH and buy section lows
    - Long only strategy with accumulation on dips
    """

    INTERFACE_VERSION = 3

    # Can this strategy go short?
    can_short: bool = False

    # Optimal timeframe for the strategy
    timeframe = "1h"

    # Run "populate_indicators()" only for new candle
    process_only_new_candles = True

    # These values can be overridden in the config
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 100

    # Hyperoptable parameters - Optimized values
    ma_length = IntParameter(20, 100, default=40, space="buy", optimize=True, load=True)
    init_entry_percentage = RealParameter(10.0, 80.0, default=12.58, space="buy", optimize=True, load=True)
    exit_percentage = RealParameter(1.0, 10.0, default=4.67, space="sell", optimize=True, load=True)

    # Volume confirmation parameters - Optimized values
    volume_factor = RealParameter(0.5, 2.0, default=1.52, space="buy", optimize=True, load=True)

    # Leverage parameter for futures trading - Optimized values
    leverage_multiplier = RealParameter(2.0, 18.0, default=4.17, space="buy", optimize=True, load=True)

    # Optimized ROI table
    minimal_roi = {
        "0": 0.216,      # 立即21.6%止盈
        "216": 0.058,    # 216分钟后5.8%止盈
        "514": 0.028,    # 514分钟后2.8%止盈
        "1679": 0        # 1679分钟后0%止盈
    }

    # Optimized stoploss
    stoploss = -0.2

    # Optimized trailing stop
    trailing_stop = True
    trailing_stop_positive = 0.03
    trailing_stop_positive_offset = 0.05
    trailing_only_offset_is_reached = True

    # Optional order type mapping
    order_types = {
        "entry": "market",
        "exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": True,
    }

    # Optional order time in force
    order_time_in_force = {
        "entry": "GTC",
        "exit": "GTC",
    }

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        """
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several different TA indicators to the given DataFrame
        """
        
        # Simple Moving Average - main trend filter
        dataframe['ma'] = ta.SMA(dataframe['close'], timeperiod=self.ma_length.value)
        
        # Price action indicators
        dataframe['is_green'] = dataframe['close'] > dataframe['open']
        dataframe['is_red'] = dataframe['close'] < dataframe['open']
        
        # Volume indicators
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        
        # ATR for risk management
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        
        # Rolling high and low for trend analysis
        dataframe['rolling_high'] = dataframe['high'].rolling(window=20).max()
        dataframe['rolling_low'] = dataframe['low'].rolling(window=20).min()
        
        # Price position relative to MA
        dataframe['price_above_ma'] = dataframe['close'] > dataframe['ma']
        dataframe['price_below_ma'] = dataframe['close'] < dataframe['ma']
        
        # Crossover detection
        dataframe['cross_under_ma'] = qtpylib.crossed_below(dataframe['close'], dataframe['ma'])
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the entry signal for the given dataframe
        """
        
        # Long entry conditions based on Holy Grail logic
        dataframe.loc[
            (
                # Green candle (bullish)
                (dataframe['is_green'])
                &
                # Price below MA (buying the dip)
                (dataframe['price_below_ma'])
                &
                # Volume confirmation
                (dataframe['volume_ratio'] > self.volume_factor.value)
                &
                # Make sure we have valid data
                (dataframe['ma'].notna())
                & (dataframe['atr'].notna())
                &
                # Additional filter: not in a strong downtrend
                (dataframe['close'] > dataframe['rolling_low'] * 1.02)
            ),
            'enter_long'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the exit signal for the given dataframe
        """
        
        # Long exit conditions based on Holy Grail logic
        dataframe.loc[
            (
                # Red candle (bearish)
                (dataframe['is_red'])
                &
                # Price above MA (taking profit)
                (dataframe['price_above_ma'])
                &
                # Volume confirmation
                (dataframe['volume_ratio'] > self.volume_factor.value)
                &
                # Make sure we have valid data
                (dataframe['ma'].notna())
            ),
            'exit_long'] = 1

        return dataframe

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str | None,
        side: str,
        **kwargs,
    ) -> float:
        """
        Customize leverage for each new trade. This method is only called in futures mode.
        """
        return min(self.leverage_multiplier.value, max_leverage)

    def custom_stake_amount(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_stake: float,
        min_stake: Optional[float],
        max_stake: float,
        leverage: float,
        entry_tag: Optional[str],
        side: str,
        **kwargs,
    ) -> float:
        """
        Customize stake amount for each new trade.
        Implements the Holy Grail's dynamic position sizing logic.
        """
        
        # Get current open trades for this pair
        open_trades = Trade.get_trades_proxy(pair=pair, is_open=True)
        
        if len(open_trades) == 0:
            # Initial entry - use init_entry_percentage
            stake_amount = max_stake * (self.init_entry_percentage.value / 100.0)
        else:
            # Additional entries - use smaller amounts (DCA approach)
            stake_amount = max_stake * 0.2  # 20% of max stake for additional entries
            
        # Ensure we don't exceed limits
        if min_stake is not None:
            stake_amount = max(stake_amount, min_stake)
        stake_amount = min(stake_amount, max_stake)
        
        return stake_amount

    def custom_exit(
        self,
        pair: str,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        **kwargs,
    ) -> Optional[Union[str, bool]]:
        """
        Custom exit logic based on Holy Grail strategy.
        Implements partial exits when conditions are met.
        """
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        # Check for partial exit conditions
        if (last_candle['is_red'] and 
            last_candle['price_above_ma'] and 
            current_profit > 0.01):  # At least 1% profit
            
            # Return partial exit signal
            return f"partial_exit_{self.exit_percentage.value}%"
            
        return None

    def adjust_trade_position(
        self,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        min_stake: Optional[float],
        max_stake: float,
        current_entry_rate: float,
        current_exit_rate: float,
        current_entry_profit: float,
        current_exit_profit: float,
        **kwargs,
    ) -> Optional[float]:
        """
        Adjust trade position based on Holy Grail logic.
        Add to position when price drops below certain thresholds.
        """
        
        # Only add to losing positions (DCA approach)
        if current_profit >= -0.02:  # Don't add if loss is less than 2%
            return None
            
        # Get dataframe for analysis
        dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        # Add to position if we have a green candle below MA
        if (last_candle['is_green'] and 
            last_candle['price_below_ma'] and
            current_profit < -0.05):  # Only if we're down more than 5%
            
            # Calculate additional stake amount
            additional_stake = min_stake if min_stake else max_stake * 0.1
            return additional_stake
            
        return None
