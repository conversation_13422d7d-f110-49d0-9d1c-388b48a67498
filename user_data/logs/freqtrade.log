2025-08-07 14:10:13,565 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 1 times.
2025-08-07 14:10:23,900 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Giving up.
2025-08-07 14:10:23,901 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1412, in _start_tls_connection
    tls_transport = await self._loop.start_tls(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 1348, in start_tls
    await waiter
ConnectionAbortedError: SSL handshake is taking longer than 10.0 seconds: aborting the connection

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1677, in _create_proxy_connection
    return await self._start_tls_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1445, in _start_tls_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host dapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 682, in reload_markets
    retrier(self._load_async_markets, retries=retries)(reload=True)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo
2025-08-07 14:10:23,910 - freqtrade - ERROR - Could not load markets, therefore cannot start. Please investigate the above error for more details.
2025-08-07 14:10:28,095 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:10:28,096 - root - INFO - Logfile configured
2025-08-07 14:10:28,097 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:10:28,098 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:10:28,099 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:10:28,099 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:10:28,100 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:10:28,100 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:10:28,117 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:10:28,118 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:10:28,120 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:10:28,136 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:10:28,137 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:10:28,224 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:10:28,225 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:10:28,226 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:10:28,227 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:10:28,227 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:10:28,228 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:10:28,228 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:10:28,229 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:10:28,230 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:10:28,231 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:10:28,231 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:10:28,232 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:10:28,232 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:10:28,233 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:10:28,233 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:10:28,234 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:10:28,234 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:10:28,236 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:10:28,236 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:10:28,237 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:10:28,238 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:10:28,239 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:10:28,239 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:10:28,240 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:10:28,240 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:10:28,241 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:10:28,242 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:10:28,243 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:10:28,243 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:10:28,244 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:10:28,245 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:10:28,245 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:10:28,246 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:10:28,247 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:10:28,247 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:10:28,248 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:10:28,249 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:10:28,249 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:10:28,250 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:10:28,250 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:10:28,251 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:10:28,251 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:10:28,252 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:10:28,253 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:10:28,257 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:10:28,257 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:10:28,258 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-08-07 14:10:28,270 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-08-07 14:10:28,283 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 14:10:38,593 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to RequestTimeout. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Retrying still for 3 times.
2025-08-07 14:10:49,594 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to RequestTimeout. Message: binance GET https://fapi.binance.com/fapi/v1/exchangeInfo". Retrying still for 2 times.
2025-08-07 14:11:00,591 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to RequestTimeout. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Retrying still for 1 times.
2025-08-07 14:11:11,595 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to RequestTimeout. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Giving up.
2025-08-07 14:11:11,597 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1189, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 87, in start_connection
    sock, _, _ = await _staggered.staggered_race(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/_staggered.py", line 165, in staggered_race
    done = await _wait_one(
           ^^^^^^^^^^^^^^^^
        (*tasks, start_next) if start_next else tasks, loop
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/_staggered.py", line 46, in _wait_one
    return await wait_next
           ^^^^^^^^^^^^^^^
asyncio.exceptions.CancelledError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 622, in _request
    with timer:
         ^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/helpers.py", line 685, in __exit__
    raise asyncio.TimeoutError from exc_val
TimeoutError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 242, in fetch
    raise RequestTimeout(details) from e
ccxt.base.errors.RequestTimeout: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to RequestTimeout. Message: binance GET https://api.binance.com/api/v3/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1189, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 87, in start_connection
    sock, _, _ = await _staggered.staggered_race(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/_staggered.py", line 165, in staggered_race
    done = await _wait_one(
           ^^^^^^^^^^^^^^^^
        (*tasks, start_next) if start_next else tasks, loop
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/_staggered.py", line 46, in _wait_one
    return await wait_next
           ^^^^^^^^^^^^^^^
asyncio.exceptions.CancelledError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 622, in _request
    with timer:
         ^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/helpers.py", line 685, in __exit__
    raise asyncio.TimeoutError from exc_val
TimeoutError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 242, in fetch
    raise RequestTimeout(details) from e
ccxt.base.errors.RequestTimeout: binance GET https://fapi.binance.com/fapi/v1/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to RequestTimeout. Message: binance GET https://fapi.binance.com/fapi/v1/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1189, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 87, in start_connection
    sock, _, _ = await _staggered.staggered_race(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/_staggered.py", line 165, in staggered_race
    done = await _wait_one(
           ^^^^^^^^^^^^^^^^
        (*tasks, start_next) if start_next else tasks, loop
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/_staggered.py", line 46, in _wait_one
    return await wait_next
           ^^^^^^^^^^^^^^^
asyncio.exceptions.CancelledError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 622, in _request
    with timer:
         ^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/helpers.py", line 685, in __exit__
    raise asyncio.TimeoutError from exc_val
TimeoutError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 242, in fetch
    raise RequestTimeout(details) from e
ccxt.base.errors.RequestTimeout: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to RequestTimeout. Message: binance GET https://api.binance.com/api/v3/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1189, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 87, in start_connection
    sock, _, _ = await _staggered.staggered_race(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<13 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/_staggered.py", line 165, in staggered_race
    done = await _wait_one(
           ^^^^^^^^^^^^^^^^
        (*tasks, start_next) if start_next else tasks, loop
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/_staggered.py", line 46, in _wait_one
    return await wait_next
           ^^^^^^^^^^^^^^^
asyncio.exceptions.CancelledError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 622, in _request
    with timer:
         ^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/helpers.py", line 685, in __exit__
    raise asyncio.TimeoutError from exc_val
TimeoutError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 242, in fetch
    raise RequestTimeout(details) from e
ccxt.base.errors.RequestTimeout: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 682, in reload_markets
    retrier(self._load_async_markets, retries=retries)(reload=True)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to RequestTimeout. Message: binance GET https://api.binance.com/api/v3/exchangeInfo
2025-08-07 14:11:11,657 - freqtrade - ERROR - Could not load markets, therefore cannot start. Please investigate the above error for more details.
2025-08-07 14:11:17,740 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:11:17,741 - root - INFO - Logfile configured
2025-08-07 14:11:17,741 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:11:17,742 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:11:17,742 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:11:17,743 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:11:17,743 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:11:17,744 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:11:17,757 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:11:17,758 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:11:17,759 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:11:17,772 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:11:17,772 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:11:17,876 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:11:17,877 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:11:17,878 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:11:17,878 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:11:17,879 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:11:17,879 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:11:17,880 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:11:17,881 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:11:17,881 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:11:17,882 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:11:17,882 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:11:17,883 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:11:17,883 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:11:17,884 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:11:17,884 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:11:17,885 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:11:17,885 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:11:17,886 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:11:17,886 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:11:17,887 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:11:17,887 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:11:17,888 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:11:17,888 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:11:17,888 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:11:17,889 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:11:17,889 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:11:17,890 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:11:17,890 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:11:17,890 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:11:17,891 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:11:17,891 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:11:17,892 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:11:17,892 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:11:17,893 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:11:17,893 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:11:17,894 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:11:17,895 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:11:17,895 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:11:17,896 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:11:17,896 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:11:17,896 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:11:17,897 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:11:17,897 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:11:17,898 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:11:17,901 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:11:17,901 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:11:17,902 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:11:17,912 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:11:17,929 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 14:11:28,248 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://fapi.binance.com/fapi/v1/exchangeInfo". Retrying still for 3 times.
2025-08-07 14:11:28,397 - freqtrade - INFO - SIGINT received, aborting ...
2025-08-07 14:16:34,364 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:16:34,365 - root - INFO - Logfile configured
2025-08-07 14:16:34,366 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:16:34,366 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:16:34,367 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:16:34,367 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:16:34,368 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:16:34,369 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:16:34,437 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:16:34,438 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:16:34,440 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:16:34,453 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:16:34,453 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:16:34,608 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:16:34,609 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:16:34,610 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:16:34,611 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:16:34,611 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:16:34,612 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:16:34,612 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:16:34,613 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:16:34,613 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:16:34,614 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:16:34,614 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:16:34,615 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:16:34,615 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:16:34,616 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:16:34,616 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:16:34,617 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:16:34,617 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:16:34,618 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:16:34,618 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:16:34,618 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:16:34,619 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:16:34,619 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:16:34,620 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:16:34,621 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:16:34,622 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:16:34,623 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:16:34,624 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:16:34,624 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:16:34,625 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:16:34,626 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:16:34,626 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:16:34,627 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:16:34,628 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:16:34,629 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:16:34,629 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:16:34,630 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:16:34,631 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:16:34,632 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:16:34,632 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:16:34,633 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:16:34,634 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:16:34,637 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:16:34,637 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:16:34,638 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:16:34,643 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:16:34,643 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:16:34,645 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:16:34,657 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:16:34,671 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 14:16:36,733 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Retrying still for 3 times.
2025-08-07 14:16:38,855 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Retrying still for 2 times.
2025-08-07 14:16:40,930 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Retrying still for 1 times.
2025-08-07 14:16:43,011 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Giving up.
2025-08-07 14:16:43,012 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 682, in reload_markets
    retrier(self._load_async_markets, retries=retries)(reload=True)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo
2025-08-07 14:16:43,081 - freqtrade - ERROR - Could not load markets, therefore cannot start. Please investigate the above error for more details.
2025-08-07 14:16:47,164 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:16:47,165 - root - INFO - Logfile configured
2025-08-07 14:16:47,165 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:16:47,166 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:16:47,167 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:16:47,167 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:16:47,168 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:16:47,168 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:16:47,183 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:16:47,183 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:16:47,185 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:16:47,198 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:16:47,199 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:16:47,276 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:16:47,277 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:16:47,278 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:16:47,278 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:16:47,279 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:16:47,279 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:16:47,280 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:16:47,280 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:16:47,281 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:16:47,282 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:16:47,282 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:16:47,283 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:16:47,283 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:16:47,284 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:16:47,284 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:16:47,285 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:16:47,286 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:16:47,287 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:16:47,287 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:16:47,288 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:16:47,288 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:16:47,289 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:16:47,289 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:16:47,290 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:16:47,290 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:16:47,291 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:16:47,291 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:16:47,292 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:16:47,292 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:16:47,293 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:16:47,293 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:16:47,294 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:16:47,294 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:16:47,295 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:16:47,296 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:16:47,296 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:16:47,297 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:16:47,297 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:16:47,297 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:16:47,298 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:16:47,299 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:16:47,299 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:16:47,300 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:16:47,300 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:16:47,304 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:16:47,305 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:16:47,305 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:16:47,316 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:16:47,329 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 14:16:49,392 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Retrying still for 3 times.
2025-08-07 14:16:51,486 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Retrying still for 2 times.
2025-08-07 14:16:53,579 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Retrying still for 1 times.
2025-08-07 14:16:55,664 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Giving up.
2025-08-07 14:16:55,665 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 682, in reload_markets
    retrier(self._load_async_markets, retries=retries)(reload=True)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo
2025-08-07 14:16:55,817 - freqtrade - ERROR - Could not load markets, therefore cannot start. Please investigate the above error for more details.
2025-08-07 14:17:01,037 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:17:01,038 - root - INFO - Logfile configured
2025-08-07 14:17:01,039 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:17:01,040 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:17:01,041 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:17:01,041 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:17:01,042 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:17:01,042 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:17:01,071 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:17:01,072 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:17:01,077 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:17:01,095 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:17:01,096 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:17:01,196 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:17:01,198 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:17:01,199 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:17:01,199 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:17:01,200 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:17:01,200 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:17:01,201 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:17:01,201 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:17:01,202 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:17:01,202 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:17:01,203 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:17:01,203 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:17:01,204 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:17:01,204 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:17:01,205 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:17:01,205 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:17:01,206 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:17:01,206 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:17:01,207 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:17:01,207 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:17:01,208 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:17:01,208 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:17:01,208 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:17:01,209 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:17:01,209 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:17:01,210 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:17:01,210 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:17:01,210 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:17:01,211 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:17:01,212 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:17:01,212 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:17:01,213 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:17:01,213 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:17:01,214 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:17:01,214 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:17:01,214 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:17:01,215 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:17:01,216 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:17:01,216 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:17:01,217 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:17:01,217 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:17:01,218 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:17:01,218 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:17:01,219 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:17:01,223 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:17:01,223 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:17:01,224 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:17:01,236 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:17:01,251 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 14:17:03,293 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Retrying still for 3 times.
2025-08-07 14:17:05,360 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Retrying still for 2 times.
2025-08-07 14:17:07,445 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Retrying still for 1 times.
2025-08-07 14:17:09,535 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo". Giving up.
2025-08-07 14:17:09,537 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1248, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 122, in start_connection
    raise first_exception
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohappyeyeballs/impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 641, in sock_connect
    return await fut
           ^^^^^^^^^
  File "/usr/local/lib/python3.13/asyncio/selector_events.py", line 681, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 111] Connect call failed ('*************', 12334)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1586, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_req, [], timeout, client_error=ClientProxyConnectionError
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host *************:12334 ssl:default [Connect call failed ('*************', 12334)]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://api.binance.com/api/v3/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 682, in reload_markets
    retrier(self._load_async_markets, retries=retries)(reload=True)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://api.binance.com/api/v3/exchangeInfo
2025-08-07 14:17:09,591 - freqtrade - ERROR - Could not load markets, therefore cannot start. Please investigate the above error for more details.
2025-08-07 14:17:13,588 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:17:13,588 - root - INFO - Logfile configured
2025-08-07 14:17:13,589 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:17:13,590 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:17:13,591 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:17:13,591 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:17:13,592 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:17:13,592 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:17:13,608 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:17:13,609 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:17:13,610 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:17:13,626 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:17:13,627 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:17:13,700 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:17:13,701 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:17:13,702 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:17:13,703 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:17:13,703 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:17:13,704 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:17:13,705 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:17:13,705 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:17:13,705 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:17:13,706 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:17:13,707 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:17:13,708 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:17:13,709 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:17:13,709 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:17:13,710 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:17:13,710 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:17:13,711 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:17:13,712 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:17:13,712 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:17:13,713 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:17:13,713 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:17:13,714 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:17:13,715 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:17:13,716 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:17:13,716 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:17:13,717 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:17:13,717 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:17:13,718 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:17:13,719 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:17:13,719 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:17:13,720 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:17:13,721 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:17:13,721 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:17:13,722 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:17:13,722 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:17:13,723 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:17:13,723 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:17:13,724 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:17:13,724 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:17:13,725 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:17:13,726 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:17:13,726 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:17:13,727 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:17:13,727 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:17:13,731 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:17:13,731 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:17:13,732 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:17:13,742 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:17:13,754 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 14:17:15,094 - freqtrade - INFO - SIGINT received, aborting ...
2025-08-07 14:18:47,775 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:18:47,776 - root - INFO - Logfile configured
2025-08-07 14:18:47,776 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:18:47,777 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:18:47,778 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:18:47,778 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:18:47,779 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:18:47,780 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:18:47,822 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:18:47,822 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:18:47,824 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:18:47,839 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:18:47,840 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:18:47,938 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:18:47,939 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:18:47,940 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:18:47,941 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:18:47,941 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:18:47,942 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:18:47,942 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:18:47,943 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:18:47,944 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:18:47,944 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:18:47,945 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:18:47,946 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:18:47,947 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:18:47,948 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:18:47,949 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:18:47,949 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:18:47,950 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:18:47,951 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:18:47,952 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:18:47,952 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:18:47,953 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:18:47,953 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:18:47,954 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:18:47,954 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:18:47,955 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:18:47,956 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:18:47,956 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:18:47,957 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:18:47,958 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:18:47,958 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:18:47,959 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:18:47,959 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:18:47,960 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:18:47,960 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:18:47,961 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:18:47,961 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:18:47,963 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:18:47,964 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:18:47,964 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:18:47,965 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:18:47,966 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:18:47,966 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:18:47,967 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:18:47,968 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:18:47,974 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:18:47,975 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:18:47,976 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:18:47,991 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:18:48,015 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 14:18:58,306 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 3 times.
2025-08-07 14:19:08,652 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 2 times.
2025-08-07 14:19:19,107 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 1 times.
2025-08-07 14:19:29,578 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Giving up.
2025-08-07 14:19:29,579 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1412, in _start_tls_connection
    tls_transport = await self._loop.start_tls(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 1348, in start_tls
    await waiter
ConnectionAbortedError: SSL handshake is taking longer than 10.0 seconds: aborting the connection

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1677, in _create_proxy_connection
    return await self._start_tls_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1445, in _start_tls_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host dapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 682, in reload_markets
    retrier(self._load_async_markets, retries=retries)(reload=True)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo
2025-08-07 14:19:29,590 - freqtrade - ERROR - Could not load markets, therefore cannot start. Please investigate the above error for more details.
2025-08-07 14:19:33,520 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:19:33,521 - root - INFO - Logfile configured
2025-08-07 14:19:33,521 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:19:33,522 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:19:33,523 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:19:33,523 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:19:33,523 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:19:33,524 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:19:33,537 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:19:33,537 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:19:33,539 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:19:33,550 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:19:33,551 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:19:33,623 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:19:33,624 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:19:33,625 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:19:33,626 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:19:33,626 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:19:33,626 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:19:33,627 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:19:33,628 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:19:33,628 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:19:33,629 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:19:33,629 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:19:33,630 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:19:33,630 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:19:33,631 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:19:33,631 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:19:33,632 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:19:33,632 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:19:33,633 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:19:33,633 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:19:33,634 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:19:33,634 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:19:33,635 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:19:33,635 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:19:33,636 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:19:33,637 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:19:33,638 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:19:33,639 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:19:33,639 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:19:33,640 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:19:33,640 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:19:33,641 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:19:33,641 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:19:33,642 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:19:33,642 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:19:33,643 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:19:33,643 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:19:33,644 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:19:33,644 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:19:33,645 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:19:33,645 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:19:33,646 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:19:33,647 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:19:33,647 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:19:33,648 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:19:33,651 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:19:33,652 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:19:33,653 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:19:33,665 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:19:33,677 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 14:19:43,976 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 3 times.
2025-08-07 14:19:54,311 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 2 times.
2025-08-07 14:20:04,651 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 1 times.
2025-08-07 14:20:14,991 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Giving up.
2025-08-07 14:20:14,992 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1412, in _start_tls_connection
    tls_transport = await self._loop.start_tls(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 1348, in start_tls
    await waiter
ConnectionAbortedError: SSL handshake is taking longer than 10.0 seconds: aborting the connection

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1677, in _create_proxy_connection
    return await self._start_tls_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1445, in _start_tls_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host dapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 682, in reload_markets
    retrier(self._load_async_markets, retries=retries)(reload=True)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo
2025-08-07 14:20:15,005 - freqtrade - ERROR - Could not load markets, therefore cannot start. Please investigate the above error for more details.
2025-08-07 14:20:18,501 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:20:18,501 - root - INFO - Logfile configured
2025-08-07 14:20:18,502 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:20:18,502 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:20:18,503 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:20:18,503 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:20:18,504 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:20:18,505 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:20:18,551 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:20:18,551 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:20:18,553 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:20:18,565 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:20:18,565 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:20:18,633 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:20:18,633 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:20:18,634 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:20:18,635 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:20:18,635 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:20:18,636 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:20:18,637 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:20:18,637 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:20:18,638 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:20:18,638 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:20:18,639 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:20:18,639 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:20:18,640 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:20:18,640 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:20:18,641 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:20:18,642 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:20:18,642 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:20:18,643 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:20:18,643 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:20:18,644 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:20:18,644 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:20:18,645 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:20:18,645 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:20:18,646 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:20:18,646 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:20:18,647 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:20:18,647 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:20:18,648 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:20:18,648 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:20:18,649 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:20:18,649 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:20:18,650 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:20:18,650 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:20:18,651 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:20:18,652 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:20:18,652 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:20:18,652 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:20:18,653 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:20:18,653 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:20:18,654 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:20:18,655 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:20:18,655 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:20:18,655 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:20:18,656 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:20:18,659 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:20:18,660 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:20:18,660 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:20:18,671 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:20:18,682 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 14:20:28,972 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 3 times.
2025-08-07 14:20:39,314 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 2 times.
2025-08-07 14:20:49,697 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 1 times.
2025-08-07 14:21:00,026 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Giving up.
2025-08-07 14:21:00,027 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1412, in _start_tls_connection
    tls_transport = await self._loop.start_tls(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 1348, in start_tls
    await waiter
ConnectionAbortedError: SSL handshake is taking longer than 10.0 seconds: aborting the connection

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1677, in _create_proxy_connection
    return await self._start_tls_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1445, in _start_tls_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host dapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 682, in reload_markets
    retrier(self._load_async_markets, retries=retries)(reload=True)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo
2025-08-07 14:21:00,037 - freqtrade - ERROR - Could not load markets, therefore cannot start. Please investigate the above error for more details.
2025-08-07 14:21:03,603 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:21:03,604 - root - INFO - Logfile configured
2025-08-07 14:21:03,605 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:21:03,606 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:21:03,607 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:21:03,607 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:21:03,608 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:21:03,609 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:21:03,626 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:21:03,628 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:21:03,630 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:21:03,644 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:21:03,646 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:21:03,745 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:21:03,745 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:21:03,747 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:21:03,747 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:21:03,748 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:21:03,748 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:21:03,749 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:21:03,749 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:21:03,750 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:21:03,750 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:21:03,751 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:21:03,752 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:21:03,753 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:21:03,753 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:21:03,754 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:21:03,756 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:21:03,756 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:21:03,757 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:21:03,758 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:21:03,758 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:21:03,759 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:21:03,760 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:21:03,761 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:21:03,761 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:21:03,762 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:21:03,763 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:21:03,763 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:21:03,764 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:21:03,764 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:21:03,765 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:21:03,766 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:21:03,767 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:21:03,768 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:21:03,768 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:21:03,769 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:21:03,770 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:21:03,771 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:21:03,771 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:21:03,772 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:21:03,773 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:21:03,774 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:21:03,775 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:21:03,777 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:21:03,779 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:21:03,783 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:21:03,784 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:21:03,785 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:21:03,799 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:21:03,811 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 14:21:14,126 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 3 times.
2025-08-07 14:21:24,480 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 2 times.
2025-08-07 14:21:34,824 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to RequestTimeout. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 1 times.
2025-08-07 14:21:45,166 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Giving up.
2025-08-07 14:21:45,168 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1412, in _start_tls_connection
    tls_transport = await self._loop.start_tls(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 1348, in start_tls
    await waiter
ConnectionAbortedError: SSL handshake is taking longer than 10.0 seconds: aborting the connection

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1677, in _create_proxy_connection
    return await self._start_tls_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1445, in _start_tls_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host dapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 682, in reload_markets
    retrier(self._load_async_markets, retries=retries)(reload=True)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo
2025-08-07 14:21:45,185 - freqtrade - ERROR - Could not load markets, therefore cannot start. Please investigate the above error for more details.
2025-08-07 14:21:48,912 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:21:48,913 - root - INFO - Logfile configured
2025-08-07 14:21:48,913 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:21:48,914 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:21:48,915 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:21:48,915 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:21:48,916 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:21:48,916 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:21:48,957 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:21:48,958 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:21:48,959 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:21:48,972 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:21:48,973 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:21:49,037 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:21:49,038 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:21:49,039 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:21:49,040 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:21:49,040 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:21:49,041 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:21:49,041 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:21:49,042 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:21:49,043 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:21:49,043 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:21:49,045 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:21:49,045 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:21:49,046 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:21:49,047 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:21:49,048 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:21:49,048 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:21:49,049 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:21:49,050 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:21:49,050 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:21:49,051 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:21:49,052 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:21:49,053 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:21:49,053 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:21:49,054 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:21:49,054 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:21:49,055 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:21:49,056 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:21:49,057 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:21:49,058 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:21:49,059 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:21:49,060 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:21:49,061 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:21:49,061 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:21:49,062 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:21:49,063 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:21:49,064 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:21:49,064 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:21:49,065 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:21:49,065 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:21:49,066 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:21:49,066 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:21:49,067 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:21:49,068 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:21:49,069 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:21:49,072 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:21:49,073 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:21:49,073 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:21:49,084 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:21:49,096 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 14:21:59,398 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 3 times.
2025-08-07 14:22:09,732 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 2 times.
2025-08-07 14:22:20,071 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Retrying still for 1 times.
2025-08-07 14:22:30,403 - freqtrade.exchange.common - WARNING - _load_async_markets() returned exception: "Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo". Giving up.
2025-08-07 14:22:30,405 - freqtrade.exchange.exchange - ERROR - Could not load markets.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1412, in _start_tls_connection
    tls_transport = await self._loop.start_tls(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 1348, in start_tls
    await waiter
ConnectionAbortedError: SSL handshake is taking longer than 10.0 seconds: aborting the connection

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              data=encoded_body,
                              ^^^^^^^^^^^^^^^^^^
                              headers=request_headers,
                              ^^^^^^^^^^^^^^^^^^^^^^^^
                              timeout=(self.timeout / 1000),
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                              proxy=final_proxy) as response:
                              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        req, traces=traces, timeout=real_timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1187, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1677, in _create_proxy_connection
    return await self._start_tls_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/home/<USER>/.local/lib/python3.13/site-packages/aiohttp/connector.py", line 1445, in _start_tls_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host dapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 642, in _api_reload_markets
    await self._api_async.load_markets(reload=reload, params={})
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 327, in load_markets
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 3053, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/binance.py", line 11337, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 932, in fetch2
    raise e
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange.py", line 682, in reload_markets
    retrier(self._load_async_markets, retries=retries)(reload=True)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 184, in wrapper
    return wrapper(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/common.py", line 187, in wrapper
    raise ex
  File "/freqtrade/freqtrade/exchange/common.py", line 172, in wrapper
    return f(*args, **kwargs)
  File "/freqtrade/freqtrade/exchange/exchange.py", line 655, in _load_async_markets
    markets = self.loop.run_until_complete(self._api_reload_markets(reload=reload))
  File "/usr/local/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/freqtrade/freqtrade/exchange/exchange.py", line 646, in _api_reload_markets
    raise TemporaryError(
        f"Error in reload_markets due to {e.__class__.__name__}. Message: {e}"
    ) from e
freqtrade.exceptions.TemporaryError: Error in reload_markets due to ExchangeNotAvailable. Message: binance GET https://dapi.binance.com/dapi/v1/exchangeInfo
2025-08-07 14:22:30,421 - freqtrade - ERROR - Could not load markets, therefore cannot start. Please investigate the above error for more details.
2025-08-07 14:22:34,272 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 14:22:34,273 - root - INFO - Logfile configured
2025-08-07 14:22:34,273 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 14:22:34,274 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 14:22:34,274 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 14:22:34,275 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 14:22:34,275 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-07 14:22:34,276 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 14:22:34,289 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 14:22:34,290 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 14:22:34,291 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 14:22:34,303 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 14:22:34,304 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 14:22:34,369 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy ADXSwingBreakoutStrategy from '/freqtrade/user_data/strategies/adx_swing_breakout_strategy.py'...
2025-08-07 14:22:34,370 - freqtrade.strategy.hyper - INFO - Loading parameters from file /freqtrade/user_data/strategies/adx_swing_breakout_strategy.json
2025-08-07 14:22:34,371 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'minimal_roi' with value in config file: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}.
2025-08-07 14:22:34,371 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 30m.
2025-08-07 14:22:34,372 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stoploss' with value in config file: -0.23.
2025-08-07 14:22:34,372 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop' with value in config file: True.
2025-08-07 14:22:34,373 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive' with value in config file: 0.019.
2025-08-07 14:22:34,373 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_stop_positive_offset' with value in config file: 0.061.
2025-08-07 14:22:34,374 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'trailing_only_offset_is_reached' with value in config file: True.
2025-08-07 14:22:34,374 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_types' with value in config file: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}.
2025-08-07 14:22:34,375 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'order_time_in_force' with value in config file: {'entry': 'GTC', 'exit': 'GTC'}.
2025-08-07 14:22:34,375 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 14:22:34,376 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 14:22:34,376 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 14:22:34,377 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'use_exit_signal' with value in config file: True.
2025-08-07 14:22:34,377 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'exit_profit_only' with value in config file: False.
2025-08-07 14:22:34,378 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'ignore_roi_if_entry_signal' with value in config file: False.
2025-08-07 14:22:34,378 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'position_adjustment_enable' with value in config file: False.
2025-08-07 14:22:34,378 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 14:22:34,379 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.389, '100': 0.152, '337': 0.046, '949': 0}
2025-08-07 14:22:34,380 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 30m
2025-08-07 14:22:34,380 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.23
2025-08-07 14:22:34,380 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-08-07 14:22:34,381 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.019
2025-08-07 14:22:34,381 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.061
2025-08-07 14:22:34,382 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: True
2025-08-07 14:22:34,382 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 14:22:34,383 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 14:22:34,383 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'market', 'exit': 'market', 'stoploss': 'market', 'stoploss_on_exchange': True, 'stoploss_price_type': 'last'}
2025-08-07 14:22:34,383 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 14:22:34,384 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 14:22:34,384 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 14:22:34,385 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 20
2025-08-07 14:22:34,385 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 14:22:34,385 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 14:22:34,386 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 14:22:34,386 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 14:22:34,387 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 14:22:34,387 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 14:22:34,387 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 14:22:34,388 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 14:22:34,388 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 14:22:34,389 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 14:22:34,389 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 14:22:34,392 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 14:22:34,393 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.91
2025-08-07 14:22:34,393 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:22:34,403 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'httpsProxy': 'http://*************:12334', 'wsProxy': 'http://*************:12334'}
2025-08-07 14:22:34,415 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
