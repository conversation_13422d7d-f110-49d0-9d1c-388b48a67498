    # Buy hyperspace params:
    buy_params = {
        "adx_base_level": 21,
        "adx_increment": 17,
        "adx_length": 27,
        "adx_trend_threshold": 32,
        "breakout_multiplier": 1.0,
        "leverage_optimize": 6,
        "swing_length_max": 5,
        "swing_length_min": 3,
        "volume_threshold": 1.508,
    }

    # Sell hyperspace params:
    sell_params = {
        "exit_adx_threshold": 30,
        "profit_target": 0.07,
        "stoploss_adx_threshold": 19,
        "trailing_stop_multiplier": 1.538,
    }

    # ROI table:  # value loaded from strategy
    minimal_roi = {
        "0": 0.05
    }

    # Stoploss:
    stoploss = -0.2  # value loaded from strategy

    # Trailing stop:
    trailing_stop = True  # value loaded from strategy
    trailing_stop_positive = 0.02  # value loaded from strategy
    trailing_stop_positive_offset = 0.05  # value loaded from strategy
    trailing_only_offset_is_reached = True  # value loaded from strategy
    

    # Max Open Trades:
    max_open_trades = 3  # value loaded from strategy