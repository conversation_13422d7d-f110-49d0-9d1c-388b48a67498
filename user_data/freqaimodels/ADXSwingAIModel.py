import logging
from typing import Any, Dict

import pandas as pd
from pandas import DataFrame

from freqtrade.freqai.base_models.BaseRegressionModel import BaseRegressionModel
from freqtrade.freqai.data_kitchen import FreqaiDataKitchen

logger = logging.getLogger(__name__)


class ADXSwingAIModel(BaseRegressionModel):
    """
    ADX摆动突破策略的FreqAI模型
    
    这个模型专门为ADX摆动突破策略设计，预测价格变化方向和强度
    """

    def fit(self, data_dictionary: Dict, dk: FreqaiDataKitchen, **kwargs) -> Any:
        """
        训练模型
        """
        
        X = data_dictionary["train_features"]
        y = data_dictionary["train_labels"]
        
        if self.freqai_info.get('data_split_parameters', {}).get('test_size', 0.1) == 0:
            eval_set = None
        else:
            eval_set = [(data_dictionary["test_features"], data_dictionary["test_labels"])]
        
        sample_weight = data_dictionary["train_weights"]
        
        model = self.get_init_model(dk.pair)
        
        model.fit(X=X, y=y, eval_set=eval_set, sample_weight=sample_weight, 
                 verbose=100, early_stopping_rounds=20)
        
        return model

    def predict(
        self, unfiltered_dataframe: DataFrame, dk: FreqaiDataKitchen, **kwargs
    ) -> tuple[DataFrame, DataFrame]:
        """
        进行预测
        """
        
        # 过滤特征
        filtered_dataframe, _ = dk.filter_features(
            unfiltered_dataframe, dk.training_features_list, training_filter=False
        )
        
        # 进行预测
        predictions = self.model.predict(filtered_dataframe)
        
        # 创建预测DataFrame
        pred_df = DataFrame(predictions, columns=[dk.label_list[0]])
        pred_df = dk.denormalize_labels_from_metadata(pred_df)
        
        return (pred_df, dk.do_predict)


class ADXSwingAIClassifier(BaseRegressionModel):
    """
    ADX摆动突破策略的分类模型
    
    预测价格变化方向（上涨/下跌/横盘）
    """

    def fit(self, data_dictionary: Dict, dk: FreqaiDataKitchen, **kwargs) -> Any:
        """
        训练分类模型
        """
        
        X = data_dictionary["train_features"]
        y = data_dictionary["train_labels"]
        
        if self.freqai_info.get('data_split_parameters', {}).get('test_size', 0.1) == 0:
            eval_set = None
        else:
            eval_set = [(data_dictionary["test_features"], data_dictionary["test_labels"])]
        
        sample_weight = data_dictionary["train_weights"]
        
        model = self.get_init_model(dk.pair)
        
        model.fit(X=X, y=y, eval_set=eval_set, sample_weight=sample_weight,
                 verbose=100, early_stopping_rounds=20)
        
        return model

    def predict(
        self, unfiltered_dataframe: DataFrame, dk: FreqaiDataKitchen, **kwargs
    ) -> tuple[DataFrame, DataFrame]:
        """
        进行分类预测
        """
        
        # 过滤特征
        filtered_dataframe, _ = dk.filter_features(
            unfiltered_dataframe, dk.training_features_list, training_filter=False
        )
        
        # 进行预测
        predictions = self.model.predict(filtered_dataframe)
        probabilities = self.model.predict_proba(filtered_dataframe)
        
        # 创建预测DataFrame
        pred_df = DataFrame(predictions, columns=[dk.label_list[0]])
        pred_df[f"{dk.label_list[0]}_proba"] = probabilities[:, 1]  # 上涨概率
        
        return (pred_df, dk.do_predict)
