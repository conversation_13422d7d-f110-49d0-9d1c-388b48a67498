#!/usr/bin/env python3
"""
策略调试脚本 - 检查ADXSwingBreakoutStrategy为什么没有交易
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加freqtrade路径
sys.path.append('/freqtrade')

from freqtrade.configuration import Configuration
from freqtrade.data.dataprovider import DataProvider
from freqtrade.resolvers import StrategyResolver
from freqtrade.data.history import load_pair_history

def debug_strategy():
    """调试策略信号生成"""
    
    print("🔍 ADXSwingBreakoutStrategy 调试分析")
    print("=" * 60)
    
    # 配置
    config = {
        'strategy': 'ADXSwingBreakoutStrategy',
        'timeframe': '30m',
        'datadir': '/freqtrade/user_data/data/binance',
        'exchange': {'name': 'binance'},
        'stake_currency': 'USDT',
        'trading_mode': 'futures'
    }
    
    # 加载策略
    try:
        strategy = StrategyResolver.load_strategy(config)
        print(f"✅ 策略加载成功: {strategy.__class__.__name__}")
    except Exception as e:
        print(f"❌ 策略加载失败: {e}")
        return
    
    # 测试币种
    pairs = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'SOL/USDT:USDT']
    
    for pair in pairs:
        print(f"\n📊 分析币种: {pair}")
        print("-" * 40)
        
        try:
            # 加载历史数据
            dataframe = load_pair_history(
                pair=pair,
                timeframe='30m',
                datadir='/freqtrade/user_data/data/binance',
                timerange=None,
                fill_up_missing=True,
                drop_incomplete=True,
                startup_candles=strategy.startup_candle_count,
                data_format='json'
            )
            
            if dataframe.empty:
                print(f"❌ {pair} 没有数据")
                continue
                
            print(f"📈 数据范围: {dataframe.index[0]} 到 {dataframe.index[-1]}")
            print(f"📊 数据量: {len(dataframe)} 条")
            
            # 运行策略指标
            dataframe = strategy.populate_indicators(dataframe, {'pair': pair})
            dataframe = strategy.populate_entry_trend(dataframe, {'pair': pair})
            dataframe = strategy.populate_exit_trend(dataframe, {'pair': pair})
            
            # 检查最近的数据
            recent_data = dataframe.tail(10)
            
            print(f"\n🔍 最近10条数据分析:")
            print(f"ADX范围: {recent_data['adx'].min():.2f} - {recent_data['adx'].max():.2f}")
            print(f"摆动高点数量: {recent_data['swing_high'].sum()}")
            print(f"摆动低点数量: {recent_data['swing_low'].sum()}")
            print(f"突破条件满足: {recent_data['breakout_condition'].sum()}")
            print(f"入场信号: {recent_data.get('enter_long', pd.Series([0])).sum()}")
            print(f"出场信号: {recent_data.get('exit_long', pd.Series([0])).sum()}")
            
            # 检查最新的条件
            latest = dataframe.iloc[-1]
            print(f"\n📋 最新K线分析 ({latest.name}):")
            print(f"  价格: {latest['close']:.4f}")
            print(f"  ADX: {latest['adx']:.2f} (阈值: >20)")
            print(f"  最近摆动高点: {latest['last_swing_high']:.4f}" if not pd.isna(latest['last_swing_high']) else "  最近摆动高点: 无")
            print(f"  入场价格: {latest['long_entry_price']:.4f}" if not pd.isna(latest['long_entry_price']) else "  入场价格: 无")
            print(f"  当前高点: {latest['high']:.4f}")
            print(f"  突破条件: {'✅' if latest['breakout_condition'] else '❌'}")
            print(f"  入场信号: {'✅' if latest.get('enter_long', 0) else '❌'}")
            
            # 分析为什么没有信号
            print(f"\n🔍 信号分析:")
            if pd.isna(latest['last_swing_high']):
                print("  ❌ 没有识别到摆动高点")
            elif latest['adx'] <= 20:
                print(f"  ❌ ADX太低 ({latest['adx']:.2f} <= 20)")
            elif latest['high'] < latest['long_entry_price']:
                print(f"  ❌ 价格未突破 ({latest['high']:.4f} < {latest['long_entry_price']:.4f})")
            else:
                print("  ✅ 所有条件满足，应该有信号")
                
        except Exception as e:
            print(f"❌ {pair} 分析失败: {e}")
            continue
    
    print(f"\n🎯 总结:")
    print("1. 检查数据是否最新")
    print("2. 检查ADX是否足够高")
    print("3. 检查是否有摆动高点")
    print("4. 检查价格是否突破摆动高点")
    print("5. 检查机器人是否在干跑模式")

if __name__ == "__main__":
    debug_strategy()
