{"$schema": "https://schema.freqtrade.io/schema.json", "trading_mode": "futures", "margin_mode": "isolated", "max_open_trades": 5, "stake_currency": "USDT", "stake_amount": 200, "tradable_balance_ratio": 1, "fiat_display_currency": "USD", "dry_run": true, "timeframe": "3m", "dry_run_wallet": 1000, "cancel_open_orders_on_exit": true, "unfilledtimeout": {"entry": 10, "exit": 30}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["1INCH/USDT:USDT", "ALGO/USDT:USDT"], "pair_blacklist": []}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "pairlists": [{"method": "StaticPairList"}], "freqai": {"enabled": true, "purge_old_models": 2, "train_period_days": 15, "backtest_period_days": 7, "live_retrain_hours": 0, "identifier": "unique-id", "feature_parameters": {"include_timeframes": ["3m", "15m", "1h"], "include_corr_pairlist": ["BTC/USDT:USDT", "ETH/USDT:USDT"], "label_period_candles": 20, "include_shifted_candles": 2, "DI_threshold": 0.9, "weight_factor": 0.9, "principal_component_analysis": false, "use_SVM_to_remove_outliers": true, "indicator_periods_candles": [10, 20], "plot_feature_importances": 0}, "data_split_parameters": {"test_size": 0.33, "random_state": 1}, "model_training_parameters": {}}, "bot_name": "", "force_entry_enable": true, "initial_state": "running", "internals": {"process_throttle_secs": 5}}