<html lang="zh-CN" data-theme="light" class="translated-ltr"><head><script data-astro-exec="">
			const url = window.location.href;
			const expectedUrl = url.replace(
				/^https:\/\/(?!beta)(\w+\.)?tradingview\.com/,
				'https://www.tradingview.com',
			);
			if (url !== expectedUrl) {
				window.location.replace(expectedUrl);
			}
		</script><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1"><link rel="icon" href="/pine-script-docs/static/favicon.ico"><link rel="icon" type="image/svg+xml" href="/pine-script-docs/favicon.svg"><title>概念 / 颜色</title><meta name="og:title" content="Concepts / Colors"><meta name="twitter:title" content="Concepts / Colors"><meta name="description" content="Everything you need to know about Pine Script™."><meta name="og:description" content="Everything you need to know about Pine Script™."><meta name="twitter:description" content="Everything you need to know about Pine Script™."><meta name="keywords" content="tradingview, pine, script, indicators, strategies"><meta name="og:image" content="/pine-script-docs/meta-image.png"><meta name="twitter:image" content="/pine-script-docs/meta-image.png"><meta name="og:image:width" content="1200"><meta name="og:image:height" content="630"><meta name="og:url" content="../../concepts/colors/"><meta name="twitter:url" content="../../concepts/colors/"><meta name="og:type" content="website"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@TradingView"><link rel="canonical" href="../../concepts/colors/"><link rel="sitemap" href="/pine-script-docs/sitemap-index.xml"><meta name="astro-view-transitions-enabled" content="true"><meta name="astro-view-transitions-fallback" content="none"><link rel="stylesheet" href="/pine-script-docs/_astro/index.jN9AZoBe.css">
<link rel="stylesheet" href="/pine-script-docs/_astro/index.C8b720yZ.css"><script type="module" src="/pine-script-docs/_astro/hoisted.D4iEV6y6.js" data-astro-exec=""></script><style type="text/css" media="screen">.monaco-editor-tv-pine-dark { 
    .mtk1 { color: #ffffff; }
    .mtk2 { color: #131722; }
    .mtk3 { color: #cc6666; }
    .mtk4 { color: #9cdcfe; }
    .mtk5 { color: #ce9178; }
    .mtk6 { color: #d4d4d4; }
    .mtk7 { color: #b5cea8; }
    .mtk8 { color: #608b4e; }
    .mtk9 { color: #787b86; }
    .mtk10 { color: #569cd6; }
    .mtk11 { color: #f77c80; }
    .mtk12 { color: #f57f17; }
    .mtk13 { color: #dcdcdc; }
    .mtk14 { color: #808080; }
    .mtk15 { color: #3179f5; }
    .mtk16 { color: #5b9cf6; }
    .mtk17 { color: #ba68c8; }
    .mtk18 { color: #42bda8; }
    .mtk19 { color: #f44747; }
    .mtk20 { color: #c586c0; }
    .mtk21 { color: #a79873; }
    .mtk22 { color: #dd6a6f; }
    .mtk23 { color: #5bb498; }
    .mtk24 { color: #909090; }
    .mtk25 { color: #778899; }
    .mtk26 { color: #ff00ff; }
    .mtk27 { color: #b46695; }
    .mtk28 { color: #388e3c; }
    .mtk29 { color: #ff0000; }
    .mtk30 { color: #4f76ac; }
    .mtk31 { color: #3dc9b0; }
    .mtk32 { color: #74b0df; }
    .mtk33 { color: #d1d4dc; }
    .mtk34 { color: #4864aa; }
    .mtki { font-style: italic; }
    .mtkb { font-weight: bold; }
    .mtku { text-decoration: underline; text-underline-position: under; }
    .mtks { text-decoration: line-through; }
    .mtks.mtku { text-decoration: underline line-through; text-underline-position: under; } }</style>
<style type="text/css" media="screen">.monaco-editor-tv-pine-light { 
.mtk1 { color: #131722; }
.mtk2 { color: #ffffff; }
.mtk3 { color: #808080; }
.mtk4 { color: #ff0000; }
.mtk5 { color: #0451a5; }
.mtk6 { color: #0000ff; }
.mtk7 { color: #098658; }
.mtk8 { color: #008000; }
.mtk9 { color: #9598a1; }
.mtk10 { color: #dd0000; }
.mtk11 { color: #cc2929; }
.mtk12 { color: #f57f17; }
.mtk13 { color: #000000; }
.mtk14 { color: #383838; }
.mtk15 { color: #0c3299; }
.mtk16 { color: #2962ff; }
.mtk17 { color: #8e24aa; }
.mtk18 { color: #22ab94; }
.mtk19 { color: #cd3131; }
.mtk20 { color: #863b00; }
.mtk21 { color: #af00db; }
.mtk22 { color: #800000; }
.mtk23 { color: #e00000; }
.mtk24 { color: #3030c0; }
.mtk25 { color: #666666; }
.mtk26 { color: #778899; }
.mtk27 { color: #c700c7; }
.mtk28 { color: #a31515; }
.mtk29 { color: #388e3c; }
.mtk30 { color: #4f76ac; }
.mtk31 { color: #008080; }
.mtk32 { color: #001188; }
.mtk33 { color: #2a2e39; }
.mtk34 { color: #4864aa; }
.mtki { font-style: italic; }
.mtkb { font-weight: bold; }
.mtku { text-decoration: underline; text-underline-position: under; }
.mtks { text-decoration: line-through; }
.mtks.mtku { text-decoration: underline line-through; text-underline-position: under; } }</style><link type="text/css" rel="stylesheet" charset="UTF-8" href="https://www.gstatic.com/_/translate_http/_/ss/k=translate_http.tr.26tY-h6gH9w.L.W.O/am=GAY/d=0/rs=AN8SPfrev-A3NvrBP0gNq8zXCqKY7IcBLA/m=el_main_css"></head> <body> <div id="search-content-blur" hidden=""></div> <script data-astro-exec="">
	/* eslint-disable @typescript-eslint/typedef */
	window.ThemeProvider = (() => {
		function getCurrent() {
			return (
				// eslint-disable-next-line no-restricted-syntax
				typeof localStorage !== 'undefined' &&
				localStorage.getItem('tv-docs-theme')
			);
		}
		const storedTheme = getCurrent();
		const theme =
			storedTheme ||
			(window.matchMedia('(prefers-color-scheme: light)').matches ?
				'light'
			:	'dark');
		document.documentElement.dataset.theme =
			theme === 'light' ? 'light' : 'dark';
		document.documentElement.classList.toggle(
			'theme-dark',
			theme === 'dark',
		); // add support for ui-lib themes
		document.documentElement.classList.toggle(
			'sl-theme-dark',
			theme === 'dark',
		); // add support for shoelace themes

		return {
			updatePickers(themeArg = storedTheme || 'auto') {
				let currentTheme = themeArg;
				if (currentTheme === 'unknown') {
					currentTheme = getCurrent() || 'auto';
				}
				document
					.querySelectorAll('docs-theme-select')
					.forEach((picker) => {
						const select = picker.querySelector('select');
						if (select) select.value = currentTheme;
						/** @type {HTMLTemplateElement | null} */
						const tmpl = document.querySelector(`#theme-icons`);
						const newIcon =
							tmpl &&
							tmpl.content.querySelector('.' + currentTheme);
						if (newIcon) {
							const oldIcon =
								picker.querySelector('svg.label-icon');
							if (oldIcon) {
								oldIcon.replaceChildren(
									...newIcon.cloneNode(true).childNodes,
								);
							}
						}
					});
			},
		};
	})();
	/* eslint-enable @typescript-eslint/typedef */
</script><template id="theme-icons"><svg width="16px" height="16px" viewBox="0 0 28 28" class="light" data-icon="theme/sun-28">  <symbol id="ai:local:theme/sun-28"><g fill="currentColor"><path d="M14 3h1.5v5H14V3Zm0 18h1.5v5H14v-5Zm12-5.5V14h-5v1.5h5ZM8 14v1.5H3V14h5Zm15.3-7-1-1-3.6 3.6 1.1 1 3.5-3.5ZM9.5 18.7l1.1 1.1-3.5 3.5-1-1 3.4-3.6ZM22 23.3l1-1-3.6-3.6-1 1.1 3.5 3.5ZM10.3 9.6l-1.1 1-3.5-3.5 1-1 3.6 3.5Z"></path><path fill-rule="evenodd" d="M19 14.5a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0Zm-1.5 0a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd"></path></g></symbol><use xlink:href="#ai:local:theme/sun-28"></use>  </svg><svg width="16px" height="16px" viewBox="0 0 28 28" class="dark" data-icon="theme/moon-28">  <symbol id="ai:local:theme/moon-28"><path fill="currentColor" fill-rule="evenodd" d="M21 7.02A9.23 9.23 0 0 0 15.2 5 9.1 9.1 0 0 0 6 14c0 4.97 4.12 9 9.2 9a9.33 9.33 0 0 0 5.8-2.02A7 7 0 0 1 14.36 14 7 7 0 0 1 21 7.02Zm-3.95-.3a7.91 7.91 0 0 0-1.85-.22A7.6 7.6 0 0 0 7.5 14a7.6 7.6 0 0 0 7.7 7.5 8 8 0 0 0 1.85-.22A8.46 8.46 0 0 1 12.86 14c0-3.1 1.69-5.8 4.19-7.28Z" clip-rule="evenodd"></path></symbol><use xlink:href="#ai:local:theme/moon-28"></use>  </svg><svg width="16px" height="16px" viewBox="0 0 28 28" class="auto" data-icon="theme/system-28">  <symbol id="ai:local:theme/system-28"><path fill="currentColor" d="M8 4h1v2H8V4Zm0 9v2h1v-2H8Zm6-3V9h-2v1h2ZM3 9v1h2V9H3Zm5.5 3a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm4.2-6-.7-.7-1.4 1.4.7.7L12.7 6ZM5 13.7l-.7-.7 1.4-1.4.7.7L5 13.7Zm7.7-.7-.7.7-1.4-1.4.7-.7 1.4 1.4ZM4.3 6l.7-.7 1.4 1.4-.7.7L4.3 6Zm3 17 14-14 .8.7L8 23.7l-.7-.7Zm17.2-.1a3.5 3.5 0 0 1-3.4-5.9H21a4 4 0 1 0 3.5 5.9Z"></path></symbol><use xlink:href="#ai:local:theme/system-28"></use>  </svg></template> <div class="backdrop" data-mobile-menu-backdrop="" data-astro-cid-h2irkosh=""></div> <div class="menu-container" data-astro-cid-h2irkosh=""> <div class="header" data-astro-cid-h2irkosh=""> <div class="header-group" data-astro-cid-h2irkosh="">    <docs-theme-select class="" data-astro-cid-3wpspbi7=""> <label style="--sl-select-width: 48px; --sl-label-icon-size: 28px;" data-astro-cid-lmznfliq=""> <span class="sr-only" data-astro-cid-lmznfliq=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">主题</font></font></span> <svg width="28" height="28" viewBox="0 0 28 28" class="icon label-icon" data-button="" data-round="" data-astro-cid-lmznfliq="" data-icon="theme/system-28">  <symbol id="ai:local:theme/sun-28"><g fill="currentColor"><path d="M14 3h1.5v5H14V3Zm0 18h1.5v5H14v-5Zm12-5.5V14h-5v1.5h5ZM8 14v1.5H3V14h5Zm15.3-7-1-1-3.6 3.6 1.1 1 3.5-3.5ZM9.5 18.7l1.1 1.1-3.5 3.5-1-1 3.4-3.6ZM22 23.3l1-1-3.6-3.6-1 1.1 3.5 3.5ZM10.3 9.6l-1.1 1-3.5-3.5 1-1 3.6 3.5Z"></path><path fill-rule="evenodd" d="M19 14.5a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0Zm-1.5 0a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd"></path></g></symbol><use xlink:href="#ai:local:theme/sun-28"></use>  </svg> <select value="auto" data-button="" data-astro-cid-lmznfliq=""> <option value="dark" data-astro-cid-lmznfliq=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">&nbsp;&nbsp;黑暗的&nbsp;&nbsp;</font></font></option><option value="light" data-astro-cid-lmznfliq=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">&nbsp;&nbsp;明亮&nbsp;&nbsp;</font></font></option><option value="auto" selected="true" data-astro-cid-lmznfliq=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">&nbsp;&nbsp;自动&nbsp;&nbsp;</font></font></option> </select>  </label>  </docs-theme-select>   <script data-astro-exec="">
	ThemeProvider.updatePickers('unknown');
</script>  </div> <div class="header-group" data-astro-cid-h2irkosh="">  <div class="not-content" style="stroke-width:2px" data-astro-cid-pkzv2hgs=""> <button id="mobile-menu-back-button" title="关闭菜单" data-astro-cid-pkzv2hgs="" class="not-content stvb-base stvb-pointer stvb-gray stvb-medium stvb-primary stvb-icon">  <svg width="28" height="28" viewBox="0 0 28 28" data-astro-cid-h2irkosh="" data-icon="theme/arrow-back">  <symbol id="ai:local:theme/arrow-back"><g fill="none"><g clip-path="url(#a)"><path stroke="var(--arrow-fill-color, #131722)" d="m17 20-6-6 6-6"></path></g><defs><clipPath id="a"><path fill="#fff" d="M28 28H0V0h28z"></path></clipPath></defs></g></symbol><use xlink:href="#ai:local:theme/arrow-back"></use>  </svg>  </button> </div>  </div> </div> <aside id="nav" class="keep-visible" style="--navbar-right-border-width: 0px" data-astro-cid-sa57sq6l=""> <div class="sidebar-viewport" data-astro-cid-sa57sq6l=""> <div class="sidebar" data-mobile="" data-astro-cid-sa57sq6l=""> <ul class="toc" aria-label="文档侧边栏" data-astro-cid-sa57sq6l=""> <li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/welcome" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">欢迎使用 Pine Script™ v5</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><details data-astro-cid-omxx3dey=""><summary data-astro-cid-omxx3dey=""><div class="summary-link" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">初识Pine Script™</font></font></div><div class="caret" data-astro-cid-omxx3dey=""><svg width="18" height="18" viewBox="0 0 24 24" class="icon" data-astro-cid-omxx3dey="" data-icon="theme/right-caret">  <use xlink:href="#ai:local:theme/right-caret"></use>  </svg></div></summary><ul class="children" data-astro-cid-omxx3dey=""><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/primer/first-steps" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">第一步</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/primer/first-indicator" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">第一个指标</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/primer/next-steps" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">下一步</font></font></a></li></ul></details></li><li class="item" data-astro-cid-omxx3dey=""><details data-astro-cid-omxx3dey=""><summary data-astro-cid-omxx3dey=""><div class="summary-link" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">语言</font></font></div><div class="caret" data-astro-cid-omxx3dey=""><svg width="18" height="18" viewBox="0 0 24 24" class="icon" data-astro-cid-omxx3dey="" data-icon="theme/right-caret">  <use xlink:href="#ai:local:theme/right-caret"></use>  </svg></div></summary><ul class="children" data-astro-cid-omxx3dey=""><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/execution-model" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">执行模型</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/time-series" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">时间序列</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/script-structure" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">脚本结构</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/identifiers" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">身份标识</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/operators" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">运算符</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/variable-declarations" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">变量声明</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/conditional-structures" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">条件结构</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/loops" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">循环</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/type-system" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">类型系统</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/built-ins" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">内置</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/user-defined-functions" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">用户定义函数</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/objects" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">对象</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/enums" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">枚举</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/methods" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">方法</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/arrays" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">数组</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/matrices" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">矩阵</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/maps" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">地图</font></font></a></li></ul></details></li><li class="item" data-astro-cid-omxx3dey=""><details open="" data-is-parent="" data-astro-cid-omxx3dey=""><summary data-astro-cid-omxx3dey=""><div class="summary-link" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">概念</font></font></div><div class="caret" data-astro-cid-omxx3dey=""><svg width="18" height="18" viewBox="0 0 24 24" class="icon" data-astro-cid-omxx3dey="" data-icon="theme/right-caret">  <use xlink:href="#ai:local:theme/right-caret"></use>  </svg></div></summary><ul class="children" data-astro-cid-omxx3dey=""><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/alerts" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">警报</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/backgrounds" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">背景</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/bar-coloring" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">条形图色彩</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/bar-plotting" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">条形图</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/bar-states" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">条形图状态</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/chart-information" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">图表信息</font></font></a></li><li class="item" data-current="" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/colors" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">颜色</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/fills" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">填充</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/inputs" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">输入</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/levels" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">级别</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/libraries" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">库</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/lines-and-boxes" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">线条和盒子</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/non-standard-charts-data" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">非标准图表数据</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/other-timeframes-and-data" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">其他时间范围和数据</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/plots" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">图</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/repainting" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">重新粉刷</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/sessions" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">会议</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/strategies" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">策略</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/tables" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">表格</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/text-and-shapes" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">文本和形状</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/time" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">时间</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/timeframes" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">时间范围</font></font></a></li></ul></details></li><li class="item" data-astro-cid-omxx3dey=""><details data-astro-cid-omxx3dey=""><summary data-astro-cid-omxx3dey=""><div class="summary-link" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">编写脚本</font></font></div><div class="caret" data-astro-cid-omxx3dey=""><svg width="18" height="18" viewBox="0 0 24 24" class="icon" data-astro-cid-omxx3dey="" data-icon="theme/right-caret">  <use xlink:href="#ai:local:theme/right-caret"></use>  </svg></div></summary><ul class="children" data-astro-cid-omxx3dey=""><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/writing/style-guide" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">风格指南</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/writing/debugging" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">调试</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/writing/profiling-and-optimization" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">分析和优化</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/writing/publishing" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">发布脚本</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/writing/limitations" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">限制</font></font></a></li></ul></details></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/faq" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">常问问题</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/error-messages" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">错误消息</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/release-notes" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">发行说明</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><details data-astro-cid-omxx3dey=""><summary data-astro-cid-omxx3dey=""><div class="summary-link" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">迁移指南</font></font></div><div class="caret" data-astro-cid-omxx3dey=""><svg width="18" height="18" viewBox="0 0 24 24" class="icon" data-astro-cid-omxx3dey="" data-icon="theme/right-caret">  <use xlink:href="#ai:local:theme/right-caret"></use>  </svg></div></summary><ul class="children" data-astro-cid-omxx3dey=""><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/migration-guides/to-pine-version-5" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">转至 Pine Script™ 版本 5</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/migration-guides/to-pine-version-4" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">转至 Pine Script™ 版本 4</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/migration-guides/to-pine-version-3" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">转至 Pine Script™ 版本 3</font></font></a></li></ul></details></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/where-can-i-get-more-information" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">在哪里可以获得更多信息？</font></font></a></li> </ul> <div class="toc-bottom" data-astro-cid-sa57sq6l=""></div> </div> </div> </aside>  </div>   <header class="header" data-astro-cid-d74r2unp=""> <nav role="navigation" aria-label="主导航" data-astro-cid-d74r2unp=""> <mobile-menu-button id="mobile-menu-button-wc" data-astro-cid-oojooh3d=""> <!-- Annoyingly I need to wrap this. TODO: improve this --> <div id="mobile-menu-button-header" data-astro-cid-oojooh3d=""> <div class="not-content" style="" data-astro-cid-pkzv2hgs=""> <button title="打开导航菜单" data-astro-cid-pkzv2hgs="" class="not-content stvb-base stvb-pointer stvb-gray stvb-medium stvb-secondary stvb-icon stvb-icon-force-color stvb-force-no-border">  <svg width="28" height="28" viewBox="0 0 24 24" data-astro-cid-oojooh3d="" data-icon="theme/bars">  <symbol id="ai:local:theme/bars"><path fill="currentColor" d="M3 8h18a1 1 0 1 0 0-2H3a1 1 0 0 0 0 2Zm18 8H3a1 1 0 0 0 0 2h18a1 1 0 0 0 0-2Zm0-5H3a1 1 0 0 0 0 2h18a1 1 0 0 0 0-2Z"></path></symbol><use xlink:href="#ai:local:theme/bars"></use>  </svg>  </button> </div>  </div> </mobile-menu-button>   <div data-hide-when-search="" data-astro-cid-d74r2unp=""> <div class="header-logo" data-astro-cid-tycb33lk=""> <a href="/pine-script-docs/" aria-label="主页按钮" data-astro-cid-tycb33lk=""> <div class="documentation-logo logo" data-astro-cid-tycb33lk=""><svg width="188" height="44" viewBox="0 0 188 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.9989 28.0001L18.4281 20.7531L11.3382 23.2611C10.402 23.5923 9.60771 22.5041 10.2085 21.7134L21.0877 7.39502C21.4749 6.88536 22.2346 6.86567 22.6478 7.35459L34.7627 21.6923C35.1531 22.1543 35.0472 22.8544 34.5376 23.1802L26.9989 28.0001ZM32.4978 22.1105L27.1641 25.5207L19.7195 19.2258C19.1767 18.7669 18.4312 18.6305 17.7611 18.8676L13.8245 20.2602L21.9253 9.59841L32.4978 22.1105Z" fill="currentColor"></path>
<path d="M33.432 28.0842L38.3592 34.7471C38.7427 35.2657 38.3724 36 37.7274 36H6.27248C5.62746 36 5.25722 35.2657 5.64074 34.7471L10.8523 27.6996C10.9484 27.5697 11.0822 27.4725 11.2356 27.4214L17.6878 25.2707C17.9285 25.1904 18.1932 25.2314 18.3984 25.3806L26.2854 31.1166C26.5445 31.3051 26.8922 31.3173 27.1639 31.1475L32.3838 27.885C32.7316 27.6677 33.1881 27.7544 33.432 28.0842Z" fill="currentColor"></path>
<path d="M59.56 13.96C63.016 13.96 65.536 16.456 65.536 19.696C65.536 22.936 63.016 25.432 59.56 25.432H57.376V31H53.896V13.96H59.56ZM59.512 22.168C61.096 22.168 62.152 21.088 62.152 19.696C62.152 18.304 61.096 17.224 59.512 17.224H57.376V22.168H59.512ZM70.8636 14.944C70.8636 16.096 69.9276 17.032 68.7516 17.032C67.6236 17.032 66.6636 16.096 66.6636 14.944C66.6636 13.792 67.6236 12.856 68.7516 12.856C69.9276 12.856 70.8636 13.792 70.8636 14.944ZM67.1196 31V19H70.4076V31H67.1196ZM73.0849 31V19H76.3729V20.368C76.9969 19.408 78.2449 18.736 79.8049 18.736C82.7089 18.736 84.4369 20.848 84.4369 23.968V31H81.1489V24.52C81.1489 22.792 80.4049 21.736 79.0369 21.736C77.5249 21.736 76.3729 22.84 76.3729 25.144V31H73.0849ZM86.2041 25C86.2041 21.448 88.8681 18.736 92.5161 18.736C95.6601 18.736 98.4201 20.752 98.4201 24.64C98.4201 24.928 98.4201 25.264 98.3721 25.768H89.3961C89.5881 27.376 91.0041 28.264 92.5881 28.264C94.0761 28.264 95.1561 27.568 95.6841 26.752L98.1321 28.576C97.0281 30.184 95.0841 31.264 92.5641 31.264C88.9881 31.264 86.2041 28.792 86.2041 25ZM92.4201 21.448C91.1961 21.448 89.8281 22.072 89.5161 23.536H95.1321C94.8441 22.12 93.6441 21.448 92.4201 21.448ZM104.412 28.24L107.076 26.008C107.916 27.28 109.38 28.024 110.772 28.024C112.14 28.024 113.052 27.352 113.052 26.368C113.052 25.408 112.356 24.736 110.676 24.16L109.236 23.656C106.5 22.696 105.036 21.112 105.036 18.784C105.036 15.568 107.484 13.672 110.796 13.672C112.884 13.672 114.708 14.392 116.172 16.024L113.868 18.4C113.052 17.416 111.996 16.936 110.82 16.936C109.644 16.936 108.54 17.44 108.54 18.448C108.54 19.48 109.38 19.936 111.18 20.584L112.548 21.088C115.044 22 116.604 23.584 116.604 26.152C116.58 29.2 114.156 31.288 110.628 31.288C107.916 31.288 105.636 30.112 104.412 28.24ZM129.859 27.808C128.827 29.872 126.667 31.264 124.171 31.264C120.619 31.264 117.811 28.624 117.811 25C117.811 21.376 120.619 18.736 124.171 18.736C126.667 18.736 128.827 20.128 129.859 22.192L127.003 23.728C126.523 22.624 125.491 21.808 124.171 21.808C122.443 21.808 121.123 23.152 121.123 25C121.123 26.848 122.443 28.192 124.171 28.192C125.491 28.192 126.523 27.376 127.003 26.272L129.859 27.808ZM131.482 31V19H134.77V20.92C135.178 19.744 136.402 18.856 137.746 18.856C138.082 18.856 138.442 18.88 138.85 19V22.336C138.346 22.168 137.89 22.072 137.362 22.072C135.802 22.072 134.77 23.296 134.77 25.264V31H131.482ZM144.215 14.944C144.215 16.096 143.279 17.032 142.103 17.032C140.975 17.032 140.015 16.096 140.015 14.944C140.015 13.792 140.975 12.856 142.103 12.856C143.279 12.856 144.215 13.792 144.215 14.944ZM140.471 31V19H143.759V31H140.471ZM146.436 36.016V19H149.724V20.344C150.276 19.576 151.548 18.736 153.204 18.736C156.396 18.736 158.844 21.592 158.844 25C158.844 28.408 156.396 31.264 153.204 31.264C151.548 31.264 150.276 30.424 149.724 29.656V36.016H146.436ZM155.508 25C155.508 23.128 154.284 21.736 152.484 21.736C150.684 21.736 149.46 23.128 149.46 25C149.46 26.872 150.684 28.264 152.484 28.264C154.284 28.264 155.508 26.872 155.508 25ZM168.407 30.88C167.879 31.048 167.231 31.144 166.319 31.144C163.775 31.144 161.735 29.728 161.735 26.8V21.88H159.311V19H161.735V15.664H165.023V19H168.407V21.88H165.023V26.152C165.023 27.616 165.647 28.192 167.063 28.192C167.591 28.192 168.023 28.12 168.407 27.976V30.88ZM177.626 22.648V13.96H179.978L181.874 16.888L183.746 13.96H186.122V22.648H183.698V18.184L181.874 20.968L180.026 18.136V22.648H177.626ZM171.386 22.648V16.456H169.058V13.96H176.258V16.456H173.93V22.648H171.386Z" fill="currentColor"></path>
</svg>
</div> </a> </div>  </div> <div class="flex" data-astro-cid-d74r2unp=""></div> <div class="flex" data-astro-cid-d74r2unp=""> <div class="search-container" data-astro-cid-fg37foga=""> <div class="search-wrapper" data-astro-cid-fg37foga=""> <input class="search-input" type="text" placeholder="搜索文档" name="s" value="" data-astro-cid-fg37foga="" data-has-input-listener="true"> <svg width="28" height="28" viewBox="0 0 28 28" class="search-button" data-astro-cid-fg37foga="" data-icon="theme/search">  <symbol id="ai:local:theme/search"><path fill="currentColor" fill-rule="evenodd" d="M18.5 12.5a6 6 0 1 1-12 0 6 6 0 0 1 12 0Zm-1.25 5.8a7.5 7.5 0 1 1 1.06-1.06l4.22 4.23.53.53L22 23.06l-.53-.53-4.22-4.22Z" clip-rule="evenodd"></path></symbol><use xlink:href="#ai:local:theme/search"></use>  </svg> <button class="search-clear" type="button" title="重置" data-search-clear="" data-astro-cid-fg37foga=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">清除</font></font></button> <span class="divider" data-astro-cid-fg37foga=""></span> <button class="search-close" type="button" title="关闭" data-search-close="" data-astro-cid-fg37foga=""> <svg width="18" height="18" viewBox="0 0 18 18" data-astro-cid-fg37foga="" data-icon="theme/cross-18">  <use xlink:href="#ai:local:theme/cross-18"></use>  </svg> </button> </div> <div id="search-results-wrapper" data-astro-cid-fg37foga=""> <aside id="search-results" hidden="" data-astro-cid-fg37foga="" data-has-button-listener="true"> <!-- Don't use h1 because when built it will be used as the first heading on the page --> <div class="heading" data-astro-cid-fg37foga=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">搜索结果</font></font></div> <div id="search-results-contents" data-astro-cid-fg37foga=""></div> </aside> </div> </div>   </div> <ul class="links" data-astro-cid-d74r2unp="">  </ul> <div class="flex" data-astro-cid-d74r2unp=""></div> <div data-hide-when-search="" data-astro-cid-d74r2unp="">    </div> <button id="search-button" data-astro-cid-6zeqadij="" data-has-listener="true" style="display:none"> <svg width="28" height="28" viewBox="0 0 28 28" data-astro-cid-6zeqadij="" data-icon="theme/search">  <use xlink:href="#ai:local:theme/search"></use>  </svg> </button>  <script data-base-url="/pine-script-docs" data-astro-exec="">
	async function loadPageFind() {
		const base = document.currentScript.getAttribute('data-base-url');
		const pageFindBundleUrl = `${base}/pagefind/`;
		const pagefind = await import(`${pageFindBundleUrl}pagefind.js`);
		await pagefind.options({
			bundlePath: pageFindBundleUrl,
		});
		window.pagefind = pagefind;
	}
	loadPageFind().catch();
</script>  <docs-theme-select class="hide-with-breakpoint-568" data-astro-cid-3wpspbi7=""> <label style="--sl-select-width: 48px; --sl-label-icon-size: 28px;" data-astro-cid-lmznfliq=""> <span class="sr-only" data-astro-cid-lmznfliq=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">主题</font></font></span> <svg width="28" height="28" viewBox="0 0 28 28" class="icon label-icon" data-button="" data-round="" data-astro-cid-lmznfliq="" data-icon="theme/system-28">  <symbol id="ai:local:theme/sun-28"><g fill="currentColor"><path d="M14 3h1.5v5H14V3Zm0 18h1.5v5H14v-5Zm12-5.5V14h-5v1.5h5ZM8 14v1.5H3V14h5Zm15.3-7-1-1-3.6 3.6 1.1 1 3.5-3.5ZM9.5 18.7l1.1 1.1-3.5 3.5-1-1 3.4-3.6ZM22 23.3l1-1-3.6-3.6-1 1.1 3.5 3.5ZM10.3 9.6l-1.1 1-3.5-3.5 1-1 3.6 3.5Z"></path><path fill-rule="evenodd" d="M19 14.5a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0Zm-1.5 0a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd"></path></g></symbol><use xlink:href="#ai:local:theme/sun-28"></use>  </svg> <select value="auto" data-button="" data-astro-cid-lmznfliq=""> <option value="dark" data-astro-cid-lmznfliq=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">&nbsp;&nbsp;黑暗的&nbsp;&nbsp;</font></font></option><option value="light" data-astro-cid-lmznfliq=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">&nbsp;&nbsp;明亮&nbsp;&nbsp;</font></font></option><option value="auto" selected="true" data-astro-cid-lmznfliq=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">&nbsp;&nbsp;自动&nbsp;&nbsp;</font></font></option> </select>  </label>  </docs-theme-select>   <script data-astro-exec="">
	ThemeProvider.updatePickers('unknown');
</script>  </nav> </header>        <div id="image-lightbox" class="not-content" hidden="" data-astro-cid-kws7taxh=""> <div class="button-wrapper" data-astro-cid-kws7taxh=""> <div class="not-content" style="" data-astro-cid-pkzv2hgs=""> <button id="lightbox-close-button" title="关闭图片预览" data-astro-cid-pkzv2hgs="" class="not-content stvb-base stvb-pointer stvb-black stvb-medium stvb-secondary stvb-icon stvb-force-no-border">  <svg width="24" height="24" viewBox="0 0 18 18" data-astro-cid-kws7taxh="" data-icon="theme/cross-18">  <symbol id="ai:local:theme/cross-18"><g fill="none"><g clip-path="url(#a)"><path fill="currentColor" fill-rule="evenodd" d="M5.53 4.47 4.47 5.53 7.94 9l-3.47 3.47 1.06 1.06L9 10.06l3.47 3.47 1.06-1.06L10.06 9l3.47-3.47-1.06-1.06L9 7.94 5.53 4.47Z" clip-rule="evenodd"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h18v18H0z"></path></clipPath></defs></g></symbol><use xlink:href="#ai:local:theme/cross-18"></use>  </svg>  </button> </div>  </div> <img id="lightbox-image" src="" data-astro-cid-kws7taxh=""> </div>   <div id="page-container" data-astro-cid-xgirumru=""> <aside id="nav" class="" style="" data-astro-cid-sa57sq6l=""> <div class="sidebar-viewport" data-astro-cid-sa57sq6l=""> <div class="sidebar" data-astro-cid-sa57sq6l=""> <ul class="toc" aria-label="文档侧边栏" data-astro-cid-sa57sq6l=""> <li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/welcome" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">欢迎使用 Pine Script™ v5</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><details data-astro-cid-omxx3dey=""><summary data-astro-cid-omxx3dey=""><div class="summary-link" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">初识Pine Script™</font></font></div><div class="caret" data-astro-cid-omxx3dey=""><svg width="18" height="18" viewBox="0 0 24 24" class="icon" data-astro-cid-omxx3dey="" data-icon="theme/right-caret">  <symbol id="ai:local:theme/right-caret"><path fill="currentColor" d="m14.83 11.29-4.24-4.24a1 1 0 1 0-1.42 1.41L12.71 12l-3.54 3.54a1 1 0 0 0 0 1.41 1 1 0 0 0 .71.29 1 1 0 0 0 .71-.29l4.24-4.24a1.002 1.002 0 0 0 0-1.42Z"></path></symbol><use xlink:href="#ai:local:theme/right-caret"></use>  </svg></div></summary><ul class="children" data-astro-cid-omxx3dey=""><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/primer/first-steps" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">第一步</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/primer/first-indicator" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">第一个指标</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/primer/next-steps" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">下一步</font></font></a></li></ul></details></li><li class="item" data-astro-cid-omxx3dey=""><details data-astro-cid-omxx3dey=""><summary data-astro-cid-omxx3dey=""><div class="summary-link" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">语言</font></font></div><div class="caret" data-astro-cid-omxx3dey=""><svg width="18" height="18" viewBox="0 0 24 24" class="icon" data-astro-cid-omxx3dey="" data-icon="theme/right-caret">  <use xlink:href="#ai:local:theme/right-caret"></use>  </svg></div></summary><ul class="children" data-astro-cid-omxx3dey=""><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/execution-model" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">执行模型</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/time-series" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">时间序列</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/script-structure" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">脚本结构</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/identifiers" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">身份标识</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/operators" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">运算符</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/variable-declarations" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">变量声明</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/conditional-structures" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">条件结构</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/loops" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">循环</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/type-system" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">类型系统</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/built-ins" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">内置</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/user-defined-functions" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">用户定义函数</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/objects" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">对象</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/enums" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">枚举</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/methods" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">方法</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/arrays" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">数组</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/matrices" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">矩阵</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/language/maps" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">地图</font></font></a></li></ul></details></li><li class="item" data-astro-cid-omxx3dey=""><details open="" data-is-parent="" data-astro-cid-omxx3dey=""><summary data-astro-cid-omxx3dey=""><div class="summary-link" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">概念</font></font></div><div class="caret" data-astro-cid-omxx3dey=""><svg width="18" height="18" viewBox="0 0 24 24" class="icon" data-astro-cid-omxx3dey="" data-icon="theme/right-caret">  <use xlink:href="#ai:local:theme/right-caret"></use>  </svg></div></summary><ul class="children" data-astro-cid-omxx3dey=""><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/alerts" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">警报</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/backgrounds" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">背景</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/bar-coloring" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">条形图色彩</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/bar-plotting" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">条形图</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/bar-states" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">条形图状态</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/chart-information" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">图表信息</font></font></a></li><li class="item" data-current="" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/colors" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">颜色</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/fills" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">填充</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/inputs" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">输入</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/levels" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">级别</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/libraries" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">库</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/lines-and-boxes" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">线条和盒子</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/non-standard-charts-data" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">非标准图表数据</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/other-timeframes-and-data" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">其他时间范围和数据</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/plots" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">图</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/repainting" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">重新粉刷</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/sessions" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">会议</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/strategies" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">策略</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/tables" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">表格</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/text-and-shapes" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">文本和形状</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/time" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">时间</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/concepts/timeframes" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">时间范围</font></font></a></li></ul></details></li><li class="item" data-astro-cid-omxx3dey=""><details data-astro-cid-omxx3dey=""><summary data-astro-cid-omxx3dey=""><div class="summary-link" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">编写脚本</font></font></div><div class="caret" data-astro-cid-omxx3dey=""><svg width="18" height="18" viewBox="0 0 24 24" class="icon" data-astro-cid-omxx3dey="" data-icon="theme/right-caret">  <use xlink:href="#ai:local:theme/right-caret"></use>  </svg></div></summary><ul class="children" data-astro-cid-omxx3dey=""><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/writing/style-guide" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">风格指南</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/writing/debugging" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">调试</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/writing/profiling-and-optimization" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">分析和优化</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/writing/publishing" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">发布脚本</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/writing/limitations" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">限制</font></font></a></li></ul></details></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/faq" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">常问问题</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/error-messages" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">错误消息</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/release-notes" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">发行说明</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><details data-astro-cid-omxx3dey=""><summary data-astro-cid-omxx3dey=""><div class="summary-link" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">迁移指南</font></font></div><div class="caret" data-astro-cid-omxx3dey=""><svg width="18" height="18" viewBox="0 0 24 24" class="icon" data-astro-cid-omxx3dey="" data-icon="theme/right-caret">  <use xlink:href="#ai:local:theme/right-caret"></use>  </svg></div></summary><ul class="children" data-astro-cid-omxx3dey=""><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/migration-guides/to-pine-version-5" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">转至 Pine Script™ 版本 5</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/migration-guides/to-pine-version-4" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">转至 Pine Script™ 版本 4</font></font></a></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/migration-guides/to-pine-version-3" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">转至 Pine Script™ 版本 3</font></font></a></li></ul></details></li><li class="item" data-astro-cid-omxx3dey=""><a class="page-link" href="/pine-script-docs/where-can-i-get-more-information" data-astro-cid-omxx3dey=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">在哪里可以获得更多信息？</font></font></a></li> </ul> <div class="toc-bottom" data-astro-cid-sa57sq6l=""></div> </div> </div> </aside>  <main class="main-pane" data-page-type="md" data-astro-cid-xgirumru=""> <a id="top" data-astro-cid-xgirumru=""></a> <main class="content" data-toc-shown="" data-astro-cid-ju3wuhkz="">  <div class="content-width" data-astro-cid-xgirumru=""> <div class="breadcrumbs" data-pagefind-ignore="" data-astro-cid-wlavna2o=""> <a href="/pine-script-docs" aria-label="返回文档主页。" data-astro-cid-wlavna2o=""> <span data-astro-cid-wlavna2o=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">用户手册</font></font></span>  </a>  <span class="divider" data-astro-cid-wlavna2o=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">/</font></font></span> <a href="/pine-script-docs/concepts/alerts" data-astro-cid-wlavna2o=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">概念</font></font></a> <span class="divider" data-astro-cid-wlavna2o=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">/</font></font></span> <span class="current-item" data-astro-cid-wlavna2o=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">颜色</font></font></span> </div>     </div> <div id="slot-container" data-astro-cid-xgirumru="">  <h1 id="colors" class="md-heading"><a href="#colors"> <span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">颜色</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h1>
<h2 id="introduction" class="md-heading"><a href="#introduction"> <span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">介绍</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h2>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">脚本视觉效果对于我们在 Pine Script™ 中编写的指标的可用性至关重要。精心设计的图表和绘图使指标更易于使用和理解。良好的视觉设计建立了视觉层次结构，使更重要的信息脱颖而出，而不太重要的信息不会妨碍。</font></font></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">在 Pine 中使用颜色可以随心所欲，也可以根据您的概念需要而复杂化。Pine Script™ 中提供的 4,294,967,296 种颜色和透明度组合可用于：</font></font></p>
<ul>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">您可以在指标的可视空间中绘制或描绘的任何元素，无论是线条、填充、文本还是蜡烛。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">脚本的可视空间的背景，无论脚本是在其自己的窗格中运行，还是在图表上以覆盖模式运行。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">图表上显示的条形图或蜡烛主体的颜色。</font></font></li>
</ul>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">脚本只能为它放置在其自身可视空间中的元素着色。此规则的唯一例外是窗格指示器可以为图表条形图或蜡烛图着色。</font></font></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™ 具有内置颜色，例如
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#const_color%7Bdot%7Dgreen"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.green</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">，以及
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Drgb"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">等函数
，可让您动态生成 RGBA 颜色空间中的任何颜色。</font></font></p>
<h3 id="transparency" class="md-heading"><a href="#transparency"> <span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">透明度</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h3>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™ 中的每种颜色由四个值定义：</font></font></p>
<ul>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">它的红、绿、蓝分量（0-255）遵循</font></font><a href="https://en.wikipedia.org/wiki/RGB_color_space" rel="nofollow"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">RGB颜色模型</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">其透明度（0-100），通常称为 Pine 之外的 Alpha 通道，如</font></font><a href="https://en.wikipedia.org/wiki/RGB_color_space" rel="nofollow"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">RGBA 颜色模型</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">中所定义。尽管透明度以 0-100 范围表示，但在函数中使用时其值可以是“浮点数”，这样您就可以访问 Alpha 通道的 256 个底层值。</font></font></li>
</ul>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">颜色的透明度决定了它的不透明度：0 表示完全不透明，100 表示颜色（无论是哪种颜色）不可见。调节透明度在更复杂的颜色视觉效果或使用背景时至关重要，可以控制哪些颜色占主导地位，以及它们在叠加时如何混合在一起。</font></font></p>
<h3 id="z-index" class="md-heading"><a href="#z-index"> <span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Z 索引</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h3>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">当您将元素放置在脚本的可视空间中时，它们在</font></font><em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">z</font></font></em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">轴上具有相对深度；一些元素将出现在其他元素之上。z </font></font><em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">-index是一个表示元素在</font></font></em><font style="vertical-align: inherit;"></font><em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">z</font></font></em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">轴上
位置的值</font><font style="vertical-align: inherit;">。z-index 最高的元素出现在顶部。</font></font></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™ 中绘制的元素按组划分。每个组在</font></font><em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">z</font></font></em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">空间中都有自己的位置，并且</font></font><strong><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">在同一组中，在脚本逻辑中最后创建的元素将显示在同一组中其他元素的顶部。一个组的元素不能放置在其组所属的</font></font></strong><font style="vertical-align: inherit;"></font><em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">z</font></font></em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">空间区域之外</font><font style="vertical-align: inherit;">，因此绘图永远不会出现在表格的顶部，例如，因为表格具有最高的 z 索引。</font></font></p>
<p><font style="vertical-align: inherit;"></font><em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">此列表包含视觉元素组，按 z 索引递增排序，因此背景颜色始终位于z</font></font></em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">空间的底部</font><font style="vertical-align: inherit;">，并且表格将始终出现在所有其他元素的顶部：</font></font></p>
<ul>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">背景颜色</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">填充</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">图</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">线</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">线填充</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">线条</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">盒子</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">标签</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">表格</font></font></li>
</ul>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">请注意，通过使用</font></font><code dir="auto">explicit_plot_zorder = true</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">indicator
 </font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_indicator"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
或
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_strategy"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">strategies()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;"> ，您可以
使用脚本中的顺序来控制</font></font><code dir="auto">plot*()</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">、
 </font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_hline"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">hline()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
和
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_fill"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">fill()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">视觉效果的相对 z 索引。</font></font></p>
<h2 id="constant-colors" class="md-heading"><a href="#constant-colors"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">恒定</font></font><span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">颜色</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h2>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™ 中有 17 种内置颜色。下表列出了它们的名称、十六进制等效值和 RGB 值作为
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Drgb"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">的参数：</font></font></p>































































































<table><thead><tr><th><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">名称</font></font></th><th><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">十六进制</font></font></th><th><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">RGB 值</font></font></th></tr></thead><tbody><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.水绿色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#00BCD4</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（0，188，212）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">颜色：黑色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#363A45</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（54，58，69）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.蓝色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#2196F3</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（33，150，243）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.紫红色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#E040FB</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（224，64，251）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.灰色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#787B86</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（120，123，134）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.绿色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#4CAF50</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（76，175，80）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.石灰</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#00E676</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb(0, 230, 118)</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.栗色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#880E4F</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（136，14，79）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.海军蓝</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#311B92</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（49，27，146）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.橄榄</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#808000</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（128，128，0）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">颜色为橙色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#FF9800</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（255，152，0）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.紫色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#9C27B0</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（156，39，176）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">红色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#FF5252</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（255，82，82）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.银色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#B2B5BE</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（178，181，190）</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.青色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#00897B</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb(0, 137, 123)</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">白颜色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#FFFFFF</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb(255, 255, 255)</font></font></td></tr><tr><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.黄色</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">#FFEB3B</font></font></td><td><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb（255，235，59）</font></font></td></tr></tbody></table>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">在以下脚本中，所有图都使用相同的
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#const_color%7Bdot%7Dolive"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.olive</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
颜色，透明度为 40，但表达方式不同。所有五种方法在功能上都是等效的：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-UsingColors-1.C5Rg2jRj_PLjh5.webp" alt="图像" width="1796" height="540" loading="lazy" decoding="async"></p>
<div class="pine-colorizer not-content colorized" data-id="61z"><div class="pine-colorizer__header"><a class="pine-colorizer__title" href="https://tradingview.com/pine-script-docs/en/v5/Introduction.html" target="_blank" rel="noopener"><span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™</font></font></span></a><button class="pine-colorizer__copy-btn"><div class="pine-colorizer__tooltip"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">已复制</font></font></div></button></div><code class="monaco-editor-tv-pine-light"><span><span class="mtk9">//</span><span class="mtk9 mtkb">@version=</span><span class="mtk9">5</span></span><br><span><span class="mtk16">indicator</span><span class="mtk13">(</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">true</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;————&nbsp;&nbsp;Transparency&nbsp;(#99)&nbsp;is&nbsp;included&nbsp;in&nbsp;the&nbsp;hex&nbsp;</span><span class="mtk9">value.</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk16">ta.sma</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">10</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"10"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">#80800099</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;————&nbsp;&nbsp;Transparency&nbsp;is&nbsp;included&nbsp;in&nbsp;the&nbsp;</span><span class="mtk9">color-generating&nbsp;function's&nbsp;arguments.</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk16">ta.sma</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">30</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"30"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk11">color.olive</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">40</span><span class="mtk13">))</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk16">ta.sma</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">50</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"50"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.rgb</span><span class="mtk13">(</span><span class="mtk12">128</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">128</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">0</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">40</span><span class="mtk13">))</span></span><br><span><span class="mtk1">&nbsp;&nbsp;</span><span class="mtk9">//&nbsp;————&nbsp;&nbsp;Use&nbsp;`transp`&nbsp;parameter&nbsp;(deprecated&nbsp;and&nbsp;</span><span class="mtk9">advised&nbsp;against)</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk16">ta.sma</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">70</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"70"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">color.olive</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">transp</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">40</span><span class="mtk13">)</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk16">ta.sma</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">90</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"90"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">#808000</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">transp</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">40</span><span class="mtk13">)</span></span><br></code></div>
<aside aria-label="注意！" class="tv-informer not-content" style="--informer-header-color:#FF9800;--informer-back-color-light:#FFF3E0;--informer-back-color-dark:#33261A"><div class="tv-informer-icon"><svg viewBox="0 0 18 18" width="18" height="18"><path fill-rule="evenodd" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16ZM9 4c-.79 0-1.38.7-1.25 1.48l.67 4.03a.59.59 0 0 0 1.16 0l.67-4.03A1.27 1.27 0 0 0 9 4Zm0 8a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z" fill="currentColor"></path></svg></div><div class="tv-informer-content"><p><span class="tv-informer-header"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">注意！</font></font></span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">最后两个
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_plot"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">plot()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
调用使用</font></font><code dir="auto">transp</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">参数指定透明度。应避免这种用法，因为</font></font><code dir="auto">transp</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">在 Pine Script™ v5 中已弃用。使用
</font></font><code dir="auto">transp</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">参数定义透明度不够灵活，因为它需要</font></font><em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">输入整数</font></font></em><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">类型的参数，这意味着必须在执行脚本之前知道它，因此无法动态计算，因为脚本逐条执行。此外，如果您使用已经包含透明度信息的参数（如接下来的三个</font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_plot"><font style="vertical-align: inherit;">plot()</font></a></font><code dir="auto">color</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">调用中所做的那样）
 </font><font style="vertical-align: inherit;">
，则用于该参数的任何参数</font><font style="vertical-align: inherit;">都不会产生任何效果。对于带有参数的其他函数也是如此</font><font style="vertical-align: inherit;">。</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_plot"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font><code dir="auto">transp</code><font style="vertical-align: inherit;"></font><code dir="auto">transp</code><font style="vertical-align: inherit;"></font></p></div></aside>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">前一个脚本中的颜色不会随着脚本逐条执行而变化。但有时，需要在脚本执行每条柱时创建颜色，因为它们取决于编译时未知的条件，或者脚本从第 0 条柱开始执行时的颜色。对于这些情况，程序员有两种选择：</font></font></p>
<ol>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">使用条件语句从一些预先确定的基色中选择颜色。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">例如，通过在脚本逐条执行时计算新颜色来动态构建新颜色，以实现颜色渐变。</font></font></li>
</ol>
<h2 id="conditional-coloring" class="md-heading"><a href="#conditional-coloring"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">条件</font></font><span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">着色</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h2>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">假设您想根据定义的某些条件为移动平均线涂上不同的颜色。为此，您可以使用条件语句，为每个州选择不同的颜色。让我们首先在移动平均线上涨时将其涂上牛市颜色，在移动平均线不上涨时将其涂上熊市颜色：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-ConditionalColors-1.D1QaF3Y-_ZumNWi.webp" alt="图像" width="1534" height="468" loading="lazy" decoding="async"></p>
<div class="pine-colorizer not-content colorized" data-id="1ry"><div class="pine-colorizer__header"><a class="pine-colorizer__title" href="https://tradingview.com/pine-script-docs/en/v5/Introduction.html" target="_blank" rel="noopener"><span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™</font></font></span></a><button class="pine-colorizer__copy-btn"><div class="pine-colorizer__tooltip"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">已复制</font></font></div></button></div><code class="monaco-editor-tv-pine-light"><span><span class="mtk9">//</span><span class="mtk9 mtkb">@version=</span><span class="mtk9">5</span></span><br><span><span class="mtk16">indicator</span><span class="mtk13">(</span><span class="mtk29">"Conditional&nbsp;colors"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">true</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">int</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk33">lengthInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.int</span><span class="mtk13">(</span><span class="mtk12">20</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Length"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">minval</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">2</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">maBullColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk11">color.green</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Bull"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">maBearColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk11">color.maroon</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Bear"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">ma</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.sma</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">lengthInput</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;Define&nbsp;our&nbsp;states.</span></span><br><span><span class="mtk18 mtkb">bool</span><span class="mtk1">&nbsp;</span><span class="mtk33">maRising</span><span class="mtk1">&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.rising</span><span class="mtk13">(</span><span class="mtk33">ma</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">1</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;Build&nbsp;our&nbsp;color.</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">c_ma</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">maRising</span><span class="mtk1">&nbsp;</span><span class="mtk18">?</span><span class="mtk1">&nbsp;</span><span class="mtk33">maBullColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">:</span><span class="mtk1">&nbsp;</span><span class="mtk33">maBearColorInput</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">ma</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"MA"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">c_ma</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">2</span><span class="mtk13">)</span></span><br></code></div>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">注意：</font></font></p>
<ul>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们为脚本用户提供牛市/熊市颜色的选择。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们定义一个布尔变量</font><font style="vertical-align: inherit;">，当当前条上的移动平均线高于上一个条上的移动平均线时，</font></font><code dir="auto">maRising</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">该变量将保持不变。</font></font><code dir="auto">true</code><font style="vertical-align: inherit;"></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们定义一个</font></font><code dir="auto">c_ma</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">颜色变量，根据布尔值指定两种颜色之一</font></font><code dir="auto">maRising</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">。我们使用
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#op_%7Bquestion%7D%7Bcolon%7D"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">? : 三元运算符</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
来编写条件语句。</font></font></li>
</ul>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">您还可以使用条件颜色来避免在某些条件下绘图。在这里，我们使用一条线绘制高低枢轴，但我们不想在新的枢轴出现时绘制任何内容，以避免枢轴转换中出现的关节。为此，我们测试枢轴变化，并在检测到变化时使用
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#var_na"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">na</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">作为颜色值，这样就不会在该条上绘制任何线：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-ConditionalColors-2.erY98c4P_Z1OwyIO.webp" alt="图像" width="1530" height="472" loading="lazy" decoding="async"></p>
<div class="pine-colorizer not-content colorized" data-id="3za"><div class="pine-colorizer__header"><a class="pine-colorizer__title" href="https://tradingview.com/pine-script-docs/en/v5/Introduction.html" target="_blank" rel="noopener"><span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™</font></font></span></a><button class="pine-colorizer__copy-btn"><div class="pine-colorizer__tooltip"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">已复制</font></font></div></button></div><code class="monaco-editor-tv-pine-light"><span><span class="mtk9">//</span><span class="mtk9 mtkb">@version=</span><span class="mtk9">5</span></span><br><span><span class="mtk16">indicator</span><span class="mtk13">(</span><span class="mtk29">"Conditional&nbsp;colors"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">true</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">int</span><span class="mtk1">&nbsp;</span><span class="mtk33">legsInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.int</span><span class="mtk13">(</span><span class="mtk12">5</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Pivot&nbsp;Legs"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">minval</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">1</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">pHiColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk11">color.olive</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"High&nbsp;pivots"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">pLoColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk11">color.orange</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Low&nbsp;pivots"</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;Intialize&nbsp;the&nbsp;pivot&nbsp;level&nbsp;variables.</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">pHi</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">na</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">pLo</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">na</span></span><br><span><span class="mtk9">//&nbsp;When&nbsp;a&nbsp;new&nbsp;pivot&nbsp;is&nbsp;detected,&nbsp;save&nbsp;its&nbsp;value.</span></span><br><span><span class="mtk33">pHi</span><span class="mtk1">&nbsp;</span><span class="mtk18">:=</span><span class="mtk1">&nbsp;</span><span class="mtk16">nz</span><span class="mtk13">(</span><span class="mtk16">ta.pivothigh</span><span class="mtk13">(</span><span class="mtk33">legsInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">legsInput</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">pHi</span><span class="mtk13">)</span></span><br><span><span class="mtk33">pLo</span><span class="mtk1">&nbsp;</span><span class="mtk18">:=</span><span class="mtk1">&nbsp;</span><span class="mtk16">nz</span><span class="mtk13">(</span><span class="mtk16">ta.pivotlow</span><span class="mtk13">(</span><span class="mtk1">&nbsp;</span><span class="mtk33">legsInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">legsInput</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">pLo</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;When&nbsp;a&nbsp;new&nbsp;pivot&nbsp;is&nbsp;detected,&nbsp;do&nbsp;not&nbsp;plot&nbsp;a&nbsp;col</span><span class="mtk9">or.</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">pHi</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"High"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.change</span><span class="mtk13">(</span><span class="mtk33">pHi</span><span class="mtk13">)</span><span class="mtk1">&nbsp;</span><span class="mtk18">?</span><span class="mtk1">&nbsp;</span><span class="mtk11">na</span><span class="mtk1">&nbsp;</span><span class="mtk18">:</span><span class="mtk1">&nbsp;</span><span class="mtk33">pHiColorInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">2</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">plot.style_line</span><span class="mtk13">)</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">pLo</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Low"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;&nbsp;</span><span class="mtk16">ta.change</span><span class="mtk13">(</span><span class="mtk33">pLo</span><span class="mtk13">)</span><span class="mtk1">&nbsp;</span><span class="mtk18">?</span><span class="mtk1">&nbsp;</span><span class="mtk11">na</span><span class="mtk1">&nbsp;</span><span class="mtk18">:</span><span class="mtk1">&nbsp;</span><span class="mtk33">pLoColorInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">2</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">plot.style_line</span><span class="mtk13">)</span></span><br></code></div>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">要理解此代码的工作原理，首先必须知道
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_ta%7Bdot%7Dpivothigh"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">ta.pivothigh()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
和
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_ta%7Bdot%7Dpivotlow"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">ta.pivotlow()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">（在这里使用时没有使用参数</font></font><code dir="auto">source</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">）将在找到
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#var_high"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">高</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">/</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#var_low"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">低</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
枢轴时返回一个值，否则它们将返回
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#var_na"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">na</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">。</font></font></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">当我们使用</font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_nz"><font style="vertical-align: inherit;">nz()</font></a><font style="vertical-align: inherit;">函数测试枢轴函数返回的
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#var_na"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">na</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">值时
，仅当返回值</font><font style="vertical-align: inherit;">不是
</font><a href="https://www.tradingview.com/pine-script-reference/v5/#var_na"><font style="vertical-align: inherit;">na</font></a><font style="vertical-align: inherit;">时，我们才允许将其分配给或
变量</font><font style="vertical-align: inherit;">，否则变量的先前值将简单地重新分配给它，这不会对其值产生任何影响。请记住，
</font><font style="vertical-align: inherit;">和的先前值</font><font style="vertical-align: inherit;">会逐条保留，因为我们在初始化它们时使用了
</font><a href="https://www.tradingview.com/pine-script-reference/v5/#kw_var"><font style="vertical-align: inherit;">var</font></a><font style="vertical-align: inherit;">
关键字，这会导致初始化仅发生在第一个柱上。</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_nz"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font><code dir="auto">pHi</code><font style="vertical-align: inherit;"></font><code dir="auto">pLo</code><font style="vertical-align: inherit;"></font><a href="https://www.tradingview.com/pine-script-reference/v5/#var_na"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font><code dir="auto">pHi</code><font style="vertical-align: inherit;"></font><code dir="auto">pLo</code><font style="vertical-align: inherit;"></font><a href="https://www.tradingview.com/pine-script-reference/v5/#kw_var"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">接下来剩下要做的就是，当我们绘制线条时，插入一个三元条件语句，当枢轴值改变时，该语句将产生
颜色</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#var_na"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">na</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">，或者当枢轴水平不变时，产生脚本输入中选择的颜色。</font></font></p>
<h2 id="calculated-colors" class="md-heading"><a href="#calculated-colors"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">计算</font></font><span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">颜色</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h2>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">使用
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dnew"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.new()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">，
 </font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Drgb"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
和
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dfrom_gradient"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.from_gradient()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">等函数，可以在脚本逐条执行时动态构建颜色。</font></font></p>
<p><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dnew"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
当您需要从基色生成不同透明度级别时，</font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dnew"><font style="vertical-align: inherit;">color.new()最有用。</font></a></font></p>
<p><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Drgb"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
当您需要从红色、绿色、蓝色或透明度分量动态构建颜色时，
 </font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Drgb"><font style="vertical-align: inherit;">color.rgb()非常有用。color.rgb </font></a></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Drgb"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">可以
创建颜色，而其姊妹函数
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dr"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.r()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">、
 </font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dg"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.g()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">、
 </font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Db"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.b()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
和
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dt"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.t()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
可用于从颜色中提取红色、绿色、蓝色或透明度值，进而可用于生成变体。</font></font></p>
<p><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dfrom_gradient"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.from_gradient()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
可用于在两种基色之间创建线性渐变。它通过评估源值与最小值和最大值来确定使用哪种中间颜色。</font></font></p>
<h3 id="colornew" class="md-heading"><a href="#colornew"> <span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.new()</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h3>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">让我们使用</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dnew"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.new(color, transp)</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
来使用两种牛市/熊市基色之一为成交量列创建不同的透明度：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-CalculatingColors-1.BtEyXAO2_Z2dfpcb.webp" alt="图像" width="1407" height="548" loading="lazy" decoding="async"></p>
<div class="pine-colorizer not-content colorized" data-id="6p1"><div class="pine-colorizer__header"><a class="pine-colorizer__title" href="https://tradingview.com/pine-script-docs/en/v5/Introduction.html" target="_blank" rel="noopener"><span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™</font></font></span></a><button class="pine-colorizer__copy-btn"><div class="pine-colorizer__tooltip"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">已复制</font></font></div></button></div><code class="monaco-editor-tv-pine-light"><span><span class="mtk9">//</span><span class="mtk9 mtkb">@version=</span><span class="mtk9">5</span></span><br><span><span class="mtk16">indicator</span><span class="mtk13">(</span><span class="mtk29">"Volume"</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;We&nbsp;name&nbsp;our&nbsp;color&nbsp;constants&nbsp;to&nbsp;make&nbsp;them&nbsp;more&nbsp;r</span><span class="mtk9">eadable.</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">GOLD_COLOR</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">#CCCC00ff</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">VIOLET_COLOR</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">#AA00FFff</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bullColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk33">GOLD_COLOR</span><span class="mtk18">,</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk29">"Bull"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bearColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk33">VIOLET_COLOR</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Bear"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">int</span><span class="mtk1">&nbsp;</span><span class="mtk33">levelsInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.int</span><span class="mtk13">(</span><span class="mtk12">10</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Gradient&nbsp;levels"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">minval</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">1</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;We&nbsp;initialize&nbsp;only&nbsp;once&nbsp;on&nbsp;bar&nbsp;zero&nbsp;with&nbsp;`var`,</span><span class="mtk9">&nbsp;otherwise&nbsp;the&nbsp;count&nbsp;would&nbsp;reset&nbsp;to&nbsp;zero&nbsp;on&nbsp;each&nbsp;b</span><span class="mtk9">ar.</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">riseFallCnt</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">0</span></span><br><span><span class="mtk9">//&nbsp;Count&nbsp;the&nbsp;rises/falls,&nbsp;clamping&nbsp;the&nbsp;range&nbsp;to:&nbsp;1</span><span class="mtk9">&nbsp;to&nbsp;`i_levels`.</span></span><br><span><span class="mtk33">riseFallCnt</span><span class="mtk1">&nbsp;</span><span class="mtk18">:=</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.max</span><span class="mtk13">(</span><span class="mtk12">1</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.min</span><span class="mtk13">(</span><span class="mtk33">levelsInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">riseFallCnt</span><span class="mtk1">&nbsp;</span><span class="mtk18">+</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.sign</span><span class="mtk13">(</span><span class="mtk11">volume</span><span class="mtk1">&nbsp;</span><span class="mtk18">-</span><span class="mtk1">&nbsp;</span><span class="mtk16">nz</span><span class="mtk13">(</span><span class="mtk11">volume</span><span class="mtk13">[</span><span class="mtk12">1</span><span class="mtk13">]))))</span></span><br><span><span class="mtk9">//&nbsp;Rescale&nbsp;the&nbsp;count&nbsp;on&nbsp;a&nbsp;scale&nbsp;of&nbsp;80,&nbsp;reverse&nbsp;it&nbsp;</span><span class="mtk9">and&nbsp;cap&nbsp;transparency&nbsp;to&nbsp;&lt;80&nbsp;so&nbsp;that&nbsp;colors&nbsp;remains</span><span class="mtk9">&nbsp;visible.</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">transparency</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">80</span><span class="mtk1">&nbsp;</span><span class="mtk18">-</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.abs</span><span class="mtk13">(</span><span class="mtk12">80</span><span class="mtk1">&nbsp;</span><span class="mtk18">*</span><span class="mtk1">&nbsp;</span><span class="mtk33">riseFallCnt</span><span class="mtk1">&nbsp;</span><span class="mtk18">/</span><span class="mtk1">&nbsp;</span><span class="mtk33">levelsInput</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;Build&nbsp;the&nbsp;correct&nbsp;transparency&nbsp;of&nbsp;either&nbsp;the&nbsp;bu</span><span class="mtk9">ll&nbsp;or&nbsp;bear&nbsp;color.</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">volumeColor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk1">&nbsp;</span><span class="mtk18">&gt;</span><span class="mtk1">&nbsp;</span><span class="mtk11">open</span><span class="mtk1">&nbsp;</span><span class="mtk18">?</span><span class="mtk1">&nbsp;</span><span class="mtk33">bullColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">:</span><span class="mtk1">&nbsp;</span><span class="mtk33">bearColorInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">transparency</span><span class="mtk13">)</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk11">volume</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Volume"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">volumeColor</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">1</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">plot.style_columns</span><span class="mtk13">)</span></span><br></code></div>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">注意：</font></font></p>
<ul>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">在我们脚本的倒数第二行，我们通过改变所使用的基本颜色（取决于条形图是向上还是向下）</font></font><strong><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">和</font></font></strong><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">透明度级别（根据累计的成交量上升或下降计算得出）来动态计算列颜色。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们不仅为脚本用户提供控制所使用的基本牛市/熊市颜色的功能，还为他们提供控制我们使用的亮度级别数量的功能。我们使用此值来确定我们将跟踪的最大上升或下降次数。让用户能够管理此值，使他们能够根据他们使用的时间范围或市场调整指标的视觉效果。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们小心控制所使用的最大透明度，使其不超过 80。这确保我们的颜色始终保持一定的可见度。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们还将输入中的级别数的最小值设置为 1。当用户选择 1 时，成交量列将采用最大亮度的牛市或熊市颜色 — 或透明度为零。</font></font></li>
</ul>
<h3 id="colorrgb" class="md-heading"><a href="#colorrgb"> <span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb()</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h3>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">在下一个示例中，我们使用</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Drgb"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb(red, green, blue, transp)</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
从 RGBA 值构建颜色。我们将结果用作节日礼物送给朋友，这样他们就可以将 TradingView 图表带到聚会上：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-CalculatingColors-2.B7PJa61g_ZI19Ev.webp" alt="图像" width="1786" height="548" loading="lazy" decoding="async"></p>
<div class="pine-colorizer not-content colorized" data-id="1g0"><div class="pine-colorizer__header"><a class="pine-colorizer__title" href="https://tradingview.com/pine-script-docs/en/v5/Introduction.html" target="_blank" rel="noopener"><span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™</font></font></span></a><button class="pine-colorizer__copy-btn"><div class="pine-colorizer__tooltip"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">已复制</font></font></div></button></div><code class="monaco-editor-tv-pine-light"><span><span class="mtk9">//</span><span class="mtk9 mtkb">@version=</span><span class="mtk9">5</span></span><br><span><span class="mtk16">indicator</span><span class="mtk13">(</span><span class="mtk29">"Holiday&nbsp;candles"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">true</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">r</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.random</span><span class="mtk13">(</span><span class="mtk12">0</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">255</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">g</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.random</span><span class="mtk13">(</span><span class="mtk12">0</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">255</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">b</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.random</span><span class="mtk13">(</span><span class="mtk12">0</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">255</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">t</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.random</span><span class="mtk13">(</span><span class="mtk12">0</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">100</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">holidayColor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.rgb</span><span class="mtk13">(</span><span class="mtk33">r</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">g</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">b</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">t</span><span class="mtk13">)</span></span><br><span><span class="mtk16">plotcandle</span><span class="mtk13">(</span><span class="mtk11">open</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">high</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">low</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">color</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">c_holiday</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">wickcolor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">holidayColor</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">bordercolor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">c_holiday</span><span class="mtk13">)</span></span><br></code></div>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">注意：</font></font></p>
<ul>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们为红、绿和蓝通道生成 0 到 255 范围内的值，为透明度生成 0 到 100 范围内的值。还要注意，因为
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_math%7Bdot%7Drandom"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">math.random()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
返回浮点值，所以浮点 0.0-100.0 范围可以访问底层 alpha 通道的完整 0-255 透明度值。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们使用</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_math%7Bdot%7Drandom"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">math.random(min, max, seed)</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
函数来生成伪随机值。我们不使用函数的第三个参数作为参数：</font></font><code dir="auto">seed</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">。当您想要确保函数结果的可重复性时，使用它很方便。使用相同的种子调用，它将产生相同的值序列。</font></font></li>
</ul>
<h3 id="colorfrom_gradient" class="md-heading"><a href="#colorfrom_gradient"> <span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.from_gradient()</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h3>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们最后的颜色计算示例将使用
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dfrom_gradient"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.from_gradient(value, bottom_value, top_value, bottom_color, top_color)</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">。让我们首先以最简单的形式使用它，在指标版本中为 CCI 信号着色，该指标版本看起来像内置的：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-CalculatingColors-3.BWe7BFsC_Zg2kjC.webp" alt="图像" width="1450" height="250" loading="lazy" decoding="async"></p>
<div class="pine-colorizer not-content colorized" data-id="4m4"><div class="pine-colorizer__header"><a class="pine-colorizer__title" href="https://tradingview.com/pine-script-docs/en/v5/Introduction.html" target="_blank" rel="noopener"><span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™</font></font></span></a><button class="pine-colorizer__copy-btn"><div class="pine-colorizer__tooltip"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">已复制</font></font></div></button></div><code class="monaco-editor-tv-pine-light"><span><span class="mtk9">//</span><span class="mtk9 mtkb">@version=</span><span class="mtk9">5</span></span><br><span><span class="mtk16">indicator</span><span class="mtk13">(</span><span class="mtk33">title</span><span class="mtk18">=</span><span class="mtk29">"CCI&nbsp;line&nbsp;gradient"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">precision</span><span class="mtk18">=</span><span class="mtk12">2</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">timeframe</span><span class="mtk18">=</span><span class="mtk29">""</span><span class="mtk13">)</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">GOLD_COLOR</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">#CCCC00</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">VIOLET_COLOR</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">#AA00FF</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">BEIGE_COLOR</span><span class="mtk1">&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">#9C6E1B</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">srcInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.source</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">title</span><span class="mtk18">=</span><span class="mtk29">"Source"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">int</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk33">lenInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.int</span><span class="mtk13">(</span><span class="mtk12">20</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Length"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">minval</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">5</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bullColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk33">GOLD_COLOR</span><span class="mtk18">,</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk29">"Bull"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bearColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk33">BEIGE_COLOR</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Bear"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">signal</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.cci</span><span class="mtk13">(</span><span class="mtk33">srcInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">lenInput</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">signalColor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.from_gradient</span><span class="mtk13">(</span><span class="mtk33">signal</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">-200</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">200</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">bearColorInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">bullColorInput</span><span class="mtk13">)</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">signal</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"CCI"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">signalColor</span><span class="mtk13">)</span></span><br><span><span class="mtk33">bandTopPlotID</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">hline</span><span class="mtk13">(</span><span class="mtk12">100</span><span class="mtk18">,</span><span class="mtk1">&nbsp;&nbsp;</span><span class="mtk29">"Upper&nbsp;Band"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">color.silver</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">hline.style_dashed</span><span class="mtk13">)</span></span><br><span><span class="mtk33">bandBotPlotID</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">hline</span><span class="mtk13">(</span><span class="mtk12">-100</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Lower&nbsp;Band"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">color.silver</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">hline.style_dashed</span><span class="mtk13">)</span></span><br><span><span class="mtk16">fill</span><span class="mtk13">(</span><span class="mtk33">bandTopPlotID</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">bandBotPlotID</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk33">BEIGE_COLOR</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">90</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Background"</span><span class="mtk13">)</span></span><br></code></div>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">注意：</font></font></p>
<ul>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">为了计算梯度，
 </font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dfrom_gradient"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.from_gradient()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
需要最小值和最大值，参数使用的参数</font></font><code dir="auto">value</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">将与这些值进行比较。我们想要像 CCI 这样的无界信号的梯度（即没有固定边界，如 RSI，它总是在 0-100 之间波动），但这并不意味着我们不能使用
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dfrom_gradient"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.from_gradient()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">。在这里，我们通过提供 -200 和 200 作为参数来解决我们的难题。它们并不代表 CCI 的实际最小值和最大值，但它们处于我们不介意颜色不再变化的水平，因为只要系列超出
</font></font><code dir="auto">bottom_value</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">和限制，</font><font style="vertical-align: inherit;">和</font></font><code dir="auto">top_value</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">使用的颜色
</font><font style="vertical-align: inherit;">就会适用。</font></font><code dir="auto">bottom_color</code><font style="vertical-align: inherit;"></font><code dir="auto">top_color</code><font style="vertical-align: inherit;"></font></li>
<li><font style="vertical-align: inherit;"></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dfrom_gradient"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.from_gradient()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">计算的颜色渐变
</font><font style="vertical-align: inherit;">
是线性的。如果系列值位于 和
</font></font><code dir="auto">bottom_value</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">参数的中间</font></font><code dir="auto">top_value</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">，则生成的颜色的 RGBA 分量也将位于 和 的</font></font><code dir="auto">bottom_color</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
中间</font></font><code dir="auto">top_color</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™ 中提供许多常见指标计算作为内置函数。这里我们使用
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_ta%7Bdot%7Dcci"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">ta.cci()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
而不是用长方法计算。</font></font></li>
</ul>
<p><font style="vertical-align: inherit;"><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dfrom_gradient"><font style="vertical-align: inherit;">color.from_gradient()</font></a></font><code dir="auto">value</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">中
</font><font style="vertical-align: inherit;">使用的参数</font><font style="vertical-align: inherit;">
不一定是我们正在计算的线的值。只要可以
提供</font><font style="vertical-align: inherit;">和的参数，我们可以使用任何我们想要的东西</font><font style="vertical-align: inherit;">。在这里，我们通过使用信号高于/低于中心线以来的条数来为波段着色，从而增强我们的 CCI 指标：</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dfrom_gradient"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font><code dir="auto">bottom_value</code><font style="vertical-align: inherit;"></font><code dir="auto">top_value</code><font style="vertical-align: inherit;"></font></p>
<p><img src="/pine-script-docs/_astro/Colors-CalculatingColors-4.-U0l6lwc_Zd5X5K.webp" alt="图像" width="1459" height="250" loading="lazy" decoding="async"></p>
<div class="pine-colorizer not-content colorized" data-id="1vq"><div class="pine-colorizer__header"><a class="pine-colorizer__title" href="https://tradingview.com/pine-script-docs/en/v5/Introduction.html" target="_blank" rel="noopener"><span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™</font></font></span></a><button class="pine-colorizer__copy-btn"><div class="pine-colorizer__tooltip"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">已复制</font></font></div></button></div><code class="monaco-editor-tv-pine-light"><span><span class="mtk9">//</span><span class="mtk9 mtkb">@version=</span><span class="mtk9">5</span></span><br><span><span class="mtk16">indicator</span><span class="mtk13">(</span><span class="mtk33">title</span><span class="mtk18">=</span><span class="mtk29">"CCI&nbsp;line&nbsp;gradient"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">precision</span><span class="mtk18">=</span><span class="mtk12">2</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">timeframe</span><span class="mtk18">=</span><span class="mtk29">""</span><span class="mtk13">)</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">GOLD_COLOR</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">#CCCC00</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">VIOLET_COLOR</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">#AA00FF</span><span class="mtk1">&nbsp;&nbsp;</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">GREEN_BG_COLOR</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk11">color.green</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">70</span><span class="mtk13">)</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">RED_BG_COLOR</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk11">color.maroon</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">70</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">srcInput</span><span class="mtk1">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.source</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Source"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">int</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk33">lenInput</span><span class="mtk1">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.int</span><span class="mtk13">(</span><span class="mtk12">20</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Length"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">minval</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">5</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">int</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk33">stepsInput</span><span class="mtk1">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.int</span><span class="mtk13">(</span><span class="mtk12">50</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Gradient&nbsp;levels"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">minval</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">1</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bullColorInput</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk33">GOLD_COLOR</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Line:&nbsp;Bull"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">inline</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk29">"11"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bearColorInput</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk33">VIOLET_COLOR</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Bear"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">inline</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk29">"11"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bullBgColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk33">GREEN_BG_COLOR</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Background:&nbsp;Bull"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">inline</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk29">"12"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bearBgColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk33">RED_BG_COLOR</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Bear"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">inline</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk29">"12"</span><span class="mtk13">)</span></span><br><span><span></span></span><br><span><span class="mtk9">//&nbsp;Plot&nbsp;colored&nbsp;signal&nbsp;line.</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">signal</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.cci</span><span class="mtk13">(</span><span class="mtk33">srcInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">lenInput</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">signalColor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.from_gradient</span><span class="mtk13">(</span><span class="mtk33">signal</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">-200</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">200</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk33">bearColorInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">0</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk33">bullColorInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">0</span><span class="mtk13">))</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">signal</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"CCI"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">signalColor</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">2</span><span class="mtk13">)</span></span><br><span><span></span></span><br><span><span class="mtk9">//&nbsp;Detect&nbsp;crosses&nbsp;of&nbsp;the&nbsp;centerline.</span></span><br><span><span class="mtk18 mtkb">bool</span><span class="mtk1">&nbsp;</span><span class="mtk33">signalX</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.cross</span><span class="mtk13">(</span><span class="mtk33">signal</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">0</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;Count&nbsp;no&nbsp;of&nbsp;bars&nbsp;since&nbsp;cross.&nbsp;Capping&nbsp;it&nbsp;to&nbsp;the</span><span class="mtk9">&nbsp;no&nbsp;of&nbsp;steps&nbsp;from&nbsp;inputs.</span></span><br><span><span class="mtk18 mtkb">int</span><span class="mtk1">&nbsp;</span><span class="mtk33">gradientStep</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.min</span><span class="mtk13">(</span><span class="mtk33">stepsInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">nz</span><span class="mtk13">(</span><span class="mtk16">ta.barssince</span><span class="mtk13">(</span><span class="mtk33">signalX</span><span class="mtk13">)))</span></span><br><span><span class="mtk9">//&nbsp;Choose&nbsp;bull/bear&nbsp;end&nbsp;color&nbsp;for&nbsp;the&nbsp;gradient.</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">endColor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">signal</span><span class="mtk1">&nbsp;</span><span class="mtk18">&gt;</span><span class="mtk1">&nbsp;</span><span class="mtk12">0</span><span class="mtk1">&nbsp;</span><span class="mtk18">?</span><span class="mtk1">&nbsp;</span><span class="mtk33">bullBgColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">:</span><span class="mtk1">&nbsp;</span><span class="mtk33">bearBgColorInput</span></span><br><span><span class="mtk9">//&nbsp;Get&nbsp;color&nbsp;from&nbsp;gradient&nbsp;going&nbsp;from&nbsp;no&nbsp;color&nbsp;to&nbsp;</span><span class="mtk9">`c_endColor`&nbsp;</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bandColor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.from_gradient</span><span class="mtk13">(</span><span class="mtk33">gradientStep</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">0</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">stepsInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">na</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">endColor</span><span class="mtk13">)</span></span><br><span><span class="mtk33">bandTopPlotID</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">hline</span><span class="mtk13">(</span><span class="mtk12">100</span><span class="mtk18">,</span><span class="mtk1">&nbsp;&nbsp;</span><span class="mtk29">"Upper&nbsp;Band"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">color.silver</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">hline.style_dashed</span><span class="mtk13">)</span></span><br><span><span class="mtk33">bandBotPlotID</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">hline</span><span class="mtk13">(</span><span class="mtk12">-100</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Lower&nbsp;Band"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">color.silver</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">hline.style_dashed</span><span class="mtk13">)</span></span><br><span><span class="mtk16">fill</span><span class="mtk13">(</span><span class="mtk33">bandTopPlotID</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">bandBotPlotID</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">bandColor</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">title</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Band"</span><span class="mtk13">)</span></span><br></code></div>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">注意：</font></font></p>
<ul>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">信号图使用与上例相同的基色和渐变。但是，我们将线宽从默认的 1 增加到了 2。它是我们视觉效果中最重要的组成部分；增加线宽是一种使其更加突出的方法，并确保用户不会被波段分散注意力，波段比原来的扁平米色更加繁忙。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">填充必须保持不显眼，原因有二。首先，它对视觉效果而言是次要的，因为它提供了补充信息，即信号处于牛市/熊市区域的持续时间。其次，由于填充的 z 索引比图大，因此填充将覆盖信号图。出于这些原因，我们将填充的基色设置为相当透明的 70，这样它们就不会遮盖图。用于波段的渐变从完全没有颜色开始（参见
</font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dfrom_gradient"><font style="vertical-align: inherit;">color.from_gradient()</font></a><font style="vertical-align: inherit;">调用中
</font><font style="vertical-align: inherit;">
用作参数的</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#var_na"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">na</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;"> 
），然后从条件 color 变量包含的输入变为基本牛市/熊市颜色</font><font style="vertical-align: inherit;">。</font></font><code dir="auto">bottom_color</code><font style="vertical-align: inherit;"></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dfrom_gradient"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font><code dir="auto">c_endColor</code><font style="vertical-align: inherit;"></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们为用户提供线和带的不同的牛/熊颜色选择。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">当我们计算变量时，我们在</font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_ta%7Bdot%7Dbarssince"><font style="vertical-align: inherit;">ta.barssince()</font></a><font style="vertical-align: inherit;">
上
</font></font><code dir="auto">gradientStep</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">使用
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_nz"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">nz()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;"> 
，因为在数据集的早期柱状图中，当测试条件尚未发生时，
 </font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_ta%7Bdot%7Dbarssince"><font style="vertical-align: inherit;">ta.barssince()</font></a><font style="vertical-align: inherit;">
将返回
</font><a href="https://www.tradingview.com/pine-script-reference/v5/#var_na"><font style="vertical-align: inherit;">na</font></a><font style="vertical-align: inherit;">。因为我们使用
</font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_nz"><font style="vertical-align: inherit;">nz()</font></a><font style="vertical-align: inherit;">，所以在这些情况下返回的值将被替换为零。</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_ta%7Bdot%7Dbarssince"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_ta%7Bdot%7Dbarssince"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font><a href="https://www.tradingview.com/pine-script-reference/v5/#var_na"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_nz"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font></li>
</ul>
<h2 id="mixing-transparencies" class="md-heading"><a href="#mixing-transparencies"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">混合</font></font><span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">透明胶片</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h2>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">在这个例子中，我们将 CCI 指标带向另一个方向。我们将使用从 CCI 计算出的 Donchian 通道（历史高点/低点）构建动态调整极值区域缓冲区。我们通过使它们的高度为 DC 的 1/4 来构建顶部/底部带。我们将使用动态调整回溯来计算 DC。为了调节回溯，我们将通过保持短期 ATR 与长期 ATR 的比率来计算一个简单的波动性指标。当该比率高于其最后 100 个值中的 50 个时，我们认为波动性很高。当波动性高/低时，我们会减少/增加回溯。</font></font></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们的目标是为指标用户提供：</font></font></p>
<ul>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">CCI 线使用牛市/熊市渐变色，正如我们在最近的例子中所展示的那样。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">唐奇安通道的顶部和底部带以这样的方式填充，即随着历史最高/最低点变得越来越老，它们的颜色变得越来越暗。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">一种了解波动性度量状态的方法，我们将通过用一种颜色绘制背景来实现，当波动性增加时，该颜色的强度也会增加。</font></font></li>
</ul>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">这是我们的指示器使用浅色主题的样子：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-MixingTransparencies-1.DJ-yTBxm_1VcSbJ.webp" alt="图像" width="1788" height="180" loading="lazy" decoding="async"></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">使用深色主题：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-MixingTransparencies-2.BJOvmETq_Z1CqQBv.webp" alt="图像" width="1786" height="178" loading="lazy" decoding="async"></p>
<div class="pine-colorizer not-content colorized" data-id="74"><div class="pine-colorizer__header"><a class="pine-colorizer__title" href="https://tradingview.com/pine-script-docs/en/v5/Introduction.html" target="_blank" rel="noopener"><span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™</font></font></span></a><button class="pine-colorizer__copy-btn"><div class="pine-colorizer__tooltip"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">已复制</font></font></div></button></div><code class="monaco-editor-tv-pine-light"><span><span class="mtk9">//</span><span class="mtk9 mtkb">@version=</span><span class="mtk9">5</span></span><br><span><span class="mtk16">indicator</span><span class="mtk13">(</span><span class="mtk29">"CCI&nbsp;DC"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">precision</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">6</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">GOLD_COLOR</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">#CCCC00ff</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">VIOLET_COLOR</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">#AA00FFff</span></span><br><span><span class="mtk18 mtkb">int</span><span class="mtk1">&nbsp;</span><span class="mtk33">lengthInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.int</span><span class="mtk13">(</span><span class="mtk12">20</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Length"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">minval</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk12">5</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bullColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk33">GOLD_COLOR</span><span class="mtk18">,</span><span class="mtk1">&nbsp;&nbsp;&nbsp;</span><span class="mtk29">"Bull"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bearColorInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk33">VIOLET_COLOR</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Bear"</span><span class="mtk13">)</span></span><br><span><span></span></span><br><span><span class="mtk9">//&nbsp;—————&nbsp;Function&nbsp;clamps&nbsp;`val`&nbsp;between&nbsp;`min`&nbsp;and&nbsp;</span><span class="mtk9">`max`.</span></span><br><span><span class="mtk15">clamp</span><span class="mtk13">(</span><span class="mtk33">val</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">min</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">max</span><span class="mtk13">)</span><span class="mtk1">&nbsp;</span><span class="mtk18">=&gt;</span></span><br><span><span class="mtk1">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="mtk16">math.max</span><span class="mtk13">(</span><span class="mtk33">min</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.min</span><span class="mtk13">(</span><span class="mtk33">max</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">val</span><span class="mtk13">))</span></span><br><span><span></span></span><br><span><span class="mtk9">//&nbsp;—————&nbsp;Volatility&nbsp;expressed&nbsp;as&nbsp;0-100&nbsp;value.</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">v</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.atr</span><span class="mtk13">(</span><span class="mtk33">lengthInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">/</span><span class="mtk1">&nbsp;</span><span class="mtk12">5</span><span class="mtk13">)</span><span class="mtk1">&nbsp;</span><span class="mtk18">/</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.atr</span><span class="mtk13">(</span><span class="mtk33">lengthInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">*</span><span class="mtk1">&nbsp;</span><span class="mtk12">5</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">vPct</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.percentrank</span><span class="mtk13">(</span><span class="mtk33">v</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">lengthInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">*</span><span class="mtk1">&nbsp;</span><span class="mtk12">5</span><span class="mtk13">)</span></span><br><span><span></span></span><br><span><span class="mtk9">//&nbsp;—————&nbsp;Calculate&nbsp;dynamic&nbsp;lookback&nbsp;for&nbsp;DC.&nbsp;It&nbsp;</span><span class="mtk9">increases/decreases&nbsp;on&nbsp;low/high&nbsp;volatility.</span></span><br><span><span class="mtk18 mtkb">bool</span><span class="mtk1">&nbsp;</span><span class="mtk33">highVolatility</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">vPct</span><span class="mtk1">&nbsp;</span><span class="mtk18">&gt;</span><span class="mtk1">&nbsp;</span><span class="mtk12">50</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">int</span><span class="mtk1">&nbsp;</span><span class="mtk33">lookBackMin</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">lengthInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">*</span><span class="mtk1">&nbsp;</span><span class="mtk12">2</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">int</span><span class="mtk1">&nbsp;</span><span class="mtk33">lookBackMax</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">lengthInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">*</span><span class="mtk1">&nbsp;</span><span class="mtk12">10</span></span><br><span><span class="mtk18">var</span><span class="mtk1">&nbsp;</span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">lookBack</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.avg</span><span class="mtk13">(</span><span class="mtk33">lookBackMin</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">lookBackMax</span><span class="mtk13">)</span></span><br><span><span class="mtk33">lookBack</span><span class="mtk1">&nbsp;</span><span class="mtk18">+=</span><span class="mtk1">&nbsp;</span><span class="mtk33">highVolatility</span><span class="mtk1">&nbsp;</span><span class="mtk18">?</span><span class="mtk1">&nbsp;</span><span class="mtk12">-2</span><span class="mtk1">&nbsp;</span><span class="mtk18">:</span><span class="mtk1">&nbsp;</span><span class="mtk12">2</span></span><br><span><span class="mtk33">lookBack</span><span class="mtk1">&nbsp;</span><span class="mtk18">:=</span><span class="mtk1">&nbsp;</span><span class="mtk33">clamp</span><span class="mtk13">(</span><span class="mtk33">lookBack</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">lookBackMin</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">lookBackMax</span><span class="mtk13">)</span></span><br><span><span></span></span><br><span><span class="mtk9">//&nbsp;—————&nbsp;Dynamic&nbsp;lookback&nbsp;length&nbsp;Donchian&nbsp;channel&nbsp;</span><span class="mtk9">of&nbsp;signal.</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">signal</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.cci</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">lengthInput</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;`lookBack`&nbsp;is&nbsp;a&nbsp;float;&nbsp;need&nbsp;to&nbsp;cast&nbsp;it&nbsp;to&nbsp;int&nbsp;t</span><span class="mtk9">o&nbsp;be&nbsp;used&nbsp;a&nbsp;length.</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">hiTop</span><span class="mtk1">&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.highest</span><span class="mtk13">(</span><span class="mtk33">signal</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">int</span><span class="mtk13">(</span><span class="mtk33">lookBack</span><span class="mtk13">))</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">loBot</span><span class="mtk1">&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.lowest</span><span class="mtk13">(</span><span class="mtk1">&nbsp;</span><span class="mtk33">signal</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">int</span><span class="mtk13">(</span><span class="mtk33">lookBack</span><span class="mtk13">))</span></span><br><span><span class="mtk9">//&nbsp;Get&nbsp;margin&nbsp;of&nbsp;25%&nbsp;of&nbsp;the&nbsp;DC&nbsp;height&nbsp;to&nbsp;build&nbsp;hig</span><span class="mtk9">h&nbsp;and&nbsp;low&nbsp;bands.</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">margin</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk13">(</span><span class="mtk33">hiTop</span><span class="mtk1">&nbsp;</span><span class="mtk18">-</span><span class="mtk1">&nbsp;</span><span class="mtk33">loBot</span><span class="mtk13">)</span><span class="mtk1">&nbsp;</span><span class="mtk18">/</span><span class="mtk1">&nbsp;</span><span class="mtk12">4</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">hiBot</span><span class="mtk1">&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">hiTop</span><span class="mtk1">&nbsp;</span><span class="mtk18">-</span><span class="mtk1">&nbsp;</span><span class="mtk33">margin</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">loTop</span><span class="mtk1">&nbsp;&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">loBot</span><span class="mtk1">&nbsp;</span><span class="mtk18">+</span><span class="mtk1">&nbsp;</span><span class="mtk33">margin</span></span><br><span><span class="mtk9">//&nbsp;Center&nbsp;of&nbsp;DC.</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">center</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.avg</span><span class="mtk13">(</span><span class="mtk33">hiTop</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">loBot</span><span class="mtk13">)</span></span><br><span><span></span></span><br><span><span class="mtk9">//&nbsp;—————&nbsp;Create&nbsp;colors.</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">signalColor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.from_gradient</span><span class="mtk13">(</span><span class="mtk33">signal</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">-200</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">200</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">bearColorInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">bullColorInput</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;Bands:&nbsp;Calculate&nbsp;transparencies&nbsp;so&nbsp;the&nbsp;longer&nbsp;s</span><span class="mtk9">ince&nbsp;the&nbsp;hi/lo&nbsp;has&nbsp;changed,&nbsp;</span></span><br><span><span class="mtk9">//&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the&nbsp;darker&nbsp;the&nbsp;color&nbsp;becomes.&nbsp;Cap&nbsp;highes</span><span class="mtk9">t&nbsp;transparency&nbsp;to&nbsp;90.</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">hiTransp</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">clamp</span><span class="mtk13">(</span><span class="mtk12">100</span><span class="mtk1">&nbsp;</span><span class="mtk18">-</span><span class="mtk1">&nbsp;</span><span class="mtk13">(</span><span class="mtk12">100</span><span class="mtk1">&nbsp;</span><span class="mtk18">*</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.max</span><span class="mtk13">(</span><span class="mtk12">1</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">nz</span><span class="mtk13">(</span><span class="mtk16">ta.barssince</span><span class="mtk13">(</span><span class="mtk16">ta.change</span><span class="mtk13">(</span><span class="mtk33">hiTop</span><span class="mtk13">))</span><span class="mtk1">&nbsp;</span><span class="mtk18">+</span><span class="mtk1">&nbsp;</span><span class="mtk12">1</span><span class="mtk13">))</span><span class="mtk1">&nbsp;</span><span class="mtk18">/</span><span class="mtk1">&nbsp;</span><span class="mtk12">255</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">60</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">90</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">loTransp</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk33">clamp</span><span class="mtk13">(</span><span class="mtk12">100</span><span class="mtk1">&nbsp;</span><span class="mtk18">-</span><span class="mtk1">&nbsp;</span><span class="mtk13">(</span><span class="mtk12">100</span><span class="mtk1">&nbsp;</span><span class="mtk18">*</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.max</span><span class="mtk13">(</span><span class="mtk12">1</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">nz</span><span class="mtk13">(</span><span class="mtk16">ta.barssince</span><span class="mtk13">(</span><span class="mtk16">ta.change</span><span class="mtk13">(</span><span class="mtk33">loBot</span><span class="mtk13">))</span><span class="mtk1">&nbsp;</span><span class="mtk18">+</span><span class="mtk1">&nbsp;</span><span class="mtk12">1</span><span class="mtk13">))</span><span class="mtk1">&nbsp;</span><span class="mtk18">/</span><span class="mtk1">&nbsp;</span><span class="mtk12">255</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">60</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">90</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">hiColor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk33">bullColorInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">hiTransp</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">loColor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk33">bearColorInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">loTransp</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;Background:&nbsp;Rescale&nbsp;the&nbsp;0-100&nbsp;range&nbsp;of&nbsp;`vPct`&nbsp;t</span><span class="mtk9">o&nbsp;0-25&nbsp;to&nbsp;create&nbsp;75-100&nbsp;transparencies.</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">bgColor</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk11">color.gray</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">100</span><span class="mtk1">&nbsp;</span><span class="mtk18">-</span><span class="mtk1">&nbsp;</span><span class="mtk13">(</span><span class="mtk33">vPct</span><span class="mtk1">&nbsp;</span><span class="mtk18">/</span><span class="mtk1">&nbsp;</span><span class="mtk12">4</span><span class="mtk13">))</span></span><br><span><span></span></span><br><span><span class="mtk9">//&nbsp;—————&nbsp;Plots</span></span><br><span><span class="mtk9">//&nbsp;Invisible&nbsp;lines&nbsp;for&nbsp;band&nbsp;fills.</span></span><br><span><span class="mtk33">hiTopPlotID</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">hiTop</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">color</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">na</span><span class="mtk13">)</span></span><br><span><span class="mtk33">hiBotPlotID</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">hiBot</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">color</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">na</span><span class="mtk13">)</span></span><br><span><span class="mtk33">loTopPlotID</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">loTop</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">color</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">na</span><span class="mtk13">)</span></span><br><span><span class="mtk33">loBotPlotID</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">loBot</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">color</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk11">na</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;Plot&nbsp;signal&nbsp;and&nbsp;centerline.</span></span><br><span><span class="mtk33">p_signal</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">signal</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"CCI"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">signalColor</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">2</span><span class="mtk13">)</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">center</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Centerline"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">color.silver</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">1</span><span class="mtk13">)</span></span><br><span><span></span></span><br><span><span class="mtk9">//&nbsp;Fill&nbsp;the&nbsp;bands.</span></span><br><span><span class="mtk16">fill</span><span class="mtk13">(</span><span class="mtk33">hiTopPlotID</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">hiBotPlotID</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">hiColor</span><span class="mtk13">)</span></span><br><span><span class="mtk16">fill</span><span class="mtk13">(</span><span class="mtk33">loTopPlotID</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">loBotPlotID</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">loColor</span><span class="mtk13">)</span></span><br><span><span></span></span><br><span><span class="mtk9">//&nbsp;—————&nbsp;Background.</span></span><br><span><span class="mtk16">bgcolor</span><span class="mtk13">(</span><span class="mtk33">bgColor</span><span class="mtk13">)</span></span><br></code></div>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">注意：</font></font></p>
<ul>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们将背景的透明度限制在 100-75 范围内，这样就不会显得太过突兀。我们还使用了中性色，这样就不会太过分散注意力。背景越暗，我们的波动性测量值就越高。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们还将波段填充的透明度值限制在 60 到 90 之间。我们使用 90，这样当发现新的高/低点并重置梯度时，起始透明度会使颜色稍微可见。我们不使用低于 60 的透明度，因为我们不希望这些波段隐藏信号线。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">我们使用非常方便的
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_ta%7Bdot%7Dpercentrank"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">ta.percentrank()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
函数从测量波动性的 ATR 比率生成 0-100 的值。将未知范围的值转换为可用于生成透明度的已知值非常有用。</font></font></li>
<li><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">因为我们必须在脚本中三次限制值，所以我们编写了一个
</font></font><code dir="auto">f_clamp()</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">函数，而不是明确地编写三次逻辑代码。</font></font></li>
</ul>
<h2 id="tips" class="md-heading"><a href="#tips"> <span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">提示</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h2>
<h3 id="designing-usable-colors-schemes" class="md-heading"><a href="#designing-usable-colors-schemes"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">设计可用的颜色</font></font><span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">方案</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h3>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">如果您编写的脚本是为其他交易者准备的，请尽量避免使用在某些环境下效果不佳的颜色，无论是用于绘图、标签、表格还是填充。至少，测试您的视觉效果，以确保它们在浅色和深色 TradingView 主题下都能表现良好；它们是最常用的。例如，应避免使用黑色和白色等颜色。</font></font></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">构建适当的输入，为脚本用户提供灵活性，使其能够根据特定环境调整脚本的视觉效果。</font></font></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">注意构建您使用的颜色的视觉层次结构，使其与脚本视觉组件的相对重要性相匹配。优秀的设计师知道如何实现颜色和重量的最佳平衡，以便眼睛自然地被设计中最重要的元素吸引。当你让一切都脱颖而出时，什么也不会。通过淡化周围的视觉效果，为某些元素腾出空间，使其脱颖而出。</font></font></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">在输入中提供多种颜色预设（而不是单一可更改的颜色）可以帮助色彩挑战用户。我们的</font></font><a href="https://www.tradingview.com/script/Jdw7wW2g-Technical-Ratings/"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">技术评级</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
展示了实现此目的的一种方法。</font></font></p>
<h3 id="plot-crisp-lines" class="md-heading"><a href="#plot-crisp-lines"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">绘制清晰的</font></font><span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">线条</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h3>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">最好使用零透明度来绘制视觉效果中的重要线条，以保持其清晰。这样，它们将更精确地显示填充。请记住，填充的 z 索引比绘图更高，因此它们被放置在绘图之上。稍微增加线条的宽度也可以使其脱颖而出。</font></font></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">如果您希望某个特殊图脱颖而出，也可以通过对同一条线使用多个图来使其更加重要。以下是我们通过调节图的连续宽度和透明度来实现此目的的示例：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-PlotCrispLines-1.CJkrlPd__Z1Me4S0.webp" alt="图像" width="1794" height="206" loading="lazy" decoding="async"></p>
<div class="pine-colorizer not-content colorized" data-id="2av"><div class="pine-colorizer__header"><a class="pine-colorizer__title" href="https://tradingview.com/pine-script-docs/en/v5/Introduction.html" target="_blank" rel="noopener"><span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™</font></font></span></a><button class="pine-colorizer__copy-btn"><div class="pine-colorizer__tooltip"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">已复制</font></font></div></button></div><code class="monaco-editor-tv-pine-light"><span><span class="mtk9">//</span><span class="mtk9 mtkb">@version=</span><span class="mtk9">5</span></span><br><span><span class="mtk16">indicator</span><span class="mtk13">(</span><span class="mtk29">""</span><span class="mtk13">)</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk11">high</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk11">color.orange</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">80</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">8</span><span class="mtk13">)</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk11">high</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk11">color.orange</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">60</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">4</span><span class="mtk13">)</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk11">high</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk11">color.orange</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">00</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">1</span><span class="mtk13">)</span></span><br><span><span></span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk11">hl2</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk11">color.orange</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">60</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">4</span><span class="mtk13">)</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk11">hl2</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk11">color.orange</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">00</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">1</span><span class="mtk13">)</span></span><br><span><span></span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk11">low</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk11">color.orange</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">0</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">1</span><span class="mtk13">)</span></span><br></code></div>
<h3 id="customize-gradients" class="md-heading"><a href="#customize-gradients"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">自定义</font></font><span class="fancy-wrap"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">渐变</font></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h3>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">构建渐变时，请根据其适用的视觉效果进行调整。例如，如果您使用渐变来为蜡烛上色，通常最好将渐变中的步骤数限制为 10 或更少，因为眼睛更难感知离散对象的强度变化。正如我们在示例中所做的那样，限制最小和最大透明度级别，以便您的视觉元素保持可见，并且在不需要时不会显得过于突兀。</font></font></p>
<h3 id="color-selection-through-script-settings" class="md-heading"><a href="#color-selection-through-script-settings"><font style="vertical-align: inherit;"><span class="fancy-wrap"><font style="vertical-align: inherit;">通过脚本设置</font></span><font style="vertical-align: inherit;">选择颜色</font></font><span class="fancy-wrap"><font style="vertical-align: inherit;"></font><span class="icon icon-link" aria-hidden="true"><svg width="28" height="28" viewBox="0 0 28 28"><g fill="currentColor" clip-path="url(#a)"><path d="M14.908 5.558a5.326 5.326 0 1 1 7.533 7.533l-3.236 3.236-1.061-1.061 3.236-3.236a3.826 3.826 0 1 0-5.411-5.411l-3.236 3.236-1.06-1.06 3.235-3.237ZM5.56 14.907a5.326 5.326 0 0 0 7.532 7.533l3.236-3.236-1.061-1.061-3.236 3.236a3.826 3.826 0 1 1-5.411-5.411l3.236-3.236-1.061-1.06-3.236 3.235Z"></path><path d="m16.346 10.592-5.753 5.753 1.061 1.06 5.753-5.752-1.06-1.06Z"></path></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h28v28H0z"></path></clipPath></defs></svg></span></span></a></h3>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">您在脚本中使用的颜色类型会影响脚本用户如何更改脚本视觉效果的颜色。只要您不使用必须在运行时计算 RGBA 分量的颜色，脚本用户就可以通过转到脚本的“设置/样式”选项卡来修改您使用的颜色。本页上的第一个示例脚本符合该标准，以下屏幕截图显示了我们如何使用脚本的“设置/样式”选项卡更改第一个移动平均线的颜色：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-ColorsSelection-1.BUBojPf-_Z2ueW1w.webp" alt="图像" width="1796" height="784" loading="lazy" decoding="async"></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">如果您的脚本使用计算颜色，即至少有一个 RGBA 组件只能在运行时才知道的颜色，则“设置/样式”选项卡将不会为用户提供他们可以用来修改绘图颜色的常用颜色小部件。同一脚本中未使用计算颜色的绘图也会受到影响。例如，在此脚本中，我们的第一个
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_plot"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">plot()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
调用使用计算颜色，而第二个调用则没有：</font></font></p>
<div class="pine-colorizer not-content colorized" data-id="7p2"><div class="pine-colorizer__header"><a class="pine-colorizer__title" href="https://tradingview.com/pine-script-docs/en/v5/Introduction.html" target="_blank" rel="noopener"><span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™</font></font></span></a><button class="pine-colorizer__copy-btn"><div class="pine-colorizer__tooltip"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">已复制</font></font></div></button></div><code class="monaco-editor-tv-pine-light"><span><span class="mtk9">//</span><span class="mtk9 mtkb">@version=</span><span class="mtk9">5</span></span><br><span><span class="mtk16">indicator</span><span class="mtk13">(</span><span class="mtk29">"Calculated&nbsp;colors"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">true</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">ma</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.sma</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">20</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">maHeight</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.percentrank</span><span class="mtk13">(</span><span class="mtk33">ma</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">100</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">transparency</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.min</span><span class="mtk13">(</span><span class="mtk12">80</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">100</span><span class="mtk1">&nbsp;</span><span class="mtk18">-</span><span class="mtk1">&nbsp;</span><span class="mtk33">maHeight</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;This&nbsp;plot&nbsp;uses&nbsp;a&nbsp;calculated&nbsp;color.</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">ma</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"MA1"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.rgb</span><span class="mtk13">(</span><span class="mtk12">156</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">39</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">176</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">transparency</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">2</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;This&nbsp;plot&nbsp;does&nbsp;not&nbsp;use&nbsp;a&nbsp;calculated&nbsp;color.</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Close"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">color.blue</span><span class="mtk13">)</span></span><br></code></div>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">第一个图中使用的颜色是计算颜色，因为其透明度只能在运行时知道。它是使用移动平均线相对于其过去 100 个值的相对位置计算得出的。过去值低于当前值的百分比越大，0-100 的值就越高</font></font><code dir="auto">maHeight</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">。由于我们希望颜色在</font></font><code dir="auto">maHeight</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">100 时最暗，因此我们从中减去 100 以获得零透明度。我们还将计算
</font></font><code dir="auto">transparency</code><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">值限制为最大值 80，以便它始终保持可见。</font></font></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">因为我们的脚本中使用了计算的颜色，“设置/样式”选项卡将不会显示任何颜色小部件：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-ColorsSelection-2._-9pvdeu_n0rsG.webp" alt="图像" width="1530" height="588" loading="lazy" decoding="async"></p>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">使脚本用户能够控制所用颜色的解决方案是向他们提供自定义输入，正如我们在此处所做的那样：</font></font></p>
<p><img src="/pine-script-docs/_astro/Colors-ColorsSelection-3.EbPWeJv__1bknIE.webp" alt="图像" width="1532" height="576" loading="lazy" decoding="async"></p>
<div class="pine-colorizer not-content colorized" data-id="4e3"><div class="pine-colorizer__header"><a class="pine-colorizer__title" href="https://tradingview.com/pine-script-docs/en/v5/Introduction.html" target="_blank" rel="noopener"><span><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Pine Script™</font></font></span></a><button class="pine-colorizer__copy-btn"><div class="pine-colorizer__tooltip"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">已复制</font></font></div></button></div><code class="monaco-editor-tv-pine-light"><span><span class="mtk9">//</span><span class="mtk9 mtkb">@version=</span><span class="mtk9">5</span></span><br><span><span class="mtk16">indicator</span><span class="mtk13">(</span><span class="mtk29">"Calculated&nbsp;colors"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">""</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk11">true</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">maInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk11">color.purple</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"MA"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">color</span><span class="mtk1">&nbsp;</span><span class="mtk33">closeInput</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">input.color</span><span class="mtk13">(</span><span class="mtk11">color.blue</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Close"</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">ma</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.sma</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">20</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">maHeight</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">ta.percentrank</span><span class="mtk13">(</span><span class="mtk33">ma</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">100</span><span class="mtk13">)</span></span><br><span><span class="mtk18 mtkb">float</span><span class="mtk1">&nbsp;</span><span class="mtk33">transparency</span><span class="mtk1">&nbsp;</span><span class="mtk18">=</span><span class="mtk1">&nbsp;</span><span class="mtk16">math.min</span><span class="mtk13">(</span><span class="mtk12">80</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">100</span><span class="mtk1">&nbsp;</span><span class="mtk18">-</span><span class="mtk1">&nbsp;</span><span class="mtk33">maHeight</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;This&nbsp;plot&nbsp;uses&nbsp;a&nbsp;calculated&nbsp;color.</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk33">ma</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"MA1"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk16">color.new</span><span class="mtk13">(</span><span class="mtk33">maInput</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">transparency</span><span class="mtk13">)</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk12">2</span><span class="mtk13">)</span></span><br><span><span class="mtk9">//&nbsp;This&nbsp;plot&nbsp;does&nbsp;not&nbsp;use&nbsp;a&nbsp;calculated&nbsp;color.</span></span><br><span><span class="mtk16">plot</span><span class="mtk13">(</span><span class="mtk11">close</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk29">"Close"</span><span class="mtk18">,</span><span class="mtk1">&nbsp;</span><span class="mtk33">closeInput</span><span class="mtk13">)</span></span><br></code></div>
<p><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">请注意，我们的脚本的“设置”现在显示“输入”选项卡，我们在其中创建了两个颜色输入。第一个使用
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#const_color%7Bdot%7Dpurple"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.purple</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
作为其默认值。无论脚本用户是否更改该颜色，它都将在
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_color%7Bdot%7Dnew"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.new()调用中用于在</font></font></a><font style="vertical-align: inherit;"></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_plot"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">plot()</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">调用
中生成计算的透明度
。第二个输入使用我们之前在</font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_plot"><font style="vertical-align: inherit;">plot()</font></a><font style="vertical-align: inherit;">调用中使用的
内置
</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#const_color%7Bdot%7Dblue"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.blue</font></font></a><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
颜色
作为其默认值，并在第二个</font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_plot"><font style="vertical-align: inherit;">plot()</font></a><font style="vertical-align: inherit;">调用
中按原样使用它
</font><font style="vertical-align: inherit;">
。</font></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_plot"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font><a href="https://www.tradingview.com/pine-script-reference/v5/#fun_plot"><font style="vertical-align: inherit;"></font></a><font style="vertical-align: inherit;"></font></p>      </div> <div class="pagination-buttons not-content" data-astro-cid-xgirumru=""> <a href="/pine-script-docs/concepts/chart-information" class="pagination-card" data-pagefind-ignore="" data-astro-cid-assl6cvf=""> <p data-astro-cid-assl6cvf=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">以前的</font></font></p> <h4 class="pagination-card-header" data-astro-cid-assl6cvf=""> <svg width="28" height="28" viewBox="0 0 28 28" data-astro-cid-assl6cvf="" data-icon="theme/arrow-back">  <use xlink:href="#ai:local:theme/arrow-back"></use>  </svg><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">图表信息  </font></font></h4> </a>  <a href="/pine-script-docs/concepts/fills" class="pagination-card" data-pagefind-ignore="" data-astro-cid-assl6cvf=""> <p data-astro-cid-assl6cvf=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">下一个</font></font></p> <h4 class="pagination-card-header" data-astro-cid-assl6cvf=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">  填充</font></font><svg width="28" height="28" viewBox="0 0 28 28" data-astro-cid-assl6cvf="" data-icon="theme/arrow">  <symbol id="ai:local:theme/arrow"><path fill="none" stroke="var(--arrow-fill-color, #131722)" d="m11 8 6 6-6 6"></path></symbol><use xlink:href="#ai:local:theme/arrow"></use>  </svg> </h4> </a>  </div>  </main>   <div id="toc" data-pagefind-ignore="" data-astro-cid-oor6cujd=""><aside class="document-toc-container ml-4 w-48" data-astro-cid-oor6cujd=""><section data-astro-cid-oor6cujd=""><header data-astro-cid-oor6cujd=""><h2 class="toc-header" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">在本页</font></font></h2></header><ul class="" id="toc-entries" data-astro-cid-oor6cujd=""><a href="#colors" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-2" data-current="" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">颜色</font></font></li></a><a href="#introduction" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-3" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">介绍</font></font></li></a><a href="#transparency" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-4" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">透明度</font></font></li></a><a href="#z-index" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-4" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Z 索引</font></font></li></a><a href="#constant-colors" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-3" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">恒定颜色</font></font></li></a><a href="#conditional-coloring" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-3" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">条件着色</font></font></li></a><a href="#calculated-colors" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-3" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">计算颜色</font></font></li></a><a href="#colornew" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-4" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.new()</font></font></li></a><a href="#colorrgb" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-4" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.rgb()</font></font></li></a><a href="#colorfrom_gradient" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-4" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">color.from_gradient()</font></font></li></a><a href="#mixing-transparencies" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-3" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">混合透明胶片</font></font></li></a><a href="#tips" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-3" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">提示</font></font></li></a><a href="#designing-usable-colors-schemes" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-4" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">设计可用的颜色方案</font></font></li></a><a href="#plot-crisp-lines" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-4" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">绘制清晰的线条</font></font></li></a><a href="#customize-gradients" class="document-toc-link" data-astro-cid-oor6cujd=""><li class="l-4" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">自定义渐变</font></font></li></a><a href="#color-selection-through-script-settings" class="document-toc-link" data-astro-cid-oor6cujd="" aria-current="true"><li class="l-4" data-astro-cid-oor6cujd=""><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">通过脚本设置选择颜色</font></font></li></a></ul><a class="back-top" href="#top" data-astro-cid-oor6cujd=""><svg width="16" height="16" viewBox="0 0 16 16" data-astro-cid-oor6cujd="" data-icon="theme/back-top">  <symbol id="ai:local:theme/back-top"><path fill="currentColor" fill-rule="evenodd" d="M6.25 12.5A2.75 2.75 0 0 0 9 9.75V4.56L6.78 6.78a.75.75 0 0 1-1.06-1.06l3.5-3.5a.75.75 0 0 1 1.06 0l3.5 3.5a.75.75 0 0 1-1.06 1.06L10.5 4.56v5.19a4.25 4.25 0 0 1-8.5 0v-1a.75.75 0 0 1 1.5 0v1a2.75 2.75 0 0 0 2.75 2.75Z" clip-rule="evenodd"></path></symbol><use xlink:href="#ai:local:theme/back-top"></use>  </svg><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
回到顶部
</font></font></a></section></aside></div> </main> </div>            <script data-astro-rerun="" src="/pine-script-docs/static/pine-highlighter-v06.js" data-astro-exec=""></script><div id="goog-gt-tt" class="VIpgJd-yAWNEb-L7lbkb skiptranslate" style="border-radius: 12px; margin: 0 0 0 -23px; padding: 0; font-family: 'Google Sans', Arial, sans-serif;" data-id=""><div id="goog-gt-vt" class="VIpgJd-yAWNEb-hvhgNd"><div class=" VIpgJd-yAWNEb-hvhgNd-l4eHX-i3jM8c"><img src="https://fonts.gstatic.com/s/i/productlogos/translate/v14/24px.svg" width="24" height="24" alt=""></div><div class=" VIpgJd-yAWNEb-hvhgNd-k77Iif-i3jM8c"><div class="VIpgJd-yAWNEb-hvhgNd-IuizWc" dir="ltr">Original text</div><div id="goog-gt-original-text" class="VIpgJd-yAWNEb-nVMfcd-fmcmS VIpgJd-yAWNEb-hvhgNd-axAV1"></div></div><div class="VIpgJd-yAWNEb-hvhgNd-N7Eqid ltr"><div class="VIpgJd-yAWNEb-hvhgNd-N7Eqid-B7I4Od ltr" dir="ltr"><div class="VIpgJd-yAWNEb-hvhgNd-UTujCb">Rate this translation</div><div class="VIpgJd-yAWNEb-hvhgNd-eO9mKe">Your feedback will be used to help improve Google Translate</div></div><div class="VIpgJd-yAWNEb-hvhgNd-xgov5 ltr"><button id="goog-gt-thumbUpButton" type="button" class="VIpgJd-yAWNEb-hvhgNd-bgm6sf" title="Good translation" aria-label="Good translation" aria-pressed="false"><span id="goog-gt-thumbUpIcon"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M21 7h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 0S7.08 6.85 7 7H2v13h16c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73V9c0-1.1-.9-2-2-2zM7 18H4V9h3v9zm14-7l-3 7H9V8l4.34-4.34L12 9h9v2z"></path></svg></span><span id="goog-gt-thumbUpIconFilled"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M21 7h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 0S7.08 6.85 7 7v13h11c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73V9c0-1.1-.9-2-2-2zM5 7H1v13h4V7z"></path></svg></span></button><button id="goog-gt-thumbDownButton" type="button" class="VIpgJd-yAWNEb-hvhgNd-bgm6sf" title="Poor translation" aria-label="Poor translation" aria-pressed="false"><span id="goog-gt-thumbDownIcon"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M3 17h6.31l-.95 4.57-.03.32c0 .*********** 1.06L9.83 24s7.09-6.85 7.17-7h5V4H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v2c0 1.1.9 2 2 2zM17 6h3v9h-3V6zM3 13l3-7h9v10l-4.34 4.34L12 15H3v-2z"></path></svg></span><span id="goog-gt-thumbDownIconFilled"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M3 17h6.31l-.95 4.57-.03.32c0 .*********** 1.06L9.83 24s7.09-6.85 7.17-7V4H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v2c0 1.1.9 2 2 2zm16 0h4V4h-4v13z"></path></svg></span></button></div></div><div id="goog-gt-votingHiddenPane" class="VIpgJd-yAWNEb-hvhgNd-aXYTce"><form id="goog-gt-votingForm" action="//translate.googleapis.com/translate_voting?client=te_lib" method="post" target="votingFrame" class="VIpgJd-yAWNEb-hvhgNd-aXYTce"><input type="text" name="sl" id="goog-gt-votingInputSrcLang"><input type="text" name="tl" id="goog-gt-votingInputTrgLang"><input type="text" name="query" id="goog-gt-votingInputSrcText"><input type="text" name="gtrans" id="goog-gt-votingInputTrgText"><input type="text" name="vote" id="goog-gt-votingInputVote"></form><iframe name="votingFrame" frameborder="0"></iframe></div></div></div> </body></html>