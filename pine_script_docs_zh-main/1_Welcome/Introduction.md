# 简介



<img src="https://www.tradingview.com/pine-script-docs/en/v4/_images/Pine_Script_logo_text.png" alt="Pine Script logo" style="zoom: 40%;float:right" />

Pine Script 是 TradingView 的编程语言。它允许交易者创建自己的交易工具并在我们的服务器上运行。我们将Pine设计成一种轻量级但功能强大的语言，用于开发指标和策略，然后可以进行回测。TradingView的大多数内置指标都是用Pine编写的，我们活跃的Pine开发者社区已经发布了超过10万个社区脚本。

我们的明确目标是让尽可能多的人了解Pine，并使之易于理解。Pine是基于云的，因此与客户端编程语言不同。虽然我们很可能不会把Pine发展成一种成熟的语言，但我们确实在不断地改进它，并且总是乐于考虑对新功能的要求。

由于每个脚本都使用云中的计算资源，我们必须加以限制，以便在我们的用户中公平地分享这些资源。我们努力设置尽可能少的限制，但当然也要根据平台顺利运行的需要来实施。限制适用于额外请求的数据量、执行时间、内存使用和脚本大小。
