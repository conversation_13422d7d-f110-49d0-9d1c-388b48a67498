# 我在哪里可以获得更多信息？

- [所有 Pine Script™ 运算符、变量和函数的描述可以在参考手册](https://www.tradingview.com/pine-script-reference/v5/)中找到 。
- 使用 TradingView 的内置脚本之一中的代码开始。打开一个新图表，然后单击工具栏上的“Pine Editor”按钮。进入编辑器窗口后，单击“打开”按钮，然后从下拉列表中选择“内置脚本...”以打开包含 TradingView 内置脚本列表的对话框。
- 有一个 TradingView 公共聊天室专门用于 Pine Script™[问答](https://www.tradingview.com/chat/#BfmVowG1TZkKO235) ，我们社区的活跃开发人员可以互相帮助。
- 有关 Pine Script™（以及其他功能）的主要版本和修改的信息定期发布在 [TradingView 的博客](https://www.tradingview.com/blog/en/category/market-analysis/pine/)上。
- TradingView 的[社区脚本](https://www.tradingview.com/script/)包含所有用户发布的脚本。还可以使用“指标和策略”按钮和脚本搜索对话框的“社区脚本”选项卡从图表中访问它们。

## [外部资源](https://www.tradingview.com/pine-script-docs/en/v5/Where_can_I_get_more_information.html#id1)

- [TradingView 上的 PineCoders](https://www.tradingview.com/u/PineCoders/#published-scripts)帐户为 Pine Script™ 程序员发布有用的信息。他们的[网站](https://www.pinecoders.com/)上也有内容。
- [Kodify](https://kodify.net/tradingview-programming-articles)为初学者和经验丰富的程序员提供了有关各种主题的 TradingView 教程。主题包括绘图、警报、策略订单以及完整的示例指标和策略。
- [Backtest Rookies](https://backtest-rookies.com/category/tradingview)发布了高质量的博客文章，重点关注在 Pine Script™ 中实现特定任务。
- [您可以在StackOverflow](https://stackoverflow.com/questions/tagged/pine-script)`[pine-script]`上的标签中询问有关 Pine Script™ 编程的问题。