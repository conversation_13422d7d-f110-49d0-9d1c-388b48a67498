{
    /* Single-line C-style comment */
    "max_open_trades": 3,
    /*
     * Multi-line C-style comment
     */
    "stake_currency": "BTC",
    "stake_amount": 0.05,
    "fiat_display_currency": "USD", // C++-style comment
    "amount_reserve_percent": 0.05, // And more, tabs before this comment
    "dry_run": false,
    "timeframe": "5m",
    "trailing_stop": false,
    "trailing_stop_positive": 0.005,
    "trailing_stop_positive_offset": 0.0051,
    "trailing_only_offset_is_reached": false,
    "minimal_roi": {
        "40": 0.0,
        "30": 0.01,
        "20": 0.02,
        "0": 0.04
    },
    "stoploss": -0.10,
    "unfilledtimeout": {
        "buy": 10,
        "sell": 30, // Trailing comma should also be accepted now
    },
    "bid_strategy": {
        "use_order_book": false,
        "ask_last_balance": 0.0,
        "order_book_top": 1,
        "check_depth_of_market": {
            "enabled": false,
            "bids_to_ask_delta": 1
        }
    },
    "ask_strategy": {
        "use_order_book": false,
        "order_book_min": 1,
        "order_book_max": 9
    },
    "order_types": {
        "buy": "limit",
        "sell": "limit",
        "stoploss": "market",
        "stoploss_on_exchange": false,
        "stoploss_on_exchange_interval": 60
    },
    "order_time_in_force": {
        "buy": "gtc",
        "sell": "gtc"
    },
    "pairlist": {
        "method": "VolumePairList",
        "config": {
            "number_assets": 20,
            "sort_key": "quoteVolume",
            "precision_filter": false
        }
    },
    "exchange": {
        "name": "binance",
        "sandbox": false,
        "key": "your_exchange_key",
        "secret": "your_exchange_secret",
        "password": "",
        "ccxt_config": {
            "enableRateLimit": true
        },
        "ccxt_async_config": {
            "enableRateLimit": false,
            "rateLimit": 500,
            "aiohttp_trust_env": false
        },
        "pair_whitelist": [
            "ETH/BTC",
            "LTC/BTC",
            "ETC/BTC",
            "DASH/BTC",
            "ZEC/BTC",
            "XLM/BTC",
            "NXT/BTC",
            "TRX/BTC",
            "ADA/BTC",
            "XMR/BTC"
        ],
        "pair_blacklist": [
            "DOGE/BTC"
        ],
        "outdated_offset": 5,
        "markets_refresh_interval": 60
    },
    "edge": {
        "enabled": false,
        "process_throttle_secs": 3600,
        "calculate_since_number_of_days": 7,
        "allowed_risk": 0.01,
        "stoploss_range_min": -0.01,
        "stoploss_range_max": -0.1,
        "stoploss_range_step": -0.01,
        "minimum_winrate": 0.60,
        "minimum_expectancy": 0.20,
        "min_trade_number": 10,
        "max_trade_duration_minute": 1440,
        "remove_pumps": false
    },
    "telegram": {
        // We can now comment out some settings
        //        "enabled": true,
        "enabled": false,
        "token": "your_telegram_token",
        "chat_id": "your_telegram_chat_id"
    },
    "api_server": {
        "enabled": false,
        "listen_ip_address": "127.0.0.1",
        "listen_port": 8080,
        "username": "freqtrader",
        "password": "SuperSecurePassword"
    },
    "db_url": "sqlite:///tradesv3.sqlite",
    "initial_state": "running",
    "forcebuy_enable": false,
    "internals": {
        "process_throttle_secs": 5
    },
    "strategy": "DefaultStrategy",
    "strategy_path": "user_data/strategies/"
}