import pytest
import pandas as pd
import numpy as np
from datetime import datetime
from pandas import DataFrame

from freqtrade.strategy import IntParameter, DecimalParameter
from freqtrade.strategy.interface import IStrategy
from user_data.strategies.hybrid_rsi_breakout_strategy import HybridRsiBreakoutStrategy
from tests.conftest import get_patched_exchange


def test_hybrid_rsi_breakout_strategy_structure():
    """
    Test the structure of the strategy class
    """
    strategy = HybridRsiBreakoutStrategy({})
    
    # Test strategy metadata
    assert strategy.INTERFACE_VERSION == 3
    assert strategy.can_short is False
    assert strategy.timeframe == "5m"
    assert strategy.process_only_new_candles is False
    assert strategy.use_exit_signal is True
    assert strategy.startup_candle_count >= 200
    
    # Test strategy parameters
    assert isinstance(strategy.adx_length, IntParameter)
    assert isinstance(strategy.adx_smoothing, IntParameter)
    assert isinstance(strategy.adx_threshold, DecimalParameter)
    assert isinstance(strategy.ema_length, IntParameter)
    assert isinstance(strategy.rsi_length, IntParameter)
    assert isinstance(strategy.rsi_buy, IntParameter)
    assert isinstance(strategy.exit_rsi, IntParameter)
    assert isinstance(strategy.breakout_length, IntParameter)
    assert isinstance(strategy.atr_length, IntParameter)
    assert isinstance(strategy.atr_multiplier, DecimalParameter)
    
    # Test default parameter values
    assert strategy.adx_length.value == 14
    assert strategy.adx_smoothing.value == 14
    assert strategy.adx_threshold.value == 20
    assert strategy.ema_length.value == 200
    assert strategy.rsi_length.value == 14
    assert strategy.rsi_buy.value == 40
    assert strategy.exit_rsi.value == 50
    assert strategy.breakout_length.value == 20
    assert strategy.atr_length.value == 14
    assert strategy.atr_multiplier.value == 2.0


def test_populate_indicators(default_conf):
    """
    Test indicator calculations
    """
    default_conf["timeframe"] = "5m"
    strategy = HybridRsiBreakoutStrategy(default_conf)
    
    # Create test dataframe
    length = 200
    df = DataFrame(
        {
            'date': pd.date_range(start='2019-01-01', periods=length, freq='5min'),
            'open': np.random.random(length) * 100,
            'high': np.random.random(length) * 100,
            'low': np.random.random(length) * 100,
            'close': np.random.random(length) * 100,
            'volume': np.random.random(length) * 1000,
        }
    )
    
    # Test indicator calculation
    result = strategy.populate_indicators(df.copy(), {'pair': 'BTC/USDT'})
    
    # Check that all required indicators are calculated
    assert 'adx' in result.columns
    assert 'plus_di' in result.columns
    assert 'minus_di' in result.columns
    assert 'ema' in result.columns
    assert 'rsi' in result.columns
    assert 'highest_break' in result.columns
    assert 'lowest_break' in result.columns
    assert 'atr' in result.columns
    assert 'isTrending' in result.columns
    assert 'isRanging' in result.columns
    assert 'bullish' in result.columns
    assert 'bearish' in result.columns
    assert 'isLive' in result.columns
    
    # Check that regime detection works correctly
    assert (result['isTrending'] == (result['adx'] > strategy.adx_threshold.value)).all()
    assert (result['isRanging'] == (result['adx'] <= strategy.adx_threshold.value)).all()
    
    # Check that market bias detection works correctly
    assert (result['bullish'] == (result['close'] > result['ema'])).all()
    assert (result['bearish'] == (result['close'] <= result['ema'])).all()
    
    # Check that RSI signals are calculated correctly
    assert 'rsi_long' in result.columns
    assert 'rsi_long_exit' in result.columns
    
    # Check that breakout signals are calculated correctly
    assert 'long_break' in result.columns
    
    # Check that ATR calculations are correct
    assert 'atr_stop_long' in result.columns
    assert 'trail_stop_dist' in result.columns
    assert 'atr_pct' in result.columns
    assert 'atr_target_1r' in result.columns
    assert 'atr_target_2r' in result.columns
    assert 'atr_target_3r' in result.columns


def test_populate_entry_trend(default_conf):
    """
    Test entry signal generation
    """
    default_conf["timeframe"] = "5m"
    strategy = HybridRsiBreakoutStrategy(default_conf)
    
    # Create test dataframe with specific conditions
    df = DataFrame(
        {
            'date': pd.date_range(start='2019-01-01', periods=5, freq='5min'),
            'open': [100, 101, 102, 103, 104],
            'high': [102, 103, 104, 105, 106],
            'low': [98, 99, 100, 101, 102],
            'close': [101, 102, 103, 104, 105],
            'volume': [1000, 1000, 1000, 1000, 1000],
            'isLive': [True, True, True, True, True],
            'isTrending': [False, False, True, True, False],
            'isRanging': [True, True, False, False, True],
            'bullish': [True, True, True, False, True],
            'bearish': [False, False, False, True, False],
            'rsi': [35, 45, 55, 65, 35],
            'highest_break': [100, 101, 102, 103, 104],
            'lowest_break': [98, 99, 100, 101, 102],
        }
    )
    
    # Test entry signal generation
    result = strategy.populate_entry_trend(df.copy(), {'pair': 'BTC/USDT'})
    
    # Check that entry signals are generated correctly
    assert 'enter_long' in result.columns
    assert 'enter_tag' in result.columns
    
    # RSI Long entry in ranging market with bullish bias and RSI below buy threshold
    assert result.iloc[0]['enter_long'] == 1
    assert result.iloc[0]['enter_tag'] == 'RSI Long'
    
    # No entry when RSI is above buy threshold
    assert result.iloc[1]['enter_long'] == 0
    
    # Breakout Long entry in trending market with bullish bias and price above highest_break
    assert result.iloc[2]['enter_long'] == 1
    assert result.iloc[2]['enter_tag'] == 'Breakout Long'
    
    # No entry in trending market with bearish bias
    assert result.iloc[3]['enter_long'] == 0


def test_populate_exit_trend(default_conf):
    """
    Test exit signal generation
    """
    default_conf["timeframe"] = "5m"
    strategy = HybridRsiBreakoutStrategy(default_conf)
    
    # Create test dataframe with specific conditions
    df = DataFrame(
        {
            'date': pd.date_range(start='2019-01-01', periods=3, freq='5min'),
            'open': [100, 101, 102],
            'high': [102, 103, 104],
            'low': [98, 99, 100],
            'close': [101, 102, 103],
            'volume': [1000, 1000, 1000],
            'isLive': [True, True, True],
            'rsi': [45, 55, 65],
        }
    )
    
    # Test exit signal generation
    result = strategy.populate_exit_trend(df.copy(), {'pair': 'BTC/USDT'})
    
    # Check that exit signals are generated correctly
    assert 'exit_long' in result.columns
    assert 'exit_tag' in result.columns
    
    # No exit when RSI is below exit threshold
    assert result.iloc[0]['exit_long'] == 0
    
    # RSI exit when RSI is above exit threshold
    assert result.iloc[1]['exit_long'] == 1
    assert result.iloc[1]['exit_tag'] == 'Close entry(s) order RSI Long'
    
    # RSI exit when RSI is above exit threshold
    assert result.iloc[2]['exit_long'] == 1
    assert result.iloc[2]['exit_tag'] == 'Close entry(s) order RSI Long'


def test_custom_stoploss(default_conf):
    """
    Test custom stoploss logic
    """
    default_conf["timeframe"] = "5m"
    strategy = HybridRsiBreakoutStrategy(default_conf)
    
    # Create mock trade objects
    class MockTrade:
        def __init__(self, enter_tag):
            self.enter_tag = enter_tag
    
    # Test RSI trade stoploss
    rsi_trade = MockTrade("RSI Long")
    stoploss = strategy.custom_stoploss(
        pair="BTC/USDT",
        trade=rsi_trade,
        current_time=datetime.now(),
        current_rate=100,
        current_profit=0.0
    )
    assert stoploss == strategy.stoploss
    
    # Test Breakout trade stoploss (should be disabled)
    breakout_trade = MockTrade("Breakout Long")
    stoploss = strategy.custom_stoploss(
        pair="BTC/USDT",
        trade=breakout_trade,
        current_time=datetime.now(),
        current_rate=100,
        current_profit=0.0
    )
    assert stoploss == 1.0


def test_custom_exit(default_conf, mocker):
    """
    Test custom exit logic for ATR trailing stop
    """
    default_conf["timeframe"] = "5m"
    strategy = HybridRsiBreakoutStrategy(default_conf)
    
    # Create mock trade and dataframe
    class MockTrade:
        def __init__(self, enter_tag, max_rate):
            self.enter_tag = enter_tag
            self.max_rate = max_rate
    
    # Create mock dataframe with ATR value
    df = DataFrame(
        {
            'date': pd.date_range(start='2019-01-01', periods=1, freq='5min'),
            'open': [100],
            'high': [102],
            'low': [98],
            'close': [101],
            'volume': [1000],
            'atr': [2.0],
        }
    )
    
    # Mock the dataframe provider
    mocker.patch.object(strategy.dp, 'get_analyzed_dataframe', return_value=(df, None))
    
    # Test RSI trade (should not exit)
    rsi_trade = MockTrade("RSI Long", 105)
    exit_reason = strategy.custom_exit(
        pair="BTC/USDT",
        trade=rsi_trade,
        current_time=datetime.now(),
        current_rate=100,
        current_profit=0.0
    )
    assert exit_reason is None
    
    # Test Breakout trade with price above stop level (should not exit)
    breakout_trade = MockTrade("Breakout Long", 105)
    exit_reason = strategy.custom_exit(
        pair="BTC/USDT",
        trade=breakout_trade,
        current_time=datetime.now(),
        current_rate=100,
        current_profit=0.0
    )
    assert exit_reason is None
    
    # Test Breakout trade with price below stop level (should exit)
    breakout_trade = MockTrade("Breakout Long", 105)
    exit_reason = strategy.custom_exit(
        pair="BTC/USDT",
        trade=breakout_trade,
        current_time=datetime.now(),
        current_rate=95,  # Below stop level of 101 (105 - 2*2)
        current_profit=-0.05
    )
    assert exit_reason == "BO Long Exit"


def test_confirm_trade_exit(default_conf):
    """
    Test confirm_trade_exit method
    """
    default_conf["timeframe"] = "5m"
    strategy = HybridRsiBreakoutStrategy(default_conf)
    
    # Create mock trade objects
    class MockTrade:
        def __init__(self, enter_tag):
            self.enter_tag = enter_tag
    
    # Test RSI trade exit
    rsi_trade = MockTrade("RSI Long")
    result = strategy.confirm_trade_exit(
        pair="BTC/USDT",
        trade=rsi_trade,
        order_type="limit",
        amount=1.0,
        rate=100,
        time_in_force="GTC",
        exit_reason="exit_signal",
        current_time=datetime.now()
    )
    assert result is True
    
    # Test Breakout trade exit
    breakout_trade = MockTrade("Breakout Long")
    result = strategy.confirm_trade_exit(
        pair="BTC/USDT",
        trade=breakout_trade,
        order_type="limit",
        amount=1.0,
        rate=100,
        time_in_force="GTC",
        exit_reason="BO Long Exit",
        current_time=datetime.now()
    )
    assert result is True


def test_adjust_trade_position(default_conf):
    """
    Test adjust_trade_position method
    """
    default_conf["timeframe"] = "5m"
    strategy = HybridRsiBreakoutStrategy(default_conf)
    
    # Create mock trade objects with metadata
    class MockTrade:
        def __init__(self, enter_tag):
            self.enter_tag = enter_tag
            self.metadata = {}
        
        def has_metadata(self, key):
            return key in self.metadata
        
        def update_metadata(self, key, value):
            self.metadata[key] = value
    
    # Create mock dataframe with ATR value
    df = DataFrame(
        {
            'date': pd.date_range(start='2019-01-01', periods=1, freq='5min'),
            'open': [100],
            'high': [102],
            'low': [98],
            'close': [101],
            'volume': [1000],
            'atr': [2.0],
        }
    )
    
    # Mock the dataframe provider
    strategy.dp = mocker.Mock()
    strategy.dp.get_analyzed_dataframe.return_value = (df, None)
    
    # Test RSI trade (should not adjust position)
    rsi_trade = MockTrade("RSI Long")
    result = strategy.adjust_trade_position(
        trade=rsi_trade,
        current_time=datetime.now(),
        current_rate=100,
        current_profit=0.0,
        min_stake=0.01,
        max_stake=1.0,
        current_entry_rate=100,
        current_exit_rate=100,
        current_entry_profit=0.0,
        current_exit_profit=0.0
    )
    assert result is None
    assert not rsi_trade.has_metadata('atr_value')
    assert not rsi_trade.has_metadata('highest_price')
    
    # Test Breakout trade (should track highest price)
    breakout_trade = MockTrade("Breakout Long")
    result = strategy.adjust_trade_position(
        trade=breakout_trade,
        current_time=datetime.now(),
        current_rate=100,
        current_profit=0.0,
        min_stake=0.01,
        max_stake=1.0,
        current_entry_rate=100,
        current_exit_rate=100,
        current_entry_profit=0.0,
        current_exit_profit=0.0
    )
    assert result is None
    assert breakout_trade.has_metadata('atr_value')
    assert breakout_trade.has_metadata('highest_price')
    assert breakout_trade.metadata['atr_value'] == 2.0
    assert breakout_trade.metadata['highest_price'] == 100
    
    # Test updating highest price
    result = strategy.adjust_trade_position(
        trade=breakout_trade,
        current_time=datetime.now(),
        current_rate=105,  # Higher price
        current_profit=0.05,
        min_stake=0.01,
        max_stake=1.0,
        current_entry_rate=100,
        current_exit_rate=100,
        current_entry_profit=0.0,
        current_exit_profit=0.0
    )
    assert result is None
    assert breakout_trade.metadata['highest_price'] == 105


def run_backtest(config, strategy):
    """
    Helper function to run a backtest
    """
    from freqtrade.optimize.backtesting import Backtesting
    
    backtesting = Backtesting(config)
    backtesting._set_strategy(strategy)
    
    data = backtesting.load_bt_data()
    backtesting.prepare_backtest(False)
    
    stats = backtesting.backtest(
        processed=backtesting.strategyrun_processed,
        start_date=config['timerange'].split('-')[0] if config.get('timerange') else None,
        end_date=config['timerange'].split('-')[1] if config.get('timerange') and '-' in config['timerange'] else None
    )
    
    return stats


def test_backtest_performance(default_conf, mocker, caplog):
    """
    Test strategy performance with backtesting
    """
    # This test is optional and can be run manually
    # It requires downloading data first
    
    # Skip this test in automated testing
    pytest.skip("Skipping backtest performance test - run manually")
    
    default_conf["timeframe"] = "5m"
    default_conf["timerange"] = "20210101-20210201"  # Adjust timerange as needed
    default_conf["max_open_trades"] = 3
    default_conf["stake_amount"] = "unlimited"
    default_conf["dry_run_wallet"] = 1000
    default_conf["datadir"] = "./user_data/data"  # Adjust path as needed
    
    strategy = HybridRsiBreakoutStrategy(default_conf)
    
    # Run backtest
    stats = run_backtest(default_conf, strategy)
    
    # Log backtest results
    caplog.set_level(logging.INFO)
    logging.info(f"Backtest results: {stats}")
    
    # Verify backtest results meet expectations
    # These assertions should be adjusted based on expected performance
    assert stats['profit_total'] > 0
    assert stats['profit_total_abs'] > 0
    assert stats['profit_total_pct'] > 0