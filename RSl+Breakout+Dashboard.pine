// This Pine Script® code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © RugSurvivor

//@version=6
strategy("Timing：RBD", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=100)

// === TIME FILTER ===
startDate   = timestamp(2025, 1, 1, 0, 0)
isLive      = time >= startDate

// === ADX REGIME DETECTION ===
adxLen       = input.int(14, "ADX Length")
adxSmooth    = input.int(14, "ADX Smoothing")
adxThreshold = input.float(20, "ADX Threshold")
[plusDI, minusDI, adx] = ta.dmi(adxLen, adxSmooth)
isTrending  = adx > adxThreshold
isRanging   = not isTrending
regimeLabel = isTrending ? "TRENDING" : "RANGING"

// === EMA TREND FILTER ===
emaLen    = input.int(38, "EMA Trend Filter")
ema       = ta.ema(close, emaLen)
bullish   = close > ema
bearish   = close < ema
biasLabel = bullish ? "Bullish" : "Bearish"

// === RSI MEAN REVERSION ===
rsiLen     = input.int(14, "RSI Length")
rsiBuy     = input.int(40, "RSI Buy Threshold")
rsiSell    = input.int(60, "RSI Sell Threshold")
exitRSI    = input.int(50, "RSI Exit Threshold")
rsi        = ta.rsi(close, rsiLen)

rsiLong     = isLive and isRanging and rsi < rsiBuy and bullish
// rsiShort    = isLive and isRanging and rsi > rsiSell and bearish
rsiLongExit = rsi > exitRSI
// rsiShortExit= rsi < exitRSI

// === BREAKOUT ENTRIES ===
breakoutLen  = input.int(20, "Breakout Lookback")
atrLen       = input.int(14, "ATR Length")
atrMult      = input.float(2.0, "ATR Trailing Multiplier")
atr          = ta.atr(atrLen)
// pre-compute highest/lowest so they run every bar
highestBreak = ta.highest(close[1], breakoutLen)
lowestBreak  = ta.lowest(close[1], breakoutLen)

longBreak  = isLive and isTrending and bullish and close > highestBreak
// shortBreak = isLive and isTrending and bearish and close < lowestBreak

// === LAST TRADE TRACKING ===
var string lastTradeType = "None"
var string lastDirection = "None"
if rsiLong
    lastTradeType := "RSI"
    lastDirection  := "Long"
// if rsiShort
//    lastTradeType := "RSI"
//    lastDirection  := "Short"
if longBreak
    lastTradeType := "Breakout"
    lastDirection  := "Long"
// if shortBreak
//    lastTradeType := "Breakout"
//    lastDirection  := "Short"

// === ENTRIES ===
if rsiLong
    strategy.entry("RSI Long", strategy.long)
// if rsiShort
//    strategy.entry("RSI Short", strategy.short)
if longBreak
    strategy.entry("Breakout Long", strategy.long)
// if shortBreak
//    strategy.entry("Breakout Short", strategy.short)

// === EXITS ===
if rsiLongExit
    strategy.close("RSI Long")
// if rsiShortExit
//    strategy.close("RSI Short")
strategy.exit("BO Long Exit",  from_entry="Breakout Long",  trail_points=atr * atrMult, trail_offset=atr * atrMult)
// strategy.exit("BO Short Exit", from_entry="Breakout Short", trail_points=atr * atrMult, trail_offset=atr * atrMult)

// === PLOTS ===
plot(ema, "38 EMA", color=color.orange)

// === ONE-LINE DASHBOARD LABEL ===
var label dash = na
if bar_index % 5 == 0
    label.delete(dash)
    dash := label.new(bar_index, high,
      "Regime: " + regimeLabel + " | Bias: " + biasLabel + " | Last: " + lastTradeType + " " + lastDirection,
      xloc=xloc.bar_index, yloc=yloc.price,
      style=label.style_label_left, size=size.small,
      textcolor=color.white, color=color.black)
