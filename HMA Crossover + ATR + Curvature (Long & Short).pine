//@version=6
strategy("Timing：HMA Crossover + ATR + Curvature (Long & Short)", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=100)

// === Inputs ===
fastLength  = input.int(15, title="Fast HMA Period")
slowLength  = input.int(34, title="Slow HMA Period")
atrLength   = input.int(14, title="ATR Period")
riskPercent = input.float(1.0, minval=0.1, maxval=10, title="Risk per Trade (%)")
atrMult     = input.float(1.5, title="Stop Loss ATR Multiplier")
trailMult   = input.float(1.0, title="Trailing Stop ATR Multiplier")
curvThresh  = input.float(0.0, step=0.01, title="Curvature Threshold (Min Acceleration)")

// === Calculations ===
fastHMA = ta.hma(close, fastLength)
slowHMA = ta.hma(close, slowLength)
atr     = ta.atr(atrLength)

// Curvature: approximate second derivative (acceleration)
curv = ta.change(ta.change(fastHMA))

// Entry Conditions
bullish = ta.crossover(fastHMA, slowHMA) and curv > curvThresh
bearish = ta.crossunder(fastHMA, slowHMA) and curv < -curvThresh

// Risk Management
stopLoss = atr * atrMult
trailStop = atr * trailMult
capital = strategy.equity
riskCapital = capital * (riskPercent / 100)
qty = riskCapital / stopLoss

// === Strategy Logic ===
if (bullish)
    strategy.entry("Long", strategy.long, qty=qty)
    strategy.exit("Long Trail Stop", from_entry="Long", trail_points=trailStop, trail_offset=trailStop)

if (bearish)
    strategy.entry("Short", strategy.short, qty=qty)
    strategy.exit("Short Trail Stop", from_entry="Short", trail_points=trailStop, trail_offset=trailStop)

plotshape(bullish, title="Buy", location=location.belowbar, color=color.green, style=shape.labelup, text="BUY")
plotshape(bearish, title="Sell", location=location.abovebar, color=color.red, style=shape.labeldown, text="SELL")