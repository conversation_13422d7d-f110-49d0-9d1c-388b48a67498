// 使用data2作为日线数据，data1作为分钟线数据// 策略参数定义Inputs:    k1(0.5),       // 买入触发系数    k2(0.5),       // 卖出触发系数    Mday(1),       // 计算卖出范围的历史天数    Nday(1),       // 计算买入范围的历史天数    lots(1),       // 交易手数    daystart(900), // 交易日开始时间（9:00）    dayend(1500),  // 交易日结束时间（15:00）    freq_min(10);  // 分钟线频率（10分钟）
// 变量声明vars:    dayclose(0), dayhigh(0), daylow(0),  // 当日收盘、最高、最低价（未使用）    sellrange(0), buyrange(0),           // 卖出/买入价格范围    buytrig(0), selltrig(0),             // 买入/卖出触发阈值    HH(0), HC(0), LC(0), LL(0),          // 历史高低点及对应收盘价    buyposition(0), sellposition(0),     // 买入/卖出触发价位    opentoday(0);                        // 当日开盘价
// 获取当日开盘价（使用data1的分钟线数据）if time >= daystart and time <= daystart + freq_min then begin    opentoday = open;  // 在开盘后10分钟内记录开盘价end;
// 计算卖出价格范围（基于Mday天数）HH = highest(high, Mday) of data2;  // Mday天内最高价HC = highest(close, Mday) of data2; // Mday天内最高价日的收盘价LL = lowest(low, Mday) of data2;    // Mday天内最低价LC = lowest(close, Mday) of data2;  // Mday天内最低价日的收盘价
// 选择最大价格波动范围作为卖出范围if (HH-LC) >= (HC-LL) then     sellrange = HH - LC else     sellrange = HC - LL;
// 计算买入价格范围（基于Nday天数）HH = Highest(High, Nday) of data2;HC = Highest(Close, Nday) of data2;LL = Lowest(Low, Nday) of data2;LC = Lowest(Close, Nday) of data2;
// 选择最大价格波动范围作为买入范围If((HH - LC) >= (HC - LL)) then     buyrange = HH - LC Else     buyrange = HC - LL;
// 计算实际触发价位buytrig = k1 * buyrange;       // 买入触发距离 = 系数 × 买入范围selltrig = k2 * sellrange;     // 卖出触发距离 = 系数 × 卖出范围buyposition = opentoday + buytrig;   // 买入触发价 = 开盘价 + 买入触发距离sellposition = opentoday - selltrig; // 卖出触发价 = 开盘价 - 卖出触发距离
// 交易逻辑（空仓时）If(MarketPosition = 0) then begin    // 价格突破买入触发价时做多    If High >= buyposition then Buy lots shares next bar at buyposition stop;    // 价格跌破卖出触发价时做空    If Low <= sellposition then SellShort lots shares next bar at sellposition stop;end;
// 交易逻辑（持空仓时）if marketposition = -1 then begin    // 价格突破买入触发价时平空    if high >= buyposition then buy lots shares next bar at buyposition stop;end;
// 交易逻辑（持多仓时）if marketposition = 1 then begin    // 价格跌破卖出触发价时平多    if low <= sellposition then sellshort lots shares next bar at sellposition stop;end;
// 绘制趋势线（收盘前30分钟）if (time >= dayend-3*freq_min and time <= dayend) then begin    // 绘制买入触发价位线    tl_new(date, 0900, buyposition, date, time, buyposition);    // 绘制卖出触发价位线    tl_new(date, 0900, sellposition, date, time, sellposition);end;（2）、鼎元C++语言核心程式源码////////////////////////////////////////////////////Dual Thrust交易系统设计 //////////////////////////////////////////////////////   //声明变量:HH,HC,LC,LL;	vector<double>HH, HC, LL, LC, HH_LC, HC_LL;//四个用到的比较变量容器	vector<double>highprice, lowprice, closeprice;//日线bar的四个要素	pc.clear();	HH.clear();	HC.clear();	LL.clear();	LC.clear();//采集日级级别的价格数据	RsqBar("day", sInst);//调用"策略运行"面板设置品种的日线数据map<string, TKVALUE>::iterator it;//迭代for (it = mapK["day"][sInst].begin(); it != mapK["day"][sInst].end(); it++) //遍历所有K线	{		highprice.push_back(it->second.dHigh);//将当根bar的最高价high存入highprice容器变量		lowprice.push_back(it->second.dLow);//将当根bar的最低价low存入lowprice容器变量		closeprice.push_back(it->second.dClose);//将当根bar的收盘价close存入closeprice容器变量	}
	HH = SeriesHigh(highprice, length);//获取length周期内high最高价格即highest(high,length)	LL = SeriesLow(lowprice, length);//与上同理,只是求最低价，即lowest(low,length)	HC = SeriesHigh(closeprice, length);//highet(close,length)	LC = SeriesLow(closeprice, length);//lowest(close,length)
	HH_LC = SeriesMinus(HH, LC);//highest(high,leng) - lowest(close,length)	HC_LL = SeriesMinus(HC, LL);//highest(close,length)-lowest(low,length)
	//range设定	vector<double>range;for (size_t i = 0; i < HH_LC.size(); i++)	{		range.push_back(max2(HH_LC[i], HC_LL[i]));//将HH_LC,HC_LL中较大值存入range容器变量中	}
	//***********************************************************************进出场触发价位设计	openvalue = tickopen;//将前面tick数据中获取的开盘价赋值给openvalue,以供下面设计进出使用	//设计交易周期的上下沿	upband =  openvalue + K1 * range[range.size() - 1]; //做多上沿	botband = openvalue - K2 * range[range.size() - 1]; //做空下沿	//***********************************************************************进出场触发价位设计
	//输出进场价格至LOG	string s2 = "   做多进场价  " + to_string(upband) + "  做空进场价  " + to_string(botband) + " 最新日线开盘价 " + to_string(openvalue) + " Range " + to_string(range[range.size()-1]);	InsertLog(s2);//输出进场价格至LOG	////////////////////////////////////////////////////Dual Thrust交易系统设计 //////////////////////////////////////////////////////