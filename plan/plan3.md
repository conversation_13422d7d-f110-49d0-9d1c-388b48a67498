INPUT: Length1(13) {Bars in ADX,DMI Calculation},Length2(13) {ADX Lowest Action Level},Length3(8) {Incremental ADX Action Level Increase},Length4(5) {Most Bars for SwingBars},Length5(3) {2nd Most Bars for SwingBars},Length6(2) {3rd Most Bars for SwingBars},Length7(1) {Fewest Bars for SwingBars};

VAR:  HighPrice(-999999), LowPrice(999999), SwingHigh(0), SwingLow(0), Counter(0),PrevSwingHighBar(0), SwingBarCount(0), LowestSwingLow(0), LowestSwingLowBar(0), SwingLowFound(False), LowtoHighBars(0), LongPrice(0), LongBar(0), LongSetUp(False), PrevSwingLowBar(0), HighestSwingHigh(0), HighestSwingHighBar(0),SwingHighFound(False),LastLongBar(0),LastLongPrice(999999), SwingBars(13);

ARRAY: SwingHighBar[1500](0), Swing<PERSON>igh<PERSON>rice[1500](0), SwingLowBar[1500](0), SwingLowPrice[1500](0);
{ ADX Filter }If ADX(Length1) < (Length2+Length3) Then SwingBars=Length4;If ADX(Length1) >= (Length2 +Length3) and ADX(Length1)<(Length2 + 2* Length3) Then  SwingBars=Length5;If ADX(Length1) >=(Length2 + 2* Length3) and ADX(Length1)<(Length2 + 3* Length3) Then  SwingBars=Length6;If ADX(Length1) >=(Length2 + 3* Length3) Then  SwingBars=Length7;

{ Long Trade Setup}SwingLowFound = False;  SwingHighFound = False;IF High > HighPrice Then HighPrice = High Else HighPrice = HighPrice[1];IF Low < LowPrice Then LowPrice = Low Else LowPrice = LowPrice[1];
IF High < High[1] And High[1] > High[2] Then BeginSwingHigh = SwingHigh + 1;  SwingHighBar[SwingHigh] = CurrentBar - 1; SwingHighPrice[SwingHigh] = High[1];   End;
IF Low > Low[1] And Low[1] < Low[2] Then  BeginSwingLow = SwingLow + 1;                   { Pivot Low Counter }SwingLowBar[SwingLow] = CurrentBar - 1;    { Most Recent Pivot Low Bar }SwingLowPrice[SwingLow] = Low[1];          { Most Recent Pivot Low Price  }End;
IF SwingHighBar[SwingHigh] = CurrentBar - 1 And SwingHighPrice[SwingHigh] < HighPrice And  SwingHigh > 1 And SwingLow > 1 Then  BeginSwingBarCount = 0;For Counter = SwingHigh - 1 DownTo 1 BeginIF SwingHighPrice[SwingHigh] < SwingHighPrice[Counter] Then BeginPrevSwingHighBar = SwingHighBar[Counter]; SwingBarCount = SwingHighBar[SwingHigh] - PrevSwingHighBar; Counter = 1; End;End;
IF SwingBarCount >= SwingBars Then  BeginLowestSwingLow = SwingLowPrice[SwingLow];    LowestSwingLowBar = SwingLowBar[SwingLow];  IF LowestSwingLowBar >= PrevSwingHighBar Then SwingLowFound = True;For Counter = SwingLow - 1 DownTo 1 BeginIF SwingLowBar[Counter] > PrevSwingHighBar Then BeginIF LowestSwingLow > SwingLowPrice[Counter] Then BeginLowestSwingLow = SwingLowPrice[Counter];  LowestSwingLowBar = SwingLowBar[Counter]; SwingLowFound = True;  End;End ElseCounter = 1;  End;End; 
IF SwingLowFound Then BeginLowtoHighBars = SwingHighBar[SwingHigh] - LowestSwingLowBar; IF LowtoHighBars >= SwingBars And SwingHighBar[SwingHigh] > LongBar Then  Begin         LongPrice = SwingHighPrice[SwingHigh] + 1 * MinMove Point;LongBar = SwingHighBar[SwingHigh];  LongSetUp = True;  End;End; End; 

{  Long Trade Entry   }         IF High >= LongPrice And LongBar > LastLongBar  Then BeginLongSetUp = False; LastLongPrice = LongPrice;  LongPrice = 999999;  LastLongBar = LongBar;End;
IF LongSetUp Then Buy("RSG Buy") next bar at LongPrice Stop; 