// 输入参数定义Inputs:     Price(Close),        // 使用收盘价作为基础价格    XAvgLen(15),         // 指数移动平均周期长度    HiLoLen(50),         // 最高价/最低价计算周期    Retrace(0.382),      // 斐波那契回撤比例(38.2%)    SetUpLen(24);        // 建仓信号持续周期
// 变量声明Vars:     XAvg(0),            // 指数移动平均值    HiHi(0),            // 周期内最高价    HiHiBar(0),         // 最高价出现的Bar索引    LoLo(0),            // 周期内最低价    LoLoBar(0),         // 最低价出现的Bar索引    Retracement(0),     // 回撤幅度计算值    BuySetup(0),        // 买入信号计数器    SellSetup(0);       // 卖出信号计数器
{ 核心指标计算部分 }XAvg = XAverage(Price, XAvgLen);                // 计算XAvgLen周期的指数移动平均HiHi = Highest(High, HiLoLen);                  // 计算HiLoLen周期内的最高价LoLo = Lowest(Low, HiLoLen);                    // 计算HiLoLen周期内的最低价HiHiBar = HighestBar(High, HiLoLen);            // 获取最高价出现的Bar位置LoLoBar = LowestBar(Low, HiLoLen);              // 获取最低价出现的Bar位置Retracement = (HiHi - LoLo) * Retrace;          // 计算回撤幅度(最高价-最低价)*回撤比例
{ 买入信号触发条件 }// 当收盘价低于(最高价-回撤幅度)且最高价出现在最低价之前时IF Close <= HiHi - Retracement AND HiHiBar < LoLoBar Then     BuySetup = 0;  // 重置买入计数器
{ 卖出信号触发条件 }// 当收盘价高于(最低价+回撤幅度)且最高价出现在最低价之后时IF Close >= LoLo + Retracement AND HiHiBar > LoLoBar Then     SellSetup = 0; // 重置卖出计数器
{ 信号持续时间计数 }BuySetup = BuySetup + 1;    // 买入信号持续周期计数SellSetup = SellSetup + 1;  // 卖出信号持续周期计数
{ 买入执行逻辑 }// 当买入信号处于有效期内(<=SetUpLen)IF BuySetup <= SetUpLen Then Begin    // 满足以下任一条件触发买入：    // 1. 检测到价格在移动平均线附近形成低点(SwingLow)    // 2. 收盘价上穿移动平均线    IF SwingLow(1, XAvg, 1, 2) <> -1 OR Close > XAvg Then Begin        Buy Next Bar at Market;  // 下一Bar以市价买入        BuySetup = SetUpLen;     // 将计数器设为最大值避免重复信号    End;End;
{ 卖出执行逻辑 }// 当卖出信号处于有效期内(<=SetUpLen)IF SellSetup <= SetUpLen Then Begin    // 满足以下任一条件触发卖出：    // 1. 检测到价格在移动平均线附近形成高点(SwingHigh)    // 2. 收盘价下穿移动平均线    IF SwingHigh(1, XAvg, 1, 2) <> -1 OR Close < XAvg Then Begin        Sell Next Bar at Market;  // 下一Bar以市价卖出        SellSetup = SetUpLen;     // 将计数器设为最大值避免重复信号    End;End;