布林线是常常用来确定商品价格是否超出其"正常" 的交易范围外的一个非常流行的技术分析工具。布林线是绘製在移动平均线的上方+2标準差和下方-2 标準差二条移动线。正常的状况下， 99%的价格被含括在这2个 标準差之内。因此，我们可以安全地假设在布林带外交易的价格是具有重大意义的。在有趋势的市场，通常在布林带外的交易价格意味著市场将有大量的动能和伴随著强烈的趋势。在盘整波动的市场，布林线可以指示的市场超卖或超买位置及作方向改变的准备。由于这双重的不同解释，我们将使用第二个条件来确认价格与布林线的相对关系。为了要这样做，我们将使用 CCI 指标。CCI 指标数值利用超过100或跌破-100 来指示市场目前是在超买或超卖状态， 它是一个振荡指标。CCI 设计的特性让约 75%的交易活动落在中立范围区。这个交易系统主要是在超卖的区域内找寻CCI反转向上的机会, 以及在超买的区域内找寻CCI向下反转的机会。结合CCI、 布林线这两种分析技术，将给我们作进场的条件准备。当价格活动最近已向下穿越布林带下方且 CCI 在超卖区域并反转向上，是为买进讯号。如果价格最近已超过布林带上方且 CCI 是在 超买区并反转向下的情况，是为卖出讯号。// 系统参数与变量定义inputs:    BBLen(27),       // 布林带周期长度    BBStdv(0.56),    // 布林带标准差倍数    CCILen(33),      // CCI指标周期长度    CCIAvgLn(8),     // CCI均线平滑周期    UpBand(165),     // CCI超买阈值    DownBand(185);   // CCI超卖阈值
vars:    BarPosition(0),  // 价格在布林带中的相对位置(0-1)    TopBand(0),      // 布林带上轨    BotBand(0),      // 布林带下轨    BBStatus(0),     // 布林带状态：1=买方环境，-1=卖方环境    CCIAvg(0),       // CCI的均线值    CCITurnsUp(false), // CCI向上反转信号    CCITurnsDown(false), // CCI向下反转信号    StopPrice(0),    // 动态止损价格    MP(0),           // 当前持仓状态：1=多仓，-1=空仓    LongCount(0),    // 多头信号触发时的K线计数    ShortCount(0),   // 空头信号触发时的K线计数    LEP(0),          // 多头入场价格    SEP(0);          // 空头入场价格
// 进场准备模块{ 计算布林通道与CCI指标的均线 }TopBand = BollingerBand(Close,BBLen,BBStdv);  // 计算布林带上轨BotBand = BollingerBand(Close,BBLen,-BBStdv); // 计算布林带下轨CCIAvg = Xaverage(CCI(CCILen),CCIAvgLn);      // 计算CCI的指数移动平均
{ 评估价格在布林带中的相对位置 }if TopBand <> BotBand then     BarPosition = (Close-BotBand)/(TopBand-BotBand); // 标准化位置计算(0-1)
if CurrentBar > 1 then Begin    { 在中线附近时维持观望 }    if BarPosition cross over 0.5 then BBStatus = 0;  // 价格上穿中线    if BarPosition cross under 0.5 then BBStatus = 0; // 价格下穿中线
    { 价格突破布林带边界时改变状态 }    if Low Cross under BotBand then BBStatus = -1;  // 跌破下轨进入卖方环境    if High Cross over TopBand then BBStatus = 1;   // 突破上轨进入买方环境end;
// 多头进场逻辑{ 当CCI在超卖区域(-185以下)出现1-3周期低点反转时触发 }CCITurnsUp = SwingLowBar(1,CCIAvg,1,3) = 1 and CCIAvg‌:ml-citation{ref="1" data="citationList"} < -DownBand;
{ 确定多头入场价格 }if BBStatus = -1 and CCITurnsUp then Begin    LongCount = CurrentBar;              // 记录信号触发时的K线位置    LEP = Highest(High,3);               // 取最近3根K线的最高价作为入场点end;
{ 在信号触发后5根K线内有效，以止损单方式挂单 }if Currentbar < LongCount+5 and MP <> 1 then Buy next bar at LEP stop;
// 空头进场逻辑{ 当CCI在超买区域(165以上)出现2-3周期高点反转时触发 }CCITurnsDown = SwingHighBar(1,CCIAvg,2,3) = 2 and CCIAvg‌:ml-citation{ref="2" data="citationList"} > UpBand;
{ 确定空头入场价格 }if BBStatus = 1 and CCITurnsDown then Begin    ShortCount = CurrentBar;             // 记录信号触发时的K线位置    SEP = Lowest(Low,3);                 // 取最近3根K线的最低价作为入场点end;
{ 在信号触发后5根K线内有效，以止损单方式挂单 }if Currentbar < ShortCount+5 and MP <> -1 then Sell next bar at SEP stop;
// 出场规则{ 多头动态止损逻辑 }if MP = 1 and MP‌:ml-citation{ref="1" data="citationList"} <> 1 then     StopPrice = Low-Average(Range,4)*2/3; // 初始止损：当前最低价-2/3近期平均波幅
if MP = 1 then Begin    ExitLong ("ExitLng") next bar at StopPrice stop; // 挂止损单    { 移动止损：每根K线向上调整30%的止损位 }    StopPrice = StopPrice + (0.3*(Low-StopPrice)); end;
{ 空头动态止损逻辑 }if MP = -1 and MP‌:ml-citation{ref="1" data="citationList"} <> -1 then     StopPrice = High + Average(Range,4)*2/3; // 初始止损：当前最高价+2/3近期平均波幅
if MP = -1 then Begin    ExitShort ("ExitShort") next bar at StopPrice stop; // 挂止损单    { 移动止损：每根K线向下调整30%的止损位 }    StopPrice = StopPrice - (0.3*(StopPrice-High));end;