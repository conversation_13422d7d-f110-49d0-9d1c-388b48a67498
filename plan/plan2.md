input:notbef(0900),notaft(1450);
var:f1(.35),f2(0.07),f3(.25),reverse(2.00),rangemin(1.15),xdiv(3);
var:ssetup(0),bsetup(0),senter(0),benter(0),bbreak(0),sbreak(0), ltoday(0),hitoday(9999),startnow(0),div(0), rfilter(false);
if currentbar=1 then startnow=0;
div=maxlist(xdiv,1);
if Date>Date[1] then begin
  startnow=startnow+1;  ssetup=hitoday[1]+f1*(Close[1]-ltoday[1]);   senter=((1+f2)/2)*(hitoday[1]+Close[1])-(f2)*ltoday[1];  benter=((1+f2)/2)*(ltoday[1]+Close[1])-(f2)*hitoday[1];  bsetup=ltoday[1]-f1*(hitoday[1]-Close[1]);  bbreak=ssetup+f3*(ssetup-bsetup);  sbreak=bsetup-f3*(ssetup-bsetup);  hitoday=high;  ltoday=low;  rfilter=hitoday[1]-ltoday[1]>=rangemin;end;
if h>hitoday then hitoday=h;if l<ltoday then ltoday=l;
if t>=notbef and t<notaft and startnow>=2 and rfilter and date>entrydate(1) then begin  if hitoday>=ssetup and marketposition>-1 then SELL("Rlev SE")next bar at senter+(hitoday-ssetup)/div stop;  if ltoday<=bsetup and marketposition<1 then BUY("Rlev LE")next bar at benter-(bsetup-ltoday)/div stop;
  if marketposition=-1 then BUY("RbUP LE")next bar at entryprice+reverse stop;  if marketposition=1 then sellshort("RbDN SE")next bar at entryprice-reverse stop;
  if marketposition=0 then  BUY("Break LE")next bar at bbreak stop;  if marketposition=0 then sellshort("Break SE")next bar at sbreak stop;end;
if t>=notaft and t<>sess1endtime then begin  if marketposition=-1 then buytocover("RbUP SX")next bar at entryprice+reverse stop;  if marketposition=1 then sell("RbDN LX")next bar at entryprice-reverse stop;  buytocover("Late SX")next bar at high+.05 stop;  sell("Late LX")next bar at low -.05 stop;END;
IF T=SESS1FIRSTBARTIME OR T[1]=SESS1FIRSTBARTIME THENIF (INTPORTION(CURRENTBAR/2))*2=CURRENTBAR THEN buytocover("EXPIRED")this bar at Close ELSE SELL("EXPIRED ")this bar at Close;