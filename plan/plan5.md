Inputs: ERDay(1), E<PERSON><PERSON>(0), Max$Stop(9999999), NumberOfContracts(1);
Variables: Variable1(0), Variable2(0), Variable3(0), Variable4(0), Variable5(0);Variables: Variable6(0), Variable7(0), Variable8(0), Variable9(0), Variable10(1);Variables: Variable11(0), Variable12(0), Variable13(0), Variable14(0), Variable15(0);Variables: Variable16(0), Variable17(0), Variable18(0), Variable19(0), Variable20(0);Variables: Variable21(0), Variable22(0), Variable23(0), Variable24(0), Variable25(0);Variables: Variable26(0), Variable27(2), Variable28(3), Variable29(0);
   IF ERDay = 1 AND EMSwing = 0 THEN    BEGIN      Variable1 = 1.800000 ;      Variable2 = 0.990000 ;      Variable3 = 0.160000 ;      Variable4 = 3.160000 ;      Variable5 = 0.420000 ;      Variable6 = 1 ;   END ;
   IF EMSwing = 1 AND ERDay = 0 THEN    BEGIN      Variable1 = 2.200000 ;      Variable2 = 1.200000 ;      Variable3 = 0.640000 ;      Variable4 = 3.150000 ;      Variable5 = 0.860000 ;      Variable6 = 0 ;   END ;
   Variable20 = AVGTRUERANGE (34) ;   Variable12 = AVGTRUERANGE (13) ;   Variable19 = BIGPOINTVALUE ;   Variable9 = ENTRIESTODAY (DATE) ;   Variable11 = ADAPTIVEMOVAVG (CLOSE, 10, 8, 120) ;   Variable10 = 1 ;   Variable21 = Variable11+Variable12*Variable1*Variable10 ;   Variable22 = Variable11-Variable12*Variable1*Variable10 ;   Variable7 = (Variable21-Variable22) ;   Variable15 = Variable2*Variable7 ;   Variable14 = Variable3*Variable7 ;   Variable16 = Variable4*Variable7 ;
   IF ERDay = 1 THEN Variable26 = MINUTESTOTIME (TimeToMinutes (SESS1ENDTIME))-15 ELSE Variable26 = MINUTESTOTIME (TimeToMinutes (SESS1ENDTIME)) ;   IF CLOSE CROSS OVER Variable11 THEN Variable17 = 1 ;   IF (Variable17[1] = 1 AND HIGH[0] >= Variable21[1]) OR CLOSE CROSS UNDER Variable11 OR MARKETPOSITION > 0 THEN Variable17 = 0 ;   IF Variable17 = 1 AND (TIME < Variable26 OR TIME >= SESS1ENDTIME) THEN    BEGIN      BUY ("Delphi_Buy") NEXT BAR NumberOfContracts CONTRACT Variable21 STOP ;   END ;
   IF MARKETPOSITION > 0 AND MAXCONTRACTPROFIT > Variable15*Variable19 THEN SELL ("LPL1Ex") NEXT BAR ENTRY ("Delphi_Buy") ENTRYPRICE+(Variable14) STOP ;   IF MARKETPOSITION > 0 THEN SELL ("LPO") NEXT BAR ENTRY ("Delphi_Buy") ENTRYPRICE+(Variable16) LIMIT ;
   IF CLOSE CROSS UNDER Variable11 THEN Variable18 = 1 ;
   IF (Variable18[1] = 1 AND LOW[0] <= Variable22[1]) OR CLOSE CROSS OVER Variable11 OR MARKETPOSITION < 0 THEN Variable18 = 0 ;   IF Variable18 = 1 AND (TIME < Variable26 OR TIME >= SESS1ENDTIME) THEN    BEGIN      SELLSHORT ("Delphi_Sell") NEXT BAR NumberOfContracts CONTRACT Variable22 STOP ;   END ;
   IF MARKETPOSITION < 0 AND MAXCONTRACTPROFIT > Variable15*Variable19 THEN BUYTOCOVER ("SPL1Ex") NEXT BAR ENTRY ("Delphi_Sell") ENTRYPRICE-(Variable14) STOP ;   IF MARKETPOSITION < 0 THEN BUYTOCOVER ("SPO") NEXT BAR ENTRY ("Delphi_Sell") ENTRYPRICE-(Variable16) LIMIT ;   IF ERDay = 1 THEN SETEXITONCLOSE ;
   IF ERDay = 1 AND TIME >= MINUTESTOTIME (TimeToMinutes (SESS1ENDTIME))-15 THEN    BEGIN      SELL ("LX EoD") CURRENTCONTRACTS CONTRACT NEXT BAR MARKET ;      BUYTOCOVER ("SX EoD") CURRENTCONTRACTS CONTRACT NEXT BAR MARKET ;   END ;
   SETSTOPCONTRACT ;   SETSTOPLOSS (Variable13) ;   Variable13 = MINLIST (Max$Stop, Variable5*Variable7*Variable19) ;