二、进出逻辑多头和空头进场条件a)多头头寸的进场是快速TMA上穿慢速TMA。当快速TMA大于慢速TMA时，多头设置进场有效。b)空头头寸的进场是快速TMA下穿慢速TMA。当快速TMA小于慢速TMA时，空头进场仍然有效。c)金字塔进场的多头进场是当前bar图的高点低于快速TMA，并且ADX大于N周期bar之前值。d)金字塔进场空头进场是当前bar图的低点在快速TMA之上，并且ADX大于N周期bar之前值。多头和空头进场a)多头进场是在进场bar的高点之上的一个点；金字塔多头进场是在快速TMA之上的一个点。金字塔式多单进场的最大数量的默认值是3。b)短线进场是在设置栏的低点之下的一个点；短线金字塔进场是在快速TMA之下的一个点。金字塔式空头进场的最大数量的默认值是3。多头和空头退出a)多头头寸的初始和追踪止损都在慢速TMA下方一点。b)空头的初始和追踪止损点在慢速TMA上方一点。三、程式源码（仅适用到multicharts平台！）
// 策略参数定义Inputs:     FastLength(5),    // 快速均线周期(三重均线)    SlowLength(30),   // 慢速均线周期(三重均线)     ADXLength(12);    // ADX指标计算周期
// 变量声明Variables:    MP(0),           // 当前持仓状态(0=空仓,1=多头,-1=空头)    Fast(0),         // 快速三重均线值    Slow(0),         // 慢速三重均线值    GoLong(False),   // 多头信号标志    GoShort(False),  // 空头信号标志    BuyStop(0),      // 多头入场止损价    SellStop(0),     // 空头入场止损价    ADXValue(0),     // ADX指标当前值    Trending(False); // 趋势强度标志
// 获取当前持仓状态MP = MarketPosition;
// 计算三重均线指标Fast = TriAverage(Close, FastLength);  // 计算快速三重均线Slow = TriAverage(Close, SlowLength);  // 计算慢速三重均线
// 计算趋势强度ADXValue = ADX(ADXLength);  // 计算ADX指标值Trending = ADXValue > ADXValue[FastLength];  // 判断当前趋势强度是否增强
// 生成交易信号GoLong = Fast > Slow;    // 快速线上穿慢速线产生多头信号GoShort = Fast < Slow;   // 快速线下穿慢速线产生空头信号
// 设置入场止损价位If Fast crosses above Slow Then BuyStop = High + 1 point;   // 多头突破时设置买入止损位If Fast crosses below Slow Then SellStop = Low - 1 point;   // 空头突破时设置卖出止损位
// 初始入场逻辑If GoLong AND MP = 0 Then Buy ("Long") next bar at BuyStop Stop;    // 空仓时满足多头条件则做多If GoShort AND MP = 0 Then Sell ("Short") next bar at SellStop Stop; // 空仓时满足空头条件则做空
// 多头持仓管理If MP = 1 Then Begin    sell next bar at Slow - 1 point Stop;  // 设置多头止损位(慢速线下方1点)    If High < Fast AND Trending Then Buy ("Longer") next bar at Fast + 1 point Stop; // 趋势中回调至快速线时加仓End;
// 空头持仓管理If MP = -1 Then Begin    buytocover next bar at Slow + 1 point Stop;  // 设置空头止损位(慢速线上方1点)    If Low > Fast AND Trending Then sellshort("Shorter") next bar at Fast - 1 point Stop; // 趋势中反弹至快速线时加仓End;