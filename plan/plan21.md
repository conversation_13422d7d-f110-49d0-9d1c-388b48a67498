二、交易规则设置a) 使用 MovAvg 2线指标，计算以下四对简单移动平均线：多头入场 = 7/23多头出场 = 4/8空头入场 = 6/18空头出场 = 2/9做多a) 买入的条件是 SMA:7 穿过 SMA:23。b) 多头入场位于设置bar的最高点加一点。做空a) 卖空设置是 SMA:6 下穿 SMA:18。b) 空头入场位于设置bar的最低点减去 1 点。退出规则a) 当 SMA:4 下穿 SMA:8 时，在下一bar开盘时退出多头头寸。b) 当 SMA:2 上穿 SMA:9 时，在下一bar开盘时退出空头头寸。三、完整源码（仅适用于multicharts平台，其它平台自行移植）// 输入参数定义Inputs:     LEFast(5),    // 多头入场快速均线周期    LESlow(20),   // 多头入场慢速均线周期    LXFast(3),    // 多头出场快速均线周期      LXSlow(10),   // 多头出场慢速均线周期    SEFast(5),    // 空头入场快速均线周期    SESlow(20),   // 空头入场慢速均线周期    SXFast(3),    // 空头出场快速均线周期    SXSlow(10);   // 空头出场慢速均线周期
// 多头交易规则{Long Entry/ Exit}// 入场条件：当快速均线上穿慢速均线时，在下根K线最高价+1点处挂突破买单IF Average(Close, LEFast) > Average(Close, LESlow) Then Buy Next Bar at High + 1 Point Stop;// 出场条件：当快速均线下穿慢速均线时，在下根K线市价平仓IF Average(Close, LXFast) Crosses Below Average(Close, LXSlow) Then sell Next Bar at Market;
// 空头交易规则  {Short Entry/Exit}// 入场条件：当快速均线下穿慢速均线时，在下根K线最低价-1点处挂突破卖单IF Average(Close, SEFast) < Average(Close, SESlow) Then sellshort Next Bar at Low - 1 Point Stop;// 出场条件：当快速均线上穿慢速均线时，在下根K线市价平仓IF Average(Close, SXFast) Crosses Above Average(Close, SXSlow) Then buytocover Next Bar at Market;