一、简介动量指标将今天的收盘价与过去指定天数的收盘价进行比较。例如，要计算 9 天动量线，从今天的收盘价中减去 9 天前的收盘价。动量振荡器的公式是：M = C - Cn，其中 C 是最新收盘价，Cn 是 n 天前的收盘价。假设 n = 9，当 9 天动量振荡器高于其零线并上升时，9 天价格变化为正且增加 - 即趋势看涨且加速。如果动量线变平，则意味着在横向移动期间，9 天的价格变化大致相等。当动量振荡器从零上方开始下降时，过去 9 天的市场涨幅小于前几天的相应涨幅——也就是说，上升趋势正在减速。当 9 天动量指标跌破零线时，当前收盘价低于 9 天前的收盘价。随着下降趋势获得看跌速度（即更大的 9 天下跌），动量线从零线加速向下。震荡指标在负值区域上扬意味着9日下跌幅度正在减小——也就是说，下行趋势正在减速。动量振荡器是一个领先指标——当价格仍在上升趋势中上升或下降趋势中下降时，它趋于平稳，当趋势开始放缓时，它反转方向。由于趋势在反转方向之前通常会显示动量下降，因此动量振荡器可以提供趋势变化可能迫在眉睫的预警。 该系统的第二个组成部分是交易量——在特定时间段内交易的股票或合约数量。在上升趋势中，交易量应在上涨日增加，在下跌日减少。在下降趋势中，交易量应在下跌日增加，在上涨日减少。在我们的成交量加权动量系统中，我们想知道“协同”成交量和动量是否会创造一个大于其部分总和的整体。换句话说，成交量应该提高动量指标的性能，而动量应该提高成交量作为分析价格行为的工具的可靠性。成交量加权动量指标 (VWMI) 构造如下：XAverage(Volume*Momentum(close,4),22)该公式产生一个四周期动量指标的 22周期指数移动平均线，该指标根据成交量加权。买入的条件是 VWMI 超过零。买入入场是在条件bar的收盘价加上五周期平均真实范围的 50%。如果在四个bar内没有触发入场，则取消进场条件。卖空的条件是 VWMI 低于零。入场点在进场bar线的收盘价减去五周期bar线平均真实范围的 50%。如果在四个bar内没有触发入场，则取消进场。对于多头头寸，初始止损是入场bar线的低点减去 5 周期bar线平均真实范围的 50%。对于空头头寸，初始止损是进场bar的高点加上 5 周期平均真实范围的 50%。在 VWMI 下穿零之后，多头的退出是在下一次开盘时。空头时的退出是在 VWMI 超过零之后的下一次开盘。二、规则逻辑设置a)构建VWMI。b)计算ATR:5。多头进场a)设置是VWMI高于零。b)进场是在设置bar的收盘价加上ATR:5的50%。c)买入设置将在设置bar后的四个bar中保持有效。空头进场a)设置是VWMI在零点以下交叉。b)进场是在设置bar的收盘价减去ATR:5的50%。c)卖出设置将在设置bar之后的四个bar内保持有效。退出交易a)做多时，我们的初始止损将是设置bar的低点减去ATR:5的50%。b)做空时，我们的初始止损将是设置bar的高点加上ATR:5的50%。c)当VWMI越过零点时，在下一次开盘时退出多头头寸。d)当VWMI越过零点时，在下次开盘时退出空头头寸。三、程式码（仅适用于multicharts平台，其它平台自行移植）// 策略输入参数定义Inputs:     Price(Close),        // 使用收盘价作为基础价格    MomLen(5),           // 动量计算周期长度    AvgLen(20),          // 成交量加权平均计算周期    ATRLen(5),           // ATR计算周期长度    ATRPcnt(0.25),       // ATR百分比用于确定通道宽度    SetupLen(5);         // 建仓信号有效周期数
// 变量声明Vars:     VWM(0),              // 成交量加权动量指标值    ATR(0),              // 平均真实波幅值    LEPrice(0),          // 多头入场基准价格    SEPrice(0),          // 空头入场基准价格    BullSetup(False),    // 多头信号标志    BearSetup(False),    // 空头信号标志    LSetup(0),           // 多头信号计数    SSetup(0);           // 空头信号计数
{指标计算部分}VWM = XAverage(Volume * Momentum(Price, MomLen), AvgLen);  // 计算成交量加权动量指标:ml-citation{ref="1" data="citationList"}ATR = AvgTrueRange(ATRLen);                                // 计算平均真实波幅:ml-citation{ref="2" data="citationList"}
{信号触发条件}BullSetup = VWM Crosses Above 0;  // 当VWM上穿零轴时触发多头信号:ml-citation{ref="1" data="citationList"}BearSetup = VWM Crosses Below 0;  // 当VWM下穿零轴时触发空头信号:ml-citation{ref="1" data="citationList"}
{多头建仓条件初始化}IF BullSetup Then Begin    LSetup = 0;                   // 重置多头计数    LEPrice = Close;              // 记录触发时的收盘价作为基准:ml-citation{ref="1" data="citationList"}End;
{空头建仓条件初始化}IF BearSetup Then Begin    SSetup = 0;                   // 重置空头计数    SEPrice = Close;              // 记录触发时的收盘价作为基准:ml-citation{ref="1" data="citationList"}End;
{信号有效期计数}LSetup = LSetup + 1;  // 每根K线递增多头计数SSetup = SSetup + 1;  // 每根K线递增空头计数
{多头入场规则}IF LSetup <= SetupLen Then Begin    // 在基准价上方ATR通道处设置买入止损单:ml-citation{ref="2" data="citationList"}    Buy Next Bar at LEPrice + (ATRPcnt * ATR) Stop;      // 设置保护性止损单    sell Next Bar at Low - (ATRPcnt * ATRLen) Stop;  End;
{空头入场规则}IF SSetup <= SetupLen Then Begin    // 在基准价下方ATR通道处设置卖出止损单:ml-citation{ref="2" data="citationList"}    sellshort Next Bar at SEPrice - (ATRPcnt * ATR) Stop;      // 设置保护性止损单    buytocover Next Bar at High + (ATRPcnt * ATRLen) Stop;  End;
{多头出场规则}IF MarketPosition = 1 Then Begin    LSetup = SetupLen;            // 标记信号周期已结束    IF BearSetup Then sell Next Bar at Market;  // 出现空头信号时平仓:ml-citation{ref="1" data="citationList"}End;
{空头出场规则}IF MarketPosition = -1 Then Begin     SSetup = SetupLen;            // 标记信号周期已结束    IF BullSetup Then buytocover Next Bar at Market;  // 出现多头信号时平仓:ml-citation{ref="1" data="citationList"}End;