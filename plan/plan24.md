二、介绍原理1、本策略涉及两个指标，1是macd指标（参数：16，34，12），2是单根EMA均线（参数：26），2、进出逻辑：（1）、如果收盘价金叉均线，同时macd中的diff>dea 则进多。（2）、如果收盘价死叉均线，同时macd中的 diff < dea 则进空。（3）、如果diff金叉dea，同时收盘价 > 均线 则做多。（4）、如果diff死叉dea，同时收盘价 < 均线 则做空。3、设有一个保护止盈单，若是进场后最大盈利超过100点，则开启保护止盈，当盈利小于10点时止盈离场。三、核心程式码（仅适用于鼎元C++量化系统，其它平台自请移植！）1、设计出场均线和过滤均线以及MACD中的diff与dea//*********************************************************MACD均线多空交易系统设计**********************************************************************************************	price = Close();//逆序收盘价格
//设计均线	filterma = Xaverage(price, filtlength);//26周期过滤均线	exitma = Xaverage(price, exitlength);//26周期出场均线
//设计MACD	fastma = Xaverage(price, fastlength);//16周期快速均线	slowma = Xaverage(price, slowlength);//34周期慢速均线	diff = fastma - slowma;//diff = fastma - slowma	dea = Xaverage(diff, spreadlength);//dea = ema(diff,N)
//输出进场价格至LOG面板string s3 = "  最新收盘价：" + to_string(price[0]) + "  过滤均线  " + to_string(filterma[0]) + "    diff    " + to_string(diff[0]) + "    dea   " + to_string(dea[0]) + "    出场均线   " + to_string(exitma[0]);	InsertLog(s3);//输出进场价格至LOG面板//**********************************************************MACD均线多空交易系统设计**********************************************************************************************//2、输出收盘价与均线的金死叉及macd的diff与dea金死叉       //macd金叉输出if (diff[0] >= dea[0] && diff[1] < dea[1])	{		InsertLog("  DIFF金叉DEA  ,最新diff: " + to_string(diff[0]) + " 最新dea : " + to_string(dea[0]) + "上一个diff: " + to_string(diff[1]) + " 上一个dea : " + to_string(dea[1]));	}
	//macd死叉输出if (diff[0] <= dea[0] && diff[1] > dea[1])	{		InsertLog("  DIFF死叉DEA  ,最新diff: " + to_string(diff[0]) + " 最新dea : " + to_string(dea[0]) + "上一个diff: " + to_string(diff[1]) + " 上一个dea : " + to_string(dea[1]));	}
	//均线金叉输出if (price[0] >= filterma[0] && price[1] < filterma[1])	{		InsertLog("  收盘价金叉均线  ,最新收盘价: " + to_string(price[0]) + " 最新均线值 : " + to_string(filterma[0]) + "上一个收盘价: " + to_string(price[1]) + " 上一个均线值 : " + to_string(filterma[1]));	}
	//均线死叉输出if (price[0] <= filterma[0] && price[1] > filterma[1])	{		InsertLog("  收盘价死叉均线  ,最新收盘价: " + to_string(price[0]) + " 最新均线值 : " + to_string(filterma[0]) + "上一个收盘价: " + to_string(price[1]) + " 上一个均线值 : " + to_string(filterma[1]));	}3、开启止盈	/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////	//开启止盈开关	if (fx == 1 && sfzy == 0 && t->LastPrice > entryprice + target1) //1持仓为多,2，没开启止盈, 3最新价大于成本价+止盈条件点数	{		sfzy = 1; //开启多单止盈变量		InsertLog("  多单达到触发止盈条件    " + sInst);	}	if (fx == -1 && sfzy == 0 && t->LastPrice < entryprice - target1)  //1,持仓为空单 2,止盈没开启 3,最新价小于空单成本价 - 止盈条件点数	{		sfzy = -1; //开启空单止盈变量		InsertLog("  空单达到触发止盈条件    " + sInst);	}
	//多单开启止盈出场模块	if (fx == 1 && sfzy == 1 && t->LastPrice < entryprice + target2) //持仓为多, 开启多单止盈, 最新价格变为小于成本价+止盈条件（平多单语句）	{		Sell(lots, "多单达到止盈条件后触发保护性止盈条件卖出平仓");	}	//空单开启止盈模块	if (fx == -1 && sfzy == -1 && t->LastPrice > entryprice - target2) //市场持仓为做空, 开启止盈变量, 收盘价大于成本价+止盈条件（平空单语句）	{		BuytoCover(lots, "空单达到止盈条件后触发保护性止盈条件买入平仓");	}	/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////4、平仓模块//【平仓模块】if (fx == 1)	{//均线死叉, 平多if ((price[0] <= exitma[0] && price[1] > exitma[1]) || price[0] < exitma[0] && price[1] < exitma[1])Sell(lots, "收盘价下穿均线多单达到出场条件卖出平仓");	}if (fx == -1)	{//均线金叉平空if ((price[0] >= exitma[0] && price[1] < exitma[1]) || price[0] > exitma[0] && price[1] > exitma[1])BuytoCover(lots, "收盘价上穿均线空单达到出场条件买入平仓");	}5、开仓模块//（3）最新价小于均线价格，持有多单进行平仓模块(sell model)if (fx == 0)// 持仓为0	{//均线金叉, macd多头排列.if (price[0] >= filterma[0] && price[1] < filterma[1] && diff[0] >= dea[0])		{			sfzy = 0;Buy(lots, "均线金叉达到入场条件买入开仓");			entryprice = price[0];		}//MACD金叉, 均线多头排列if (diff[0] >= dea[0] && diff[1] < dea[1] && price[0] >= filterma[0])		{			sfzy = 0;Buy(lots, "macd金叉达到入场条件买入开仓");			entryprice = price[0];		}//均线死叉, macd同时空头排列if (price[0] <= filterma[0] && price[1] > filterma[1] && diff[0] <= dea[0])		{			sfzy = 0;SellShort(lots, "均线死叉达到入场条件卖出开仓");			entryprice = price[0];		}
//macd死叉,均线与收盘价空头排列if (diff[0] <= dea[0] && diff[1] > dea[1] && price[0] <= filterma[0])		{			sfzy = 0;SellShort(lots, "macd死叉达到入场条件卖出开仓");			entryprice = price[0];		}	}