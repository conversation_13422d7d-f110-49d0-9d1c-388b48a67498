// 策略名称：基于移动平均线导数的交易策略// 输入参数定义Inputs:     Price(Close),    // 使用收盘价作为计算基础    Length(9);       // 移动平均线周期设为9
// 变量声明Variables:     AvgVal(0),       // 存储移动平均值    DerivMA1(0),     // 存储一阶导数值    DerivMA2(0),     // 存储二阶导数值    BuyFlag(False),  // 买入信号标志    SellFlag(False); // 卖出信号标志
{ 导数计算部分 }// 计算指定长度的移动平均值AvgVal = Average(Price, Length);
{ 一阶导数计算 }// 计算移动平均线的变化率（一阶导数）// 公式：(当前MA值 - Length周期前的MA值)/周期长度DerivMA1 = (AvgVal - AvgVal[Length]) / Length;
{ 二阶导数计算 }// 计算一阶导数的变化率（二阶导数）// 公式：(当前一阶导数值 - 上一周期一阶导数值)/1DerivMA2 = (DerivMA1 - DerivMA1[1]) / 1;
{ 条件设置部分 }// 定义各种交叉条件Condition1 = DerivMA1 Crosses Below 0;  // 一阶导数下穿零轴Condition2 = DerivMA1 Crosses Above 0;  // 一阶导数上穿零轴Condition3 = DerivMA2 Crosses Below 0;  // 二阶导数下穿零轴Condition4 = DerivMA2 Crosses Above 0;  // 二阶导数上穿零轴
{ 信号设置部分 }// 当二阶导数上穿零轴时，设置买入标志If Condition4 Then BuyFlag = True;// 当二阶导数下穿零轴时，设置卖出标志If Condition3 Then SellFlag = True;
{ 多头入场逻辑 }// 当一阶导数上穿零轴且买入标志为真时，执行买入If Condition2 AND BuyFlag Then Begin    Buy Next Bar at Close + 1 Point Stop;  // 下根K线收盘价+1点买入    BuyFlag = False;  // 重置买入标志End;
{ 空头入场逻辑 }// 当一阶导数下穿零轴且卖出标志为真时，执行卖出If Condition1 AND SellFlag Then Begin    sellshort Next Bar at Close - 1 Point Stop;  // 下根K线收盘价-1点卖出    SellFlag = False;  // 重置卖出标志End;