三、交易规则设置设置a) 计算 12 周期慢速随机指标。b) 将超买水平设置为 70。c) 将超卖水平设置为 30。做多a) 在超卖区域（低于 30）检查 %K 是否超过 %D。b) 在设置bar的高点上方设置一个买入止损点。此买入止损将在六个bar内保持有效。做空a) 检查 %K 是否在超买区域（高于 70）越过 %D。b) 将卖出止损设置在设置bar的低点下方。这个卖出止损将在六个bar上保持有效。退出指令a) 一旦我们进入多头头寸，我们将在设置bar的低位设置保护性止损；当我们进入空头头寸时，我们将在设置bar的高点设置保护性止损。四、完整源码（仅适用到multiharts平台，其它软件请自行移植）
Inputs:     Length(12),       // KDJ计算周期    OBought(70),      // 超买阈值    OSold(30),        // 超卖阈值    SetUpLen(6);      // 入场信号有效期
Vars:    KVal(0),         // K值    DVal(0),         // D值    BuySetup(0),     // 买入触发价位    SellSetup(0),    // 卖出触发价位    BuyPeriod(99),   // 买入信号计数器    SellPeriod(99),  // 卖出信号计数器    BuyStop(0),      // 买入止损位    SellStop(0);     // 卖出止损位
// 计算KDJ指标kVal = SlowKCustom(High,Low,Close,Length);DVal = SlowDCustom(High, Low, Close,Length);
{========== 入场信号设置 ==========}// 当K值超卖且金叉时设置买入信号IF KVal < OSold AND KVal Crosses Above DVal Then Begin     BuySetup = Highest(High, 2);  // 买入触发价=最近2根K线最高价    BuyStop = Low;                // 止损位=当前K线最低价    BuyPeriod = 0;                // 重置买入信号计数器End;
// 当K值超买且死叉时设置卖出信号IF KVal > OBought AND KVal Crosses Below DVal Then Begin     SellSetup = Lowest(Low, 2);   // 卖出触发价=最近2根K线最低价    SellStop = High;              // 止损位=当前K线最高价    SellPeriod = 0;               // 重置卖出信号计数器End;
{========== 信号有效期管理 ==========}// 如果已有持仓则跳过计数，否则递增计数器IF MarketPosition = 1 Then BuyPeriod = SetUpLen + 1 Else BuyPeriod = BuyPeriod + 1;IF MarketPosition = -1 Then SellPeriod = SetUpLen + 1 Else SellPeriod = SellPeriod + 1;
{========== 入场指令 ==========}// 在有效期内挂突破单IF BuyPeriod <= SetUpLen Then Buy Next Bar at BuySetup + 1 Point Stop;IF SellPeriod <= SetUpLen Then sellshort Next Bar at SellSetup - 1 Point Stop;
{========== 系统退出条件 ==========}// 多头平仓条件：K值死叉但未达超卖IF KVal Crosses Below DVal AND KVal > OSold Then Begin    BuyPeriod = SetUpLen;        // 强制结束信号有效期    sell Next Bar at Market;     // 市价平仓End;
// 空头平仓条件：K值金叉但未达超买IF Kval Crosses Above DVal AND KVal < OBought Then Begin    SellPeriod = SetUpLen;       // 强制结束信号有效期    buytocover Next Bar at Market; // 市价平仓End;
{========== 止损指令 ==========}sell Next Bar at BuyStop Stop;      // 多头止损buytocover Next Bar at SellStop Stop; // 空头止损