二、交易规则设置a) 计算一个 10 周期的高低价格通道。做多a) 检查收盘价是否高于 10 周期价格通道的顶线。b) 在突破 10 根bar线内检查新的 4 根bar线低点。c) 在下根bar线开盘时买入做多。做空a) 检查收盘价是否低于 10 周期价格通道的底线。b) 在突破 10 根bar线内检查新的 4 根bar线高点。c) 在下一根bar线开盘时卖出做空。退出规则a) 我们将启用资金管理止损、盈亏平衡止损和 % 风险追踪止损。b) 我们还将在反弹至新的 8 周期高点时退出多头头寸，并在下跌至新的 8 周期低点时退出空头头寸。三、完整源码（仅限multicharts平台，其它平台自行移植！）// 策略参数定义Input:     Channel(10),      // 通道计算周期，用于确定最高/最低价通道    RetrBars(10),     // 回撤确认周期，用于验证突破信号的有效性    NewLo_Hi(4),      // 新低/新高确认周期，用于入场信号过滤    TrailStp(8);      // 跟踪止损周期，用于动态退出位置计算
// 变量声明Vars:     HiChannel(0),     // 存储通道上轨（最高价通道）    LoChannel(0);     // 存储通道下轨（最低价通道）
// 通道计算模块{Assignment of Channel calculations}HiChannel = Highest(Close, Channel)[1];  // 计算前Channel周期收盘价最高值作为上轨LoChannel = Lowest(Close, Channel)[1];   // 计算前Channel周期收盘价最低值作为下轨
// 多头入场条件{Long Entry}// 条件1：收盘价突破上轨且在RetrBars周期内首次出现// 条件2：当前最低价低于NewLo_Hi周期内最低价（确认回调）If MRO(Close > HiChannel, RetrBars, 1)>-1 AND Low < Lowest(Low, NewLo_Hi)[1]     then Buy next bar at Market;  // 满足条件后下一根K线市价买入
// 空头入场条件{Short Entry}// 条件1：收盘价跌破下轨且在RetrBars周期内首次出现// 条件2：当前最高价高于NewLo_Hi周期内最高价（确认反弹）If MRO(Close < LoChannel, RetrBars, 1)>-1 AND High > Highest(High, NewLo_Hi)[1]     then Sell next bar at Market;  // 满足条件后下一根K线市价卖出
// 退出机制{Exits}// 动态跟踪止损：当最高价突破TrailStp周期内最高价时平多仓If High > Highest(High,TrailStp)[1] then sell next bar at market;// 动态跟踪止损：当最低价跌破TrailStp周期内最低价时平空仓If Low < Lowest(Low,TrailStp)[1] then buytocover next bar at market;