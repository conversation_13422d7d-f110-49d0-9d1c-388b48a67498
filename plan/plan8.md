// 输入参数：强度参数，用于确定高低点检测范围INPUT:STRENGTH(1);
// 变量声明VAR:R(0),Z(0),Y(0),W(0),E(1),  // 循环计数器和临时变量  POINT1(0),POINT2(0),POINT1BAR(0),POINT2BAR(0),  // 高点相关变量  SLOPE(0),LINE(0),  // 趋势线斜率和当前值  LPOINT1(0),LPOINT2(0),LPOINT1BAR(0),LPOINT2BAR(0),  // 低点相关变量  USLOPE(0),ULINE(0),  // 上升趋势线斜率和当前值  LONGX(99999),SHORTX(0),  // 多头和空头目标价位  LL(99999),HH(0),ADD(0);  // 最低价、最高价和附加调整值
// 数组：存储最近20个高点和低点及其对应K线位置ARRAY:LP[20](0),LPBAR[20](0),HP[20](0),HPBAR[20](0);
// 初始化R=STRENGTH;IF MARKETPOSITION<1 THEN LONGX=99999;  // 重置多头目标价IF MARKETPOSITION>-1 THEN SHORTX=0;  // 重置空头目标价
// 更新所有高低点的K线位置计数FOR Z=0 TO 19 BEGIN  LPBAR[Z]=LPBAR[Z]+1;  HPBAR[Z]=HPBAR[Z]+1;END;
// 检测新低点IF L[R]<LOWEST(TrueLow,R) THEN BEGIN  E=1;  // 检查是否连续多个K线有相同低点  WHILE L[R]=L[R+E] BEGIN    E=E+1;  END;  // 确认是有效新低点  IF L[R]<LOWEST(TrueLow,R)[R+E] THEN BEGIN    // 更新低点数组    FOR Z=19 DOWNTO 1 BEGIN      LP[Z]=LP[Z-1];      LPBAR[Z]=LPBAR[Z-1];    END;    LP[0]=L[R];  // 记录新低点价格    LPBAR[0]=R;  // 记录新低点K线位置  END;END;
// 寻找两个最近的低点用于画趋势线LPOINT2=LP[0];  // 最新低点LPOINT2BAR=LPBAR[0];  // 最新低点位置LPOINT1=0;Y=1;// 寻找前一个更低的低点WHILE LPOINT1=0 AND Y<20 BEGIN  IF LP[Y]<LP[0] THEN BEGIN    LPOINT1=LP[Y];    LPOINT1BAR=LPBAR[Y];  END;  Y=Y+1;END;
// 如果找到两个有效低点，计算下降趋势线IF LPOINT1>0 AND LPOINT1BAR>LPOINT2BAR THEN BEGIN  USLOPE=(LPOINT2-LPOINT1)/(LPOINT1BAR-LPOINT2BAR);  // 计算斜率  ULINE=LPOINT1;  // 初始化趋势线值  HH=0;  // 向后计算趋势线并寻找最高点  FOR W=LPOINT1BAR DOWNTO 0 BEGIN    IF W<=MAXBARSBACK THEN BEGIN      IF H[W]>HH THEN BEGIN        HH=H[W];  // 记录最高点        ADD=ULINE-HH;  // 计算调整值      END;    END;    ULINE=ULINE+USLOPE;  // 更新趋势线值  END;  // 空头入场条件  IF MARKETPOSITION>-1 AND (C>ULINE OR L>ULINE-SLOPE) THEN BEGIN    SHORTX=ULINE+ADD;  // 设置空头目标价    Sell Short("Sell") Next Bar ULINE STOP;  // 空头入场  END;END;// 空头平仓条件IF SHORTX>0 THEN Buy to Cover("ObjectiveSX") Next Bar SHORTX LIMIT;
// 检测新高点（逻辑与低点检测类似）IF H[R]>HIGHEST(TrueHigh,R) THEN BEGIN  E=1;  WHILE H[R]=H[R+E] BEGIN    E=E+1;  END;  IF H[R]>HIGHEST(TrueHigh,R)[R+E] THEN BEGIN    FOR Z=19 DOWNTO 1 BEGIN      HP[Z]=HP[Z-1];      HPBAR[Z]=HPBAR[Z-1];    END;    HP[0]=H[R];    HPBAR[0]=R;  END;END;
// 寻找两个最近的高点用于画趋势线POINT2=HP[0];  // 最新高点POINT2BAR=HPBAR[0];  // 最新高点位置POINT1=0;Y=1;// 寻找前一个更高的高点WHILE POINT1=0 AND Y<20 BEGIN  IF HP[Y]>HP[0] THEN BEGIN    POINT1=HP[Y];    POINT1BAR=HPBAR[Y];  END;  Y=Y+1;END;
// 如果找到两个有效高点，计算上升趋势线IF POINT1>0 AND POINT1BAR>POINT2BAR THEN BEGIN  SLOPE=(POINT2-POINT1)/(POINT1BAR-POINT2BAR);  // 计算斜率  LINE=POINT1;  // 初始化趋势线值  LL=99999;  // 向后计算趋势线并寻找最低点  FOR W=POINT1BAR DOWNTO 0 BEGIN    IF W<=MAXBARSBACK THEN BEGIN      IF L[W]<LL THEN BEGIN        LL=L[W];  // 记录最低点        ADD=LINE-LL;  // 计算调整值      END;    END;    LINE=LINE+SLOPE;  // 更新趋势线值  END;  // 多头入场条件  IF MARKETPOSITION<1 AND (C<LINE OR H<LINE-SLOPE) THEN BEGIN    LONGX=LINE+ADD;  // 设置多头目标价    Buy("Buy") Next Bar LINE STOP;  // 多头入场  END;END;// 多头平仓条件IF LONGX<99999 THEN Sell("ObjectiveLX") Next Bar LONGX LIMIT;