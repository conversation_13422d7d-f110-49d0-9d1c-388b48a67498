（1），第一代：// 策略参数定义Inputs:     Ceil(30),    // 通道周期上限值    Flr(10),     // 通道周期下限值    S(30);       // 标准差计算周期
// 变量声明Vars:     X(0),        // 存储标准差计算结果    ZDelta(0),   // 标准差变化率    N(0);        // 动态通道周期
// 计算收盘价的标准差X = Stddev(Close, S);
// 计算标准差的变化率（避免除零错误）if X<>0 then ZDelta = (X - X[1]) / X;
// 初始化动态通道周期NIf CurrentBar=1 then N = (Ceil+Flr)/2;  // 第一根K线取上下限平均值
// 动态调整通道周期（基于标准差变化率）If CurrentBar>1 then N = N[1] * (1 + ZDelta);
// 确保通道周期在设定的上下限范围内N = MaxList(N, Flr);  // 不低于下限FlrN = MinList(N, Ceil); // 不超过上限Ceil
{ 入场逻辑 }// 多头入场：当没有持仓时，在N周期最高价突破时买入if marketposition<>1 then Buy next bar at Highest(High, N) Stop;
// 空头入场：当没有空头持仓时，在N周期最低价突破时卖出if marketposition<>-1 then sellshort next bar at Lowest(Low, N) Stop;
DBS基本原型是一个高低价格通道系統(Price Channel Breakout)，这种典型通道系統就是取一个例如N=20天的最高、最低价来算出上下通道，向上突破上通道就做多，向下跌破下通道就做空，而DBS的”Dynamic”就体现在把上例的N由固定值20设计成变动的--可以依价格结构不同来对应变化。接下来就是变动的逻辑或者说基础是什么，这里采用的是波动率，以函式Stddev(C,30)计算30天期历史波动率，基本的逻辑假设就是，当波动变大时，行情呈现剧烈震荡，N应该大一点，例如N变成30，通道就会放大，等於是把进场的条件设严格，避免被假突破给骗进场；反之，当波动率趋小时，N就随之变小，所谓盘久必变，如此行情一发动就可进场。Zdelta负责算出与前历史波动率的差异(取%)，正值代表波动率大於前根，反之负值代表小於前根，所以当波动率变大，N= N [1]*(1+ZDelta)，N将同步增加，反之亦然，这样就达到了我们要的N与波动率同向变动的效果，此外，实务上还要限制N在一定范围内，否则N爆涨暴跌成为极端值将失真。所以初始值设定N=(Ceil+Flr)/2，也就是上下限的中间值，再透过N=MaxList(N, Flr)、N=MinList(N, Ceil) 将N限制在界线10~30之内(很实用的语法技巧，可以学起来)。完成后，进场讯号是典型的价格通道系统，但此时N已经具有”Dynamic”的能力了（2），第二代：// 策略参数定义Inputs:     ceilingAmt(60),    // 最大回溯周期上限    floorAmt(20),      // 最小回溯周期下限    bolBandTrig(2.00); // 布林带标准差倍数
// 变量声明Vars:     lookBackDays(20),  // 动态调整的回溯周期    todayVolatility(0),// 当日波动率(30周期标准差)    yesterDayVolatility(0), // 昨日波动率    deltaVolatility(0);// 波动率变化率    buyPoint(0),       // 多头入场点(N周期最高价)    sellPoint(0),      // 空头入场点(N周期最低价)    longLiqPoint(0),   // 多头平仓点(N周期均价)    shortLiqPoint(0),  // 空头平仓点(N周期均价)    upBand(0),         // 布林带上轨    dnBand(0);         // 布林带下轨
// 计算当前波动率(30周期标准差)todayVolatility = StdDev(Close,30);
// 计算昨日波动率(通过偏移索引获取)yesterDayVolatility = StdDev(Close[1],30);
// 计算波动率变化百分比deltaVolatility = (todayVolatility - yesterDayVolatility)/todayVolatility;
// 动态调整回溯周期(基于波动率变化)lookBackDays = lookBackDays * (1 + deltaVolatility);lookBackDays = Round(lookBackDays,0);  // 取整
// 确保回溯周期在设定范围内lookBackDays = MinList(lookBackDays,ceilingAmt); // 不超过上限lookBackDays = MaxList(lookBackDays,floorAmt);   // 不低于下限
// 计算布林带通道upBand = BollingerBand(Close,lookBackDays,+BolBandTrig); // 上轨(均值+2σ)dnBand = BollingerBand(Close,lookBackDays,-BolBandTrig); // 下轨(均值-2σ)
// 计算突破点位buyPoint = Highest(High,lookBackDays);  // N周期最高价(多头触发点)sellPoint = Lowest(Low,lookBackDays);   // N周期最低价(空头触发点)
// 设置平仓点位longLiqPoint = Average(Close,lookBackDays);  // 多头平仓均价shortLiqPoint = Average(Close,lookBackDays); // 空头平仓均价
// 交易逻辑if(Close > upBand) then Buy("DBS-2 Buy") next bar at buyPoint stop;  // 突破上轨做多if(Close < dnBand) then SellShort("DBS-2 Sell") next bar at sellPoint stop; // 突破下轨做空if(MarketPosition = 1) then Sell("LongLiq") next bar at longLiqPoint stop;  // 多头平仓if(MarketPosition = -1) then BuytoCover("ShortLiq") next bar at shortLiqPoint stop; // 空头平仓
二代DBS其实精神不变，只是原本是价格通道换成布林通道，一样是依波动率来决定布林通道要计算的K棒数。