二、交易规则多头和空头设置a)对于买入设置，我们要求平滑的线性回归线是上升的，并且收盘价要高于线性回归线。此外，动能必须低于零，但在上升（在这个bar图上值比前一个bar图上值更大）。b)对于卖出设置，我们将要求平滑的线性回归线正在下降，并且收盘价低于线性回归线。此外，动能必须大于零，但在下降（在这个bar图上值上比前一个bar上值要小）。多头和空头进场a)在买入设置后，我们将在设置bar的高点上加上10周期平均真实范围ATR的50%，以确定我们的多头进入价格。该订单将4个bar内保持有效。b)在卖出设置后，我们将从设置bar的低点减去10周期平均真实范围ATR的50%，以确定我们的空头进入价格。该订单将在4个bar内保持有效。多头和空头退出一旦我们进入一个持仓，我们将执行我们一系列的ATR止损。我们在每个bar图上的退出将是最接近ATR保护性止损、盈亏平衡止损、追踪性止损、波动性止损或大利润止损的位置。三、程式码（仅适用于multicharts平台，其它平台自行移植）// 输入参数定义Inputs:    Price(Close),           // 使用收盘价作为基础价格    Regression(20),         // 线性回归周期长度    XMALength(15),          // 指数移动平均周期    MomentumLength(10),     // 动量指标周期    EntryPercent(.25),      // 入场价格偏移百分比(基于ATR)    EntryCounter(4);        // 最大允许入场尝试次数
// 变量声明Variables:    XLRAverage(0),          // 线性回归均线值    Moment(0),              // 动量指标值    BuySetup(False),        // 多头入场条件标志    SellSetup(False),       // 空头入场条件标志    BuyPrice(0),            // 多头入场价格    SellPrice(0),           // 空头入场价格    LongCounter(0),         // 多头尝试计数器    ShortCounter(0),        // 空头尝试计数器    atr(0);                 // 平均真实波幅(ATR)
// 指标计算XLRAverage = XAverage(LinearRegValue(Price, Regression, 0), XMALength); // 计算线性回归值的指数移动平均Moment = Momentum(Price, MomentumLength);  // 计算价格动量atr = AvgTrueRange(10);                    // 计算10周期平均真实波幅
// 计数器递增(每根K线)LongCounter = LongCounter + 1;   // 多头尝试计数器+1ShortCounter = ShortCounter + 1; // 空头尝试计数器+1
// 交易条件判断BuySetup = XLRAverage > XLRAverage[1]  // 线性回归均线上涨          AND Close > XLRAverage       // 收盘价在均线上方          AND Moment < 0               // 动量为负(价格下跌)          AND Moment > Moment[1];      // 但动量在改善(下跌减缓)
SellSetup = XLRAverage < XLRAverage[1] // 线性回归均线下跌           AND Close < XLRAverage      // 收盘价在均线下方           AND Moment > 0              // 动量为正(价格上涨)           AND Moment < Moment[1];     // 但动量在减弱(上涨减缓)
// 多头入场设置If BuySetup Then Begin    BuyPrice = High + ( EntryPercent * atr ); // 入场价=最高价+ATR的25%    LongCounter = 1;                          // 重置多头尝试计数器End;
// 空头入场设置If SellSetup Then Begin    SellPrice = Low - ( EntryPercent * atr ); // 入场价=最低价-ATR的25%    ShortCounter = 1;                         // 重置空头尝试计数器End;
// 执行交易指令If LongCounter <= EntryCounter Then Buy next bar at BuyPrice Stop;   // 在BuyPrice设置止损买单If ShortCounter <= EntryCounter Then sellshort next bar at SellPrice Stop; // 在SellPrice设置止损卖单