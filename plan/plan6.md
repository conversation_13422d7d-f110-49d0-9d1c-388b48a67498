Inputs:Length1(13) {ADX计算中的bar数},Length2(13) {ADX最小运行水平},Length3(13) {ADX行动水平的增量提升},Length4(8) {卖出止损的最大bar数},  Length5(5) {卖出止损的第二大bar数},  Length6(3) {卖出止损的第三大bar数},  Length7(1) {卖出止损的最少bar数},  Length8(13) {买入止损的最大bar数},Length9(8) {买入止损的第二多bar数},  Length10(5) {买入止损的第三多ba数},  Length11(2) {买入止损的最少bar数}.
// 计算ADX指标值，Length1为ADX计算周期Value1 = ADX(Length1);  
// 计算不同周期的最低低价减去最小变动单位(MinMove points)Value2 = Lowest(L, Length4) - MinMove points;  // Length4周期最低价减去缓冲Value3 = Lowest(L, Length5) - MinMove points;  // Length5周期最低价减去缓冲  Value4 = Lowest(L, Length6) - MinMove points;  // Length6周期最低价减去缓冲Value5 = Lowest(L, Length7) - MinMove points;  // Length7周期最低价减去缓冲
// 计算不同周期的最高高价加上最小变动单位Value6 = Highest(H, Length8) + MinMove points;  // Length8周期最高价加上缓冲Value7 = Highest(H, Length9) + MinMove points;  // Length9周期最高价加上缓冲Value8 = Highest(H, Length10) + MinMove points; // Length10周期最高价加上缓冲Value9 = Highest(H, Length11) + MinMove points; // Length11周期最高价加上缓冲
// 定义ADX强度分级条件Condition1 = Value1 >= Length2 and Value1 < (Length2 + Length3);  // ADX中等偏弱区间Condition2 = Value1 >= (Length2 + Length3) and Value1 < (Length2 + (Length3 * 2));  // ADX中等强度区间Condition3 = Value1 >= (Length2 + (Length3 * 2)) and Value1 < (Length2 + (Length3 * 3));  // ADX较强区间Condition4 = Value1 >= (Length2 + (Length3 * 3));  // ADX极强区间
// 空头入场逻辑{Sell Entry}// ADX较弱时使用较大周期(8根K线)的最低点作为止损位If Value1 < Length2 or Condition1 then sellshort ("WR S-8 Bar Entry") next bar at Value2 Stop;  // ADX中等时使用5根K线周期最低点If Condition2 then sellshort ("WR S-5 Bar Entry") next bar at Value3 Stop;  // ADX较强时使用3根K线周期最低点  If Condition3 then sellshort ("WR S-3 Bar Entry") next bar at Value4 Stop;// ADX极强时使用1根K线周期最低点If Condition4 then sellshort ("WR S-1 Bar Entry") next bar at Value5 Stop;  
// 多头入场逻辑  {Buy Entry}// ADX较弱时使用较大周期(13根K线)的最高点作为止损位If Value1 < Length2 or Condition1 then BUY ("WR L-13 Bar Entry") next bar at Value6 Stop;// ADX中等时使用8根K线周期最高点If Condition2 then BUY ("WR L-8 Bar Entry") next bar at Value7 Stop;  // ADX较强时使用5根K线周期最高点If Condition3 then BUY ("WR L-5 Bar Entry") next bar at Value8 Stop;// ADX极强时使用2根K线周期最高点If Condition4 then BUY ("WR L-2 Bar Entry") next bar at Value9 Stop;