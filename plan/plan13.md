// 输入参数声明inputs: profit(3),    // 止盈百分比参数        length(50);   // 均线周期参数
// 变量声明var: upline(0),       // 布林带上轨     downline(0),     // 布林带下轨     ma(0),           // 50周期均线     var_k(0),        // KDJ指标K值     var_d(0),        // KDJ指标D值     var_j(0),        // KDJ指标J值     cmi(0),          // 市场波动指数     lot(1);          // 交易手数
// 计算布林带上下轨（2倍标准差）upline=BollingerBand(close,50,2);   // 上轨=50周期均线+2倍标准差downline=BollingerBand(close,50,-2);// 下轨=50周期均线-2倍标准差
// 计算50周期简单移动平均线ma=average(close,50);
// 调用自定义KDJ指标函数（周期9，平滑3）Alex_KDJ(9,3,var_k,var_d,var_j);
// 计算30周期内最高价与最低价的波动范围value1=highest(high,30)-lowest(low,30);
// 计算CMI市场波动指数（当前价与29周期前价格的波动百分比）if value1<>0 then cmi=absvalue(close-close[29])/value1*100;
// 交易逻辑分为低波动(cmi<20)和高波动(cmi>=20)两种模式if cmi<20 then begin  // 低波动区间采用KDJ摆动策略        if var_k > var_d and var_d<30 then  // K上穿D且D值低于30时做多                buy("swing_b") lot shares next bar at market        else if var_k < var_d and var_d>70 then // K下穿D且D值高于70时做空                sellshort("swing_s") lot shares next bar at market;endelse if cmi>=20 then begin  // 高波动区间采用趋势突破策略        if close cross above upline then  // 价格突破布林带上轨做多                buy("trend_b") lot shares next bar at market        else if close cross under downline then // 价格跌破布林带下轨做空                sellshort("trend_s") lot shares next bar at market;end;
// 摆动策略的退出条件if var_k<var_d and close>entryprice*(1+0.01*profit) then // K下穿D且达到止盈点时平多        sell from entry("swing_b") next bar at marketelse if var_k>var_d and close<entryprice*(1-0.01*profit) then // K上穿D且达到止盈点时平空        buytocover from entry("swing_s") next bar at market;
// 趋势策略的退出条件if close cross under ma then // 价格跌破50均线时平多        sell from entry("trend_b") next bar at marketelse if close cross above ma then // 价格突破50均线时平空        buytocover from entry("trend_s") next bar at market;