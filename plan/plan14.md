// 策略参数声明Input: ADXLen(14), AvgLen(4), CH(8);  // ADX计算周期14，DeltaADX平均周期4，通道保持周期8Vars: UpCh(0), DnCh(0), MP(0), StopPrice(0), DeltaADX(0), DeltaAvg(0), LastOrderBar(0);
// 获取当前持仓状态MP = MarketPosition;
// 计算ADX指标及其变化量Value1 = ADX(ADXLen);  // 计算14周期ADX值DeltaADX = Value1 - Value1[1];  // ADX日变化量DeltaAvg = WAverage(DeltaADX , AvgLen );  // 对ADX变化量进行4周期加权平均
// 入场条件判断及通道计算If (DeltaAvg < 0 or DeltaAvg crosses over 0) and BarNumber > 1 then Begin    UpCh = Close + Average(Range, 4)/2;  // 上轨=收盘价+4周期平均波幅/2    DnCh = Close - Average(Range, 4)/2;  // 下轨=收盘价-4周期平均波幅/2    LastOrderBar = BarNumber;  // 记录当前Bar序号End;
// 清空入场标记（若已有持仓）If MP <> 0 then LastOrderBar = -999;
// 下单逻辑（在通道保持周期内有效）If BarNumber < LastOrderBar + CH then Begin    Buy next bar at UpCh stop;  // 突破上轨做多    sellshort next bar at DnCh stop;  // 突破下轨做空    // 初始止损单（未持多单时设置空单止损，未持空单时设置多单止损）    If MP <> 1 then sell ("LEntryStop") next bar at Lowest(Low,6) stop;    If MP <> -1 then buytocover ("SEntryStop") next bar at Highest(High,6) stop;End;
// 多头持仓止损计算If MP = 1 and MP[1] <> 1 then Begin  // 新开多单时    LastOrderBar = -999;    StopPrice = Low - Average(Range,4);  // 初始止损=最低价-4周期平均波幅End;
// 空头持仓止损计算If MP = -1 and MP[1] <> -1 then Begin  // 新开空单时    StopPrice = High + Average(Range,4);  // 初始止损=最高价+4周期平均波幅    LastOrderBar = -999;End;
// 多头动态止损调整If MP = 1 then Begin    sell ("LongTrailStop") next bar at StopPrice stop;  // 挂止损单    StopPrice = StopPrice + (Low-StopPrice)/3;  // 动态上移止损位（1/3法则）End;
// 空头动态止损调整If MP = -1 then Begin    buytocover ("ShortTrailStop") next bar at StopPrice stop;  // 挂止损单    StopPrice = StopPrice - (StopPrice-High)/3;  // 动态下移止损位（1/3法则）End;