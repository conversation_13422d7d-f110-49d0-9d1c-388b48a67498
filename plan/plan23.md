二、交易规则多头和空头进场条件a)当WOBV越过其简单移动平均线时，做多进场生效。b)当WOBV低于其简单移动平均线时，做空进场生效。多头和空头进场a)多头进场是在设置bar的高点之上1点。b)空头进场是在设置bar的低点以下1个点。多头和空头退出a)多头在初始保护性止损点、盈亏平衡止损点或追踪止损点以下1点出场。如果WOBV低于其移动平均线，也要退出多头。b)在初始保护性止损点、盈亏平衡止损点或追踪止损点之上1点退出空头。如果WOBV越过其移动平均线，也要退出空头。三、完整程式码（仅适用于multicharts平台，其它平台自行移植）// 输入参数定义Inputs: AvgLength(25);  // 设置移动平均线的计算周期为25根K线
// 变量声明Variables:     WOBV(0),       // 加权OBV指标值（Weighted On-Balance Volume）    SMA(0),        // WOBV的简单移动平均值    BuySetup(False),  // 买入信号触发标志    SellSetup(False), // 卖出信号触发标志    LEPrice(0),    // 多头入场价格基准    SEPrice(0);    // 空头入场价格基准
// 计算加权OBV指标// 当价格区间(Range)不为零时，根据收盘价与开盘价差占区间的比例乘以成交量If Range <> 0 Then WOBV = WOBV + (((Close - Open) / Range) * Volume);
// 计算WOBV指标的简单移动平均SMA = Average(WOBV, AvgLength);
// 生成交易信号逻辑If WOBV Crosses Above SMA Then Begin  // 当WOBV上穿SMA时    BuySetup = True;     // 激活买入信号    LEPrice = High;      // 记录当前K线最高价作为买入基准价    SellSetup = False;   // 关闭卖出信号    buytocover next bar at market;  // 平空仓指令End;
If WOBV Crosses Below SMA Then Begin  // 当WOBV下穿SMA时    SellSetup = True;    // 激活卖出信号    SEPrice = Low;       // 记录当前K线最低价作为卖出基准价    BuySetup = False;    // 关闭买入信号    sell next bar at market;  // 平多仓指令End;
// 持仓状态检查If MarketPosition = 1 Then BuySetup = False;   // 若持有多头则关闭买入信号If MarketPosition = -1 Then SellSetup = False; // 若持有空头则关闭卖出信号
// 执行交易指令If BuySetup Then Buy Next Bar at LEPrice + 1 Point Stop;  // 在突破高点1个点位时做多If SellSetup Then sellshort Next Bar at SEPrice - 1 Point Stop;  // 在突破低点1个点位时做空