TradeRange = (Highest(H,TradeLength) - Lowest(L,TradeLength))/100;//交易区间计算：20周期高点-20周期低点差除100Long_Entry_Price = Lowest(L,TradeLength)+TradeRange*LongPercent;//多单进场价格：20周期低点 + 交易区间*多头因子Short_Entry_Price = Lowest(L,TradeLength)+TradeRange*ShortPercent;//做空进场价格：20周期低点 + 交易区间*空头因子ShortExit = Lowest(L,TradeLength)+TradeRange*ExitShort_Percent;//空单出场：20周期低点 + 交易区间乘空头出场因子LongExit = Lowest(L,TradeLength)+TradeRange*ExitLong_Percent;//多单出场：20周期低点+ 交易区间乘多头出场因子
If high > Top_Range then TrendDirection = 1; //如果当根bar最高价大于顶部区间，趋势方向为1if low < Bottom_Range then TrendDirection = -1;	//如何当根bar最低价小于底部区间，趋势方向为-1
If TrendDirection = 1 then Buy next bar at Long_Entry_Price stop; //趋势方向为1时开仓做多If TrendDirection = -1 then Sellshort Next Bar at Short_Entry_Price stop; //趋势方向为-1时开仓做空
If marketposition >= 1 then Sell next bar at LongExit stop; //持有多单，在多单出场条件平仓If marketposition <= -1 then BuytoCover Next Bar at ShortExit stop;    //持有空单，在空单出场条件平仓