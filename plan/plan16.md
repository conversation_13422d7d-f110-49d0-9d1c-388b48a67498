// 策略输入参数定义Inputs:     HiLoLen(8),      // 高低点计算周期长度    ATRLen(10),      // 平均真实波幅计算周期    SetUpInt(4),     // 设置间隔周期    XAvgLen(9),      // 指数移动平均线周期    SetUpLen(24),    // 设置持续时间    RiskMult(2),     // 风险乘数    HiLoStop(44);    // 高低点止损周期
// 变量声明Vars:     LPivot(0),       // 低点枢轴位置    HPivot(0),       // 高点枢轴位置      EMA(0),          // 指数移动平均值    BuySetupHigh(0), // 买入设置高点    BuySetupCount(100), // 买入设置计数器    SellSetupLow(0), // 卖出设置低点    SellSetupCount(100); // 卖出设置计数器Vars:     BuySetupLow(0),  // 买入设置低点    SellSetupHigh(0), // 卖出设置高点    LExitFlag(0),    // 多头退出标志    SExitFlag(0),    // 空头退出标志    LExitLow(0),     // 多头退出低点    SExitHigh(0),    // 空头退出高点    PMult(2),        // 利润乘数    TT(0);           // 总交易次数
// 基本系统条件定义{Assignment of basic system conditions}Condition1 = SwingLow(1, Low, 1, 2) <> -1;  // 检测低点摆动Condition2 = SwingHigh(1, High, 1, 2) <> -1; // 检测高点摆动Condition3 = Low[1] = Lowest(Low, HiLoLen)[1]; // 检测是否为周期内最低点Condition4 = High[1] = Highest(High, HiLoLen)[1]; // 检测是否为周期内最高点Condition5 = TrueRange > AvgTrueRange(ATRLen); // 检测真实波幅是否大于平均波幅
// 系统条件变量赋值{Variables which represent indicate the occurrence of basic system criteria}LPivot= MRO(Condition1 AND Condition3, SetUpInt, 1); // 查找符合条件的低点枢轴HPivot = MRO(Condition2 AND Condition4, SetUpInt, 1); // 查找符合条件的高点枢轴
// 系统变量赋值{Assignment of System variables}EMA = XAverage(Close, XAvgLen); // 计算指数移动平均TT = TotalTrades; // 获取总交易次数
// 买入设置逻辑{Buy Setup}IF LPivot > 0 AND Lowest(Low, LPivot) < Low[LPivot+1] Then Begin    // 当满足波动条件和价格条件时设置买入参数    IF Condition5 AND Close > MaxList(Close[1], Open, Low + (2*(TrueRange/3)), Low[LPivot+1]) Then Begin        BuySetupHigh = High; // 记录当前高点        BuySetupLow = Low;   // 记录当前低点        BuySetupCount = 0;   // 重置买入计数器    End;End;
// 卖出设置逻辑{Sell Setup}IF HPivot > 0 AND Highest(High, HPivot) > High[HPivot+1] Then Begin    // 当满足波动条件和价格条件时设置卖出参数    IF Condition5 AND Close < MinList(Close[1], Open, Low + (TrueRange/3), High[HPivot+1]) Then Begin        SellSetupLow = Low;   // 记录当前低点        SellSetupHigh = High; // 记录当前高点        SellSetupCount = 0;   // 重置卖出计数器    End;End;
// 盈亏平衡止损逻辑{Breakeven Stops, above floor}IF MarketPosition = 1 OR TT <> TT[1] Then Begin    BuySetupCount = SetUpLen; // 重置买入计数器    // 当价格达到利润目标或首次设置时平仓    IF Close > EntryPrice + (PMult*(EntryPrice - LExitLow)) OR LExitFlag = 0 Then Begin        sell Next Bar at EntryPrice Stop; // 在入场价止损平仓        LExitFlag = 0; // 重置退出标志    End;End;
IF MarketPosition = -1 OR TT <> TT[1] Then Begin    SellSetupCount = SetUpLen; // 重置卖出计数器    // 当价格达到利润目标或首次设置时平仓    IF Close < EntryPrice - (PMult*(SExitHigh - EntryPrice)) OR SExitFlag = 0 Then Begin        buytocover Next Bar at EntryPrice Stop; // 在入场价止损平仓        SExitFlag = 0; // 重置退出标志    End;End;
// 入场周期计数器更新{Varaible counters for the length of the Entry period}BuySetupCount = BuySetupCount + 1; // 买入计数器递增SellSetupCount = SellSetupCount + 1; // 卖出计数器递增
// 多头入场逻辑{Long Entry}IF BuySetupCount <= SetUpLen Then Begin    // 当价格突破设置高点且高于EMA时买入    IF BuySetupHigh + 1 Point > EMA Then Begin        Buy Next Bar at BuySetupHigh + 1 Point Stop; // 在突破点买入        LExitLow = BuySetupLow; // 设置初始止损低点        LExitFlag = 1; // 设置退出标志    End;End;
// 空头入场逻辑{Short Entry}IF SellSetupCount <= SetUpLen Then Begin    // 当价格跌破设置低点且低于EMA时卖出    IF SellSetupLow + 1 Point < EMA Then Begin        sellshort Next Bar at SellSetupLow - 1 Point Stop; // 在突破点卖出        SExitHigh = SellSetupHigh; // 设置初始止损高点        SExitFlag = 1; // 设置退出标志    End;End;
// 初始止损设置{Initial Breakeven Stops}IF LExitFlag = 1 Then sell Next Bar at LExitLow - 1 Point Stop; // 设置多头止损IF SExitFlag = 1 Then buytocover Next Bar at SExitHigh + 1 Point Stop; // 设置空头止损
// 系统退出逻辑{System Exits}sell Next Bar at Highest(High, HiLoStop) Limit; // 多头在周期高点限价退出buytocover Next Bar at Lowest(Low, HiLoStop) Limit; // 空头在周期低点限价退出