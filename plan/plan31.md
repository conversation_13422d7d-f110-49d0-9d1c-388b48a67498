系统构成：基于最高价、最低价、收盘价三者平均值计算而来的三价均线 基于三价均线加减真实波幅计算而来的通道上下轨入场条件：三价均线向上，并且价格上破通道上轨，开多单； 三价均线向下，并且价格下破通道下轨，开空单。出场条件:持有多单时，价格下破三价均线，平多单 ；持有空单时，价格上破三价均线，平空单。完整程式源码（仅限multicharts平台，其它平台自行移植！）// 策略参数定义input:avglength(40),  // 移动平均计算周期长度      atrlength(40),  // 平均真实波幅(ATR)计算周期长度      pAngle(50);     // 线性回归角度阈值(用于判断趋势强度)
// 变量声明var:movavgval(0),     // 三价平均(高+低+收)/3的移动平均值    upBand(0),        // 上轨线(移动平均+ATR)    downBand(0),      // 下轨线(移动平均-ATR)    mp(0),            // 当前持仓状态(1=多仓,-1=空仓,0=无仓)    vLRSlope(0),      // 线性回归斜率    vLRAngle(0),      // 线性回归角度(反映趋势强度)    vLRIntercept(0),  // 线性回归截距    vLRValueRaw(0);   // 线性回归原始值
// 计算三价移动平均movavgval = average((high+low+close)/3, avglength);
// 计算通道上下轨(基于ATR波动幅度)upBand = movavgval + AvgTrueRange(atrlength);downBand = movavgval - AvgTrueRange(atrlength);
// 计算移动平均值的线性回归指标value1 = LinearReg(movavgval, 2, 0, vLRSlope, vLRAngle, vLRIntercept, vLRValueRaw);
// 获取当前持仓状态mp = marketposition;
// 交易逻辑// 当线性回归角度超过阈值(强趋势条件)if vLRAngle > pAngle or vLRAngle < -pAngle then begin    // 做多条件：均线上涨+无多仓+收盘价突破上轨    if movavgval > movavgval‌:ml-citation{ref="1" data="citationList"} and mp <> 1 and close > upBand then         buy next bar at market;
    // 做空条件：均线下跌+无空仓+收盘价跌破下轨    if movavgval < movavgval‌:ml-citation{ref="1" data="citationList"} and mp <> -1 and close < downBand then         sellshort next bar at market;end;
// 平仓逻辑// 多仓止损：当价格回落至移动平均线if mp = 1 then sell next bar at movavgval stop;// 空仓止损：当价格回升至移动平均线if mp = -1 then buytocover next bar at movavgval stop;
指标显示效果：input:avglength(40),atrlength(40);var:movavgval(0),upBand(0),downBand(0),mp(0);
movavgval=average((high+low+close)/3,avglength);upBand=movavgval+AvgTrueRange(atrlength);downBand=movavgval-AvgTrueRange(atrlength);
plot1(movavgval);plot2(upBand);plot3(downBand);