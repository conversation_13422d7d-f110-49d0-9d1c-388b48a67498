// 策略参数定义Inputs: TIMEOFFSET(0);  // 时间偏移量(分钟)，用于调整交易时段Variables:     LOCKPTS(1),        // 盈利锁定点数    RETRACESELL(0),    // 回撤卖出触发价    RETRACEBUY(0),     // 回撤买入触发价      SRETRACE(0),       // 卖出回撤信号标志(1=触发)    LRETRACE(0),       // 买入回撤信号标志(1=触发)    ENTRIES(0),        // 当日交易次数计数器    LIMPETUS(0),       // 买入动量信号标志(1=触发)    IMPETUSBUY(0),     // 买入动量触发价    SIMPETUS(0),       // 卖出动量信号标志(1=触发)     IMPETUSSELL(0),    // 卖出动量触发价    VALUE11(0);        // 基准价格(使用DATA2的收盘价)
// 初始化基准价格(VALUE11)IF CURRENTBAR = 1 THEN VALUE11 = CLOSE DATA(2);  // 首个K线初始化IF DATE > DATE[1] THEN VALUE11 = CLOSE DATA(2);  // 新交易日重置
// 每日开盘时重置所有交易信号IF DATE > DATE[1] THEN BEGIN    SRETRACE = 0; LRETRACE = 0;     RETRACEBUY = 0; RETRACESELL = 0;    ENTRIES = 0; LIMPETUS = 0;     IMPETUSBUY = 0; SIMPETUS = 0;     IMPETUSSELL = 0;END;
// 主交易时段(13:30-15:00)IF TIME >= 1330+TIMEOFFSET AND TIME < 1500+TIMEOFFSET THEN BEGIN    // 空仓且当日未交易时执行    IF MARKETPOSITION = 0 AND ENTRIES = 0 THEN BEGIN        // 卖出回撤条件：DATA2低点不创新低，且高点低于ID中点，且跌幅超1.3%        IF LOW DATA(2) >= LOW[1] DATA(2)            AND HIGH DATA(2) < (IDHigh+IDLow)*0.5            AND VALUE11-IDLow >= 0.013*VALUE11 THEN BEGIN            SRETRACE = 1;  // 触发卖出回撤信号            RETRACESELL = LOW;  // 记录触发价格        END;
        // 执行卖出回撤交易(S1)        IF SRETRACE = 1 AND LOW > IDLow THEN             SELLSHORT ("S1") RETRACESELL-0.05 STOP;  // 低于触发价0.05点挂单
        // 买入回撤条件：DATA2高点不创新高，且低点高于ID中点，且涨幅超1.3%          IF HIGH DATA(2) <= HIGH[1] DATA(2)            AND LOW DATA(2) > (IDHigh+IDLow)*0.5            AND IDHigh-VALUE11 >= 0.013*VALUE11 THEN BEGIN            LRETRACE = 1;  // 触发买入回撤信号            RETRACEBUY = HIGH;  // 记录触发价格        END;
        // 执行买入回撤交易(L1)        IF LRETRACE = 1 AND HIGH < IDHigh THEN             BUY ("L1") RETRACEBUY+0.05 STOP;  // 高于触发价0.05点挂单
        // 13:45后动量突破条件        IF TIME >= 1345+TIMEOFFSET THEN BEGIN            // 买入动量：涨幅超1.3%            IF IDHigh-VALUE11 >= 0.013*VALUE11 THEN BEGIN                LIMPETUS = 1;  // 触发买入动量信号                IMPETUSBUY = IDHigh;  // 记录突破高点            END;            // 执行买入动量交易(L2)            IF LIMPETUS = 1 THEN                 BUY ("L2") IMPETUSBUY+0.05 STOP;
            // 卖出动量：跌幅超1.3%            IF VALUE11-IDLow >= 0.013*VALUE11 THEN BEGIN                SIMPETUS = 1;  // 触发卖出动量信号                IMPETUSSELL = IDLow;  // 记录突破低点            END;            // 执行卖出动量交易(S2)            IF SIMPETUS = 1 THEN                 SELLSHORT ("S2") IMPETUSSELL-0.05 STOP;        END;    END;
    // 空头持仓止损逻辑    IF MARKETPOSITION < 0 THEN BEGIN        // S1策略止损：取DATA2高点+0.1点与入场价+0.9%的较大值        BUYTOCOVER ("SX1") ENTRY("S1")             MAXLIST(HIGH DATA(2)+0.1, ENTRYPRICE+(0.009*VALUE11)) STOP;        // S2策略止损：取DATA2高点+0.1点与入场价+0.1%的较大值        BUYTOCOVER ("SX2") ENTRY("S2")             MAXLIST(HIGH DATA(2)+0.1, ENTRYPRICE+(0.001*VALUE11)) STOP;    END;
    // 多头持仓止损逻辑    IF MARKETPOSITION > 0 THEN BEGIN        // L1策略止损：取DATA2低点-0.1点与入场价-0.9%的较小值        SELL ("LX1") ENTRY("L1")             MINLIST(LOW DATA(2)-0.1, ENTRYPRICE-(0.009*VALUE11)) STOP;        // L2策略止损：取DATA2低点-0.1点与入场价-0.1%的较小值        SELL ("LX2") ENTRY("L2")             MINLIST(LOW DATA(2)-0.1, ENTRYPRICE-(0.001*VALUE11)) STOP;    END;END;
// 收盘前强制平仓时段(15:00-15:15)IF TIME >= 1500+TIMEOFFSET AND TIME <= 1515+TIMEOFFSET THEN BEGIN    SELL ("LX3") LOW-0.05 STOP;       // 多头平仓    BUYTOCOVER ("SX3") HIGH+0.05 STOP; // 空头平仓END;
// 多头盈利保护(浮盈0.8%后锁定1点利润)IF MARKETPOSITION > 0 THEN BEGIN    IF MaxTradeHigh-ENTRYPRICE >= 0.008*VALUE11 THEN         SELL ("LX4") ENTRYPRICE+LOCKPTS STOP;END;
// 空头盈利保护(浮盈0.8%后锁定1点利润) IF MARKETPOSITION < 0 THEN BEGIN    IF ENTRYPRICE-MinTradeLow >= 0.008*VALUE11 THEN         BUYTOCOVER ("SX4") ENTRYPRICE-LOCKPTS STOP;END;
// 交易计数IF MARKETPOSITION <> 0 THEN ENTRIES = ENTRIES+1;