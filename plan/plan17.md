一、简介1、构建两个动量振荡器。一个代表高点减去移动平均线；另一个将代表低点减去移动平均线。使用收盘价的长期移动平均线来识别趋势。当趋势上升时，我们将使用低点震荡指标来设置买入机会。当趋势下降时，我们将使用高点震荡指标来发出卖出机会的信号。2、对于多头和空头头寸，我们将设置资金管理止损并启用跟踪出场。我们退出多头头寸的信号将是当高点震荡指标低于零时；当低点振荡器超过零时，我们将退出我们的空头头寸。3、高点振荡器即用最高价减最高价的ema均线，低点振荡品种即用最低价减最低价ema均线。二、交易规则初始设置a) 计算 60 周期EMA（指数移动平均线）的收盘价。b) 计算高位震荡器和低位震荡器。做多a) 检查 60 周期 EMA 是否大于 上一个bar的60ema。b) 检查低振荡器是否低于零但上升。换句话说，低震荡指标应该是一个负值，但比上一根bar的负值要小。c) 在一根bar开盘时买入。做空a) 检查 60 bar EMA 是否小于前bar值 EMA[1]。b) 检查高位震荡指标是否高于零线但下降。换句话说，高位震荡指标应该是一个正值，但小于一根bar线之前的值。c) 在下一根bar开盘时卖空。退出规则a)一旦我们持有头寸，我们将设置资金管理止损、盈亏平衡止损和追踪止损。b)此外，当高震荡指标低于零时，我们将在下一次开盘时退出多头头寸；当低位震荡指标上穿零时，我们将退出空头头寸。三、策略源码1、指标“High Oscillator”Input: Len1(10);var:HighOsc(0);HighOsc = High - XAverage(High, Len1);plot1(HighOsc,"Ho",yellow);plot2(0,"0",green);
2、指标“Low Osc”Input: Len1(10), Len2(60);var:LowOsc(0);LowOsc = Low - XAverage(low, Len1);plot1(LowOsc,"Lo",yellow);plot2(0,"0",green);
3、策略信号：（仅适用于multicharts平台，其它平台自行移植！）// 策略参数定义Input: Len1(10),    // 短期平均周期长度(用于计算高低点振荡指标)       Len2(60),    // 长期收盘价平均周期长度       TrailStp(700); // 追踪止损金额
// 变量声明Vars: HighOsc(0),   // 高点振荡指标 = 当前高点 - 短期高点均值      LowOsc(0),    // 低点振荡指标 = 当前低点 - 短期低点均值      CloseAvg(0),  // 长期收盘价均值      BuySetup(False),  // 多头入场条件标志      SellSetup(False); // 空头入场条件标志
{ 指标计算部分 }HighOsc = High - XAverage(High, Len1);  // 计算高点偏离短期均线的程度LowOsc = Low - XAverage(Low, Len1);     // 计算低点偏离短期均线的程度CloseAvg = XAverage(Close, Len2);       // 计算长期收盘价均线
{ 入场条件设置 }// 多头入场条件：长期均线上涨 + 收盘价高于均线 + 低点指标为负(价格低于短期均线)BuySetup = CloseAvg > CloseAvg[1] AND Close > CloseAvg AND LowOsc < 0;
// 空头入场条件：长期均线下跌 + 收盘价低于均线 + 高点指标为正(价格高于短期均线)SellSetup = CloseAvg < CloseAvg[1] AND Close < CloseAvg AND HighOsc > 0;
{ 多头交易逻辑 }// 多头入场：满足入场条件且低点指标开始回升If BuySetup AND LowOsc > LowOsc[1] then Buy next bar at Market;
// 多头出场逻辑If MarketPosition = 1 then begin  // 追踪止损：基于收盘价设置固定点数的追踪止损  sell("LTrailStop") next bar at Close-(TrailStp/BigPointValue) Stop;
  // 趋势反转退出：当高点指标下穿零线时平仓  If HighOsc Crosses Under 0 then sell("LTiming") next bar at Market;End;
{ 空头交易逻辑 }// 空头入场：满足入场条件且高点指标开始回落If SellSetup AND HighOsc < HighOsc[1] then sellshort next bar at Market;
// 空头出场逻辑If MarketPosition = -1 then Begin  // 追踪止损：基于收盘价设置固定点数的追踪止损  buytocover("STrailStop")next bar at Close+(TrailStp/BigPointValue) Stop;
  // 趋势反转退出：当低点指标上穿零线时平仓  If LowOsc crosses Over 0 then buytocover("STiming") next bar at Market;End;