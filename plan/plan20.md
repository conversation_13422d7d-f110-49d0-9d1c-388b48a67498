二、逻辑规则设置a) 计算 30 周期 EMA 的高点和 30 周期 EMA 的低点。b) 计算 12 根 ADX。做多a) 检查收盘价是否高于 EMA 高点和上升的 ADX。b) 如果两个条件在同一条bar上为真，则通过将 MAC 高度的一半加上设置bar的收盘价来计算入场价格。c) 在设置后保持两个bar的挂单有效。做空a) 检查收盘价是否低于 EMA 的低点和上升的 ADX。b) 如果两个条件在同一个bar上为真，则通过从设置bar的收盘价减去 MAC 高度的一半来计算入场价格。c) 在设置后保持两个bar的挂单有效。退出交易a) 在收盘价低于低点 EMA 时退出多头头寸。b) 在收盘价高于高点 EMA 时退出空头头寸。还可以启用资金管理止损、盈亏平衡止损和美元风险追踪止损。三、完整策略源码（仅适用于multicharts平台，其它平台请自行移植！）
// 策略输入参数声明Inputs:     AvgLen(30),    // 均线计算周期长度    ADXLen(12),    // ADX指标计算周期    EntryBar(2);   // 入场信号确认所需K线数
// 变量声明Vars:     UpperMA(0),    // 上轨均线（基于最高价的指数平均）    LowerMA(0),    // 下轨均线（基于最低价的指数平均）    ADXValue(0),   // ADX指标当前值    ChanSpread(0), // 通道宽度的一半    BuySetup(False),  // 多头入场条件标志    SellSetup(False), // 空头入场条件标志    BuyTarget(0),  // 多头入场目标价位    SellTarget(0), // 空头入场目标价位    MROBS(0),      // 多头信号有效周期计数器    MROSS(0);      // 空头信号有效周期计数器
{==== 指标计算部分 ====}// 计算前一根K线的上轨均线（基于最高价的指数平均）UpperMA = XAverage(High, AvgLen)[1];  // 计算前一根K线的下轨均线（基于最低价的指数平均）LowerMA = XAverage(Low, AvgLen)[1];  // 计算ADX指标值（衡量趋势强度）ADXValue = ADX(ADXLen);// 计算通道宽度的一半（用于确定入场目标位）ChanSpread = (UpperMA - LowerMA) / 2;
{==== 交易信号设置 ====}// 多头入场条件：收盘价突破上轨且ADX指标上升BuySetup = Close > UpperMA AND ADXValue > ADXValue[1];// 空头入场条件：收盘价跌破下轨且ADX指标上升SellSetup = Close < LowerMA AND ADXValue > ADXValue[1];
// 设置多头入场目标价位（突破点+半通道宽度）IF BuySetup Then BuyTarget = Close + ChanSpread;// 设置空头入场目标价位（突破点-半通道宽度）IF SellSetup Then SellTarget = Close - ChanSpread;
{==== 信号有效性验证 ====}// 检查多头信号在EntryBar周期内是否持续有效MROBS = MRO(BuySetup, EntryBar, 1);// 检查空头信号在EntryBar周期内是否持续有效MROSS = MRO(SellSetup, EntryBar, 1);
{==== 入场逻辑 ====}// 多头入场：信号有效且当前无多头持仓时，在下根K线以BuyTarget触发止损买单IF MROBS <> -1 AND MRO(MarketPosition=1, MROBS, 1) = -1 Then     Buy Next Bar at BuyTarget Stop;// 空头入场：信号有效且当前无空头持仓时，在下根K线以SellTarget触发止损卖单IF MROSS <> -1 AND MRO(MarketPosition=-1, MROSS, 1) = -1 Then     sellshort Next Bar at SellTarget Stop;
{==== 出场逻辑 ====}// 多头平仓：下根K线价格触及上轨下方1个最小价位单位时止损出场sell Next Bar at UpperMA - 1 Point Stop;// 空头平仓：下根K线价格触及下轨上方1个最小价位单位时止损出场buytocover Next Bar at LowerMA + 1 Point Stop;