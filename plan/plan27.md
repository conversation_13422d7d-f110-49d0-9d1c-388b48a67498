一、指标画线与交易源码指标：NetGrid// 策略参数定义inputs:    N1(1000),  // 长期移动平均线计算周期(1000根K线)    N2(200),   // 波动幅度(bd)的平滑周期(200根K线)    N3(20);    // 通道宽度调节系数
// 主指标变量声明var:    ma1(0),    // N1周期收盘价移动平均线    bd(0),     // 单根K线波动幅度(最高价-最低价)    bdma(0),   // N2周期平均波动幅度    up1(0),    // 上轨1(ma1 + 1/4通道宽度)    up2(0),    // 上轨2(ma1 + 1/2通道宽度)    up3(0),    // 上轨3(ma1 + 3/4通道宽度)    up4(0),    // 上轨4(ma1 + 完整通道宽度)    dw1(0),    // 下轨1(ma1 - 1/4通道宽度)    dw2(0),    // 下轨2(ma1 - 1/2通道宽度)    dw3(0),    // 下轨3(ma1 - 3/4通道宽度)    dw4(0);    // 下轨4(ma1 - 完整通道宽度)
// 指标计算部分ma1 = Average(close, n1);  // 计算N1周期简单移动平均线
bd = High - Low;  // 计算单根K线波动范围bdma = Average(bd, n2);  // 计算N2周期平均波动幅度
// 计算多级通道上轨(阻力位)up1 = ma1 + N3 * bdma / 4;   // 上轨1：基准线+1/4通道宽度up2 = ma1 + N3 * bdma / 2;   // 上轨2：基准线+1/2通道宽度up3 = ma1 + N3 * bdma * 3/4; // 上轨3：基准线+3/4通道宽度up4 = ma1 + N3 * bdma;       // 上轨4：基准线+完整通道宽度
// 计算多级通道下轨(支撑位)dw1 = ma1 - N3 * bdma / 4;   // 下轨1：基准线-1/4通道宽度dw2 = ma1 - N3 * bdma / 2;   // 下轨2：基准线-1/2通道宽度dw3 = ma1 - N3 * bdma * 3/4; // 下轨3：基准线-3/4通道宽度dw4 = ma1 - N3 * bdma;       // 下轨4：基准线-完整通道宽度
// 图表绘制指令Plot9(ma1);  // 绘制基准移动平均线(通常显示为中线)Plot1(up1);  // 绘制上轨1(1/4通道线)Plot2(up2);  // 绘制上轨2(1/2通道线)Plot3(up3);  // 绘制上轨3(3/4通道线)Plot4(up4);  // 绘制上轨4(完整通道线)Plot5(dw1);  // 绘制下轨1(1/4通道线)Plot6(dw2);  // 绘制下轨2(1/2通道线)Plot7(dw3);  // 绘制下轨3(3/4通道线)Plot8(dw4);  // 绘制下轨4(完整通道线)
策略：NetGrid
// 策略参数定义inputs:    N1(1000),  // 长期移动平均线周期    N2(200),   // 波动幅度(bd)的平滑周期      N3(20);    // 通道宽度乘数
// 变量声明var:    ma1(0),    // N1周期移动平均线    bd(0),     // 当日波动幅度(High-Low)    bdma(0),   // N2周期平均波动幅度    up1(0),    // 第一阻力位(ma1 + 1/4通道宽度)    up2(0),    // 第二阻力位(ma1 + 1/2通道宽度)    up3(0),    // 第三阻力位(ma1 + 3/4通道宽度)     up4(0),    // 第四阻力位(ma1 + 完整通道宽度)    dw1(0),    // 第一支撑位(ma1 - 1/4通道宽度)    dw2(0),    // 第二支撑位(ma1 - 1/2通道宽度)    dw3(0),    // 第三支撑位(ma1 - 3/4通道宽度)    dw4(0);    // 第四支撑位(ma1 - 完整通道宽度)
// 指标计算ma1 = Average(close, n1);          // 计算长期移动平均线bd = High - Low;                   // 当日价格波动范围bdma = Average(bd, n2);            // 波动幅度的N2周期平均值
// 计算多级通道位置 (基于波动幅度自适应)up1 = ma1 + N3 * bdma / 4;         // 上轨1/4位置up2 = ma1 + N3 * bdma / 2;         // 上轨1/2位置  up3 = ma1 + N3 * bdma * 3 / 4;     // 上轨3/4位置up4 = ma1 + N3 * bdma;             // 完整上轨位置
dw1 = ma1 - N3 * bdma / 4;         // 下轨1/4位置dw2 = ma1 - N3 * bdma / 2;         // 下轨1/2位置dw3 = ma1 - N3 * bdma * 3 / 4;     // 下轨3/4位置dw4 = ma1 - N3 * bdma;             // 完整下轨位置
// 持仓状态跟踪变量var:    Intrabarpersist Int CurrStatus (0);  // 持久化变量，记录当前持仓状态CurrStatus = Marketposition * CurrentShares;  // 计算当前净持仓
//====================== 多头交易逻辑 ======================//// 基础入场：价格上穿均线开多仓if Close Cross Above ma1 then begin   buy("ma1B") 1 shares next bar at market;end;
// 空头持仓时的金字塔止盈if CurrStatus = 1 then begin          // 当持有多头1单位时   SellShort("up1S") 1 shares next bar at up1 limit;  // 在上轨1/4位置反手开空end;
if CurrStatus = -1 then begin         // 当持有空头1单位时     SellShort("up2S") 2 shares next bar at up2 limit;  // 在上轨1/2位置加空end;
if CurrStatus = -3 then begin         // 当持有空头3单位时   BuytoCover("up2SP") From Entry("up2S") 2 shares next bar at up1 limit;  // 平掉部分空单   SellShort("up3S") 4 shares next bar at up3 limit;  // 在上轨3/4位置再加空end;
// 极端行情平仓：价格突破完整上轨时平所有空仓if Close Cross Above Up4 and Marketposition < 0 then begin   BuytoCover("CAll") all Shares next bar at market;end;
//====================== 空头交易逻辑 ======================//// 基础入场：价格下穿均线开空仓if Close Cross Under ma1 then begin   SellShort("ma1S") 1 shares next bar at market;end;
// 多头持仓时的金字塔止盈if CurrStatus = -1 then begin         // 当持有空头1单位时   Buy("dw1B") 1 shares next bar at dw1 limit;  // 在下轨1/4位置反手开多end;
if CurrStatus = 1 then begin          // 当持有多头1单位时   Buy("dw2B") 2 shares next bar at dw2 limit;  // 在下轨1/2位置加多end;
if CurrStatus = 3 then begin          // 当持有多头3单位时   Sell("dw2SP") From Entry("dw2B") 2 shares next bar at dw1 limit;  // 平掉部分多单end;
二、思路如下：1、求周期为1000的均线。2、求每日bar 的最高价与最低价的差，然后求这个差值的200周期均线，每日差均线。3、向上的upgrid为以20倍每日差均线为基础分四档，第一档是0.25倍，第二档为0.5倍，第三档为0.75倍，第四档为1倍。4、向下的downgrid为以20倍每日差均线为基础分四档，第一档是-0.25倍，第二档为-0.5倍，第三档为-0.75倍，第四档为-1倍。5、1000周期均线分别+四档upgrid为上四档， 1000周期均线分别 - 四档downgrid为下四档，形成一个连均线共9档的区间线。三、核心逻辑：1、指标计算部分：计算N1周期(1000)的收盘价移动平均线ma计算最高价与最低价的差值bd，再求其N2周期(200)的平均值bdma基于ma1和bdma计算4组上轨(up1-up4)和4组下轨(dw1-dw4)12、交易逻辑部分：当价格上穿ma1时开多仓，下穿ma1时开空仓采用金字塔加仓方式：在多头持仓时，价格触及下轨(dw1-dw3)逐步加仓；在空头持仓时，价格触及上轨(up1-up3)逐步加仓设置止盈平仓条件：当价格触及极端上轨up4时平空仓，触及极端下轨dw4时平多仓使用CurrStatus变量跟踪当前持仓状态