二、交易规则对于超人系统，我们定义了多头和空头设置、进场、保护性止损和盈利目标。接下来将定义我们的交易规则。多头和空头设置a)当MarketStrength大于或等于95，DollarPerBar1大于或等于0，DollarPerBar2小于或等于0时，多头设置就生效。b)当MarketStrength小于或等于-95，DollarPerBar1小于或等于0，DollarPerBar2大于或等于0时，卖空设置生效。多头和空头进场a)多头进入是在n周期bar高点之上的一个点。b)空头进场是在n周期低点以下的一个点。多头和空头退出a)在追踪止损点、利润目标点或卖空信号时退出多头。b)在移动止损点、盈利目标处或在发出做多信号时退出做空。三、程式码（仅适用于multicharts平台，其它平台自行移植）
// 输入参数定义Inputs:     Length(5),       // 计算市场强度和最高/低价的周期长度    StopLen(6),      // 止损计算周期长度      ProfitFactor(1); // 盈利乘数因子
// 变量声明Variables:     MktStrength(0),      // 市场强度指标值    DollarsPerBar1(0),   // 近期价格变化值(美元)    DollarsPerBar2(0),   // 远期价格变化值(美元)    StopLoss(0);         // 止损价格
// 计算市场强度指标(0-100范围)MktStrength = MarketStrength(Length);
// 计算价格变化(美元计价):// 最近4根K线的平均变化值DollarsPerBar1 = BigPointValue * ((Close - Close[4]) / 4);  // 前4-8根K线的平均变化值  DollarsPerBar2 = BigPointValue * ((Close[4] - Close[8]) / 5);
// 条件判断:Condition1 = DollarsPerBar1 >= 0;  // 近期趋势向上Condition2 = DollarsPerBar2 >= 0;  // 远期趋势向上
// 多头入场条件:// 市场强度≥95 + 近期上涨 + 远期下跌If MktStrength >= 95 AND Condition1 = True AND Condition2 = False Then Begin    // 当前高点创周期新高 + 无持仓    If High = Highest(High, Length) AND MarketPosition <> 1 Then Begin        Buy Next Bar at Highest(High, Length) Stop;  // 突破买入        buytocover ("LngRev") This Bar on Close;     // 平空单(反转策略)        StopLoss = Lowest(Low, StopLen);             // 设置止损(周期最低点)        sell ("InitLng") Next Bar at StopLoss Stop;  // 初始止损单    End;End;
// 空头入场条件:// 市场强度≤-95 + 近期下跌 + 远期上涨  If MktStrength <= -95 AND Condition1 = False AND Condition2 = True Then Begin    // 当前低点创周期新低 + 无持仓    If Low = Lowest(Low, Length) AND MarketPosition <> -1 Then Begin        sellshort Next Bar at Lowest(Low, Length) Stop;  // 突破卖空        sell ("ShrtRev") This Bar on Close;              // 平多单(反转策略)        StopLoss = Highest(High, StopLen);               // 设置止损(周期最高点)        buytocover ("InitShrt") Next Bar at StopLoss Stop; // 初始止损单    End;End;
// 动态止损模块If MarketPosition = 1 Then     sell ("LngStp") Next Bar at StopLoss Stop;       // 多头止损If MarketPosition = -1 Then     buytocover ("ShrtStp") Next Bar at StopLoss Stop; // 空头止损
// 止盈模块(基于止损距离的倍数)If MarketPosition = 1 Then     sell ("LngPrft") Next Bar at EntryPrice + ((EntryPrice - StopLoss) * ProfitFactor) Limit;If MarketPosition = -1 Then     buytocover ("ShrtPrft") Next Bar at EntryPrice - ((StopLoss - EntryPrice) * ProfitFactor) Limit;