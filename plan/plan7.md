主程式码{ System: catscan } Vars: ChoppyMarket(False), XMA1(Xaverage(TrueRange,10)), XMA2(Xaverage(TrueRange,10)),CI(0); //四个变量
CI=ChoppyIndex;//调用振荡指数函数ChoppyMarket = -ChopLimits <= CI and CI <= +ChopLimits; //如果是振荡市场,则 choppmarket为true}
{计算每日的atr指数加权移动平均}XMA1 = 0.818*XMA2+ 0.182*TrueRange; ｛振荡市场的每日tr的指数加权平均｝XMA2 = 0.818*XMA1 + 0.182*TrueRange;｛趋势市场的每日tr的指数加权平均｝
{振荡市场进场策略}If MarketPosition <> 1 and ChoppyMarket then Buy("ChoppyBuy") next bar at Open of Tomorrow+1.25*XMA1 Stop; If MarketPosition <>-1 and ChoppyMarket then Sell("ChoppySell") next bar at Open of Tomorrow-1.25*XMA1 Stop;

{ 趋势市场进场策略}If MarketPosition <> 1 and CI > ChopLimits then Buy("BullBuy") next bar at Open of Tomorrow+0.50*XMA2 Stop; If MarketPosition <>-1 and CI < -ChopLimits then Sell("BearSell") next bar at Open of Tomorrow-0.50*XMA2 Stop; 
{区间突破进场信号}If MarketPosition <>1 then Buy("ChannelBuy") next bar at the Highest(TrueHigh,40) + 1 Point Stop;If MarketPosition <>-1 then Sell("ChannelSell") next bar at the Lowest(TrueLow,40) - 1 Point Stop; 
{2000元止损出场在进场的下一bar开启}If MarketPosition = 1 then sell("MMLongExitStop") next bar at EntryPrice - (2000/BigPointValue) Stop;If MarketPosition =-1 then buytocover("MMShortExitStop")next bar at EntryPrice +(2000/BigPointValue) Stop;公式之一：{ 函数：ChoppyIndex}（振荡市场指数函数）If HighestBar(TrueHigh,30) <= LowestBar(TrueLow,30) then ChoppyIndex = 10*(Highest(TrueHigh,30)-Lowest(TrueLow,30))else ChoppyIndex = - 10*(Highest(TrueHigh,30)-Lowest(TrueLow,30));公式之二：{ 函数：ChopLimits }{此用户函数用于设置每个CATSCAN系统商品的Chop Limits。
如果Choppy指数在-Chop Limits和+Chop Limits之间，则为波动市场。如果波动指数大于+Chop Limits，则为看涨趋势市场。如果波动指数小于-Chop Limits，则为看跌趋势市场。}
If CurrentBar=1 then beginIf CommodityNumber = 2     then ChopLimits = 85;    {(LC)  Live Cattle}If CommodityNumber = 3     then ChopLimits = 100;  {(CC)  Cocoa}If CommodityNumber = 4     then ChopLimits = 70;    {(LH  Live Hogs}If CommodityNumber = 5     then ChopLimits = 50;    {(PB)  Pork Bellies}If CommodityNumber = 7     then ChopLimits = 60;    {(CT)  Cotton}If CommodityNumber = 8     then ChopLimits = 190;  {(HG )or (CP)  Copper}If CommodityNumber = 9     then ChopLimits = 230;  {(C) or (CN)  Corn}If CommodityNumber = 10   then ChopLimits = 140;  {(KC)  Coffee}If CommodityNumber = 11   then ChopLimits = 85;    {(LC)  Live Cattle}If CommodityNumber = 12   then ChopLimits = 140;  {(OJ)  Orange Juice}If CommodityNumber = 13   then ChopLimits = 40;    {(PL)  Platinum}If CommodityNumber = 16   then ChopLimits = 450;  {(SI) or (SV)  Silver}If CommodityNumber = 17   then ChopLimits = 700;  {(S) or (SD)  Soybeans}If CommodityNumber = 18   then ChopLimits = 160;  {(SM)  Soybean Meal}If CommodityNumber = 19   then ChopLimits = 27;    {(BO) Soybean Oil}If CommodityNumber = 20   then ChopLimits = 10;    {(SU) Sugar #11}If CommodityNumber = 21   then ChopLimits = 29;    {(W) or (WD)  Wheat}If CommodityNumber = 24   then ChopLimits = 37;    {(DM)  D Mark}If CommodityNumber = 25   then ChopLimits = 10;    {(SF)  Swiss Franc}If CommodityNumber = 26   then ChopLimits = 105;  {(BP)  British Lb}If CommodityNumber = 27   then ChopLimits = 170;  {(LB)  Lumber}If CommodityNumber = 30   then ChopLimits = 600;  {(GC)  Gold}If CommodityNumber = 41   then ChopLimits = 22;    {(TB)  T Bills}If CommodityNumber = 44   then ChopLimits = 62;    {(US)  T Bonds}If CommodityNumber = 65   then ChopLimits = 30;    {(JY)  Japanese Yen}If CommodityNumber = 89   then ChopLimits = 18;    {(HO)  Heating Oil}If CommodityNumber = 127 then ChopLimits = 10;    {(S2)  Day Swiss Franc}If CommodityNumber = 128 then ChopLimits = 105;  {(B2)  Day British Lb}If CommodityNumber = 141 then ChopLimits = 19;    {(ED  Euro$}If CommodityNumber = 144 then ChopLimits = 62;    {(TQ)  Day T Bonds}If CommodityNumber = 149 then ChopLimits = 60;    {(SP)  S&P 500}If CommodityNumber = 150 then ChopLimits = 50;    {(TY)  T Notes}If CommodityNumber = 188 then ChopLimits = 16;    {(CL)  Crude Oil}If CommodityNumber = 224 then ChopLimits = 75;    {(HU)  Unleaded Gas}If CommodityNumber = 250 then ChopLimits = 50;    {(TT)   Day T Notes}If CommodityNumber = 261 then ChopLimits = 37;    {(D2)  Day D Mark}If CommodityNumber = 262 then ChopLimits = 30;    {(J2)   Day Japanese Yen}If CommodityNumber = 263 then ChopLimits = 30;    {(DX   US $ Index}If CommodityNumber = 269 then ChopLimits = 19;    {(E2)  Day Euro$}end;