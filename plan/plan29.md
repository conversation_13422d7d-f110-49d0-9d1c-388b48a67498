一、multicharts平台程式码：// 策略参数定义Input:     ADXLen(14),    // ADX指标计算周期    AvgLen(4),     // ADX变化率的平均周期    CH(8);         // 通道保持周期（用于限制交易频率）
// 变量声明Vars:     UpCh(0),       // 上轨通道价格（买入触发价）    DnCh(0),       // 下轨通道价格（卖出触发价）    MP(0),         // 当前持仓状态（0=空仓，1=多仓，-1=空仓）    StopPrice(0),  // 动态止损价格    DeltaADX(0),   // ADX指标单周期变化值    DeltaAvg(0),   // ADX变化率的加权平均值    LastOrderBar(0); // 记录最后一次开仓的K线编号
// 获取当前持仓状态MP = MarketPosition;
// 计算ADX指标及其变化Value1 = ADX(ADXLen);               // 计算ADX指标DeltaADX = Value1 - Value1[1];      // 计算ADX变化值DeltaAvg = WAverage(DeltaADX, AvgLen); // 计算ADX变化率的加权平均
// 通道生成条件：当ADX变化率转弱或由负转正时If (DeltaAvg < 0 or DeltaAvg crosses over 0) and BarNumber > 1 then Begin    // 计算通道上下轨（基于收盘价±最近4根K线平均波幅的一半）    UpCh = Close + Average(Range, 4)/2;    DnCh = Close - Average(Range, 4)/2;    LastOrderBar = BarNumber;  // 记录通道生成时的K线编号End;
// 调试输出（可注释掉）print(date,time,upch,dnch);
// 持仓状态下禁用新通道生成If MP <> 0 then LastOrderBar = -999;
// 在通道有效期内（生成后CH根K线内）执行交易逻辑If BarNumber < LastOrderBar + CH then Begin    // 突破上轨做多，突破下轨做空    Buy next bar at UpCh stop;    sellshort next bar at DnCh stop;
    // 反向止损单（当没有对应持仓时）    If MP <> 1 then sell ("LEntryStop") next bar at Lowest(Low,6) stop;    If MP <> -1 then buytocover ("SEntryStop") next bar at Highest(High,6) stop;End;
// 多头开仓时的初始化If MP = 1 and MP[1] <> 1 then Begin    LastOrderBar = -999;  // 禁用新通道生成    StopPrice = Low - Average(Range,4);  // 初始止损价（低于开仓K线最低价）End;
// 空头开仓时的初始化If MP = -1 and MP[1] <> -1 then Begin    StopPrice = High + Average(Range,4); // 初始止损价（高于开仓K线最高价）    LastOrderBar = -999;  // 禁用新通道生成End;
// 多头持仓时的动态止损逻辑If MP = 1 then Begin    sell ("LongTrailStop") next bar at StopPrice stop;  // 挂止损单    StopPrice = StopPrice + (Low-StopPrice)/3;  // 动态上移止损价（1/3法则）End;
// 空头持仓时的动态止损逻辑If MP = -1 then Begin    buytocover ("ShortTrailStop") next bar at StopPrice stop;  // 挂止损单    StopPrice = StopPrice - (StopPrice-High)/3;  // 动态下移止损价（1/3法则）End;
二、原理说明在这个系统中，我们定义了多头和空头入场以及离场交易、多头和空头入场反转头寸，而退出则平掉您现有的头寸。我们还进行了一些设置工作，其中包括计算 ADX 和 ADX 差值的 4 条指数平均值。接下来描述设置、进入和退出。设置a) 计算 ADX 值。b) 计算 ADX 差值。c) 计算 ADX 差值的 4 周期平均值。做多a) 如果 ADX 差值的指数平均值低于零或越过零，找到该范围的 4 根柱线平均值，将该平均值除以 2，然后将结果值添加到当前柱线的收盘价。b) 使用结果值设置止损单。此止损单将在接下来的 10 根柱线中保持有效。做空a) 同样，如果 ADX 差值的指数平均值低于零或越过零，则找到范围的 4 柱平均值，将该平均值除以 2，然后从收盘价中减去结果值。b) 使用结果值设置止损单。此止损单将在接下来的 10 根柱线中保持有效。退出a) 在多头头寸的入场上，我们在最后 8 根柱子的最低低点处发出多头头寸的退出订单。b) 在多头头寸中，一旦入场柱收盘，我们的计算4周期range平均值，将平均值除以 4，然后从当前柱的低点减去该值。这是我们的bar2多头退出价格。c) 当处于多头头寸时，对于bar 2 之后的所有bar，我们获得先前计算的退出价格，并将最低价与先前止损价之间的差值添加到它的三分之一。我们在每个bar线的末尾重复此操作，为我们提供所有后续bar线的退出价格。d) 在空头头寸的入场bar上，我们在最近 8 根柱线中最高的高点处发出空头头寸的离场指令。e) 当处于空头位置时，一旦入场柱关闭，我们计算4周期range平均值并将其除以 4。然后，我们将此值添加到当前柱的最高价。这是我们对柱 2 的空头退出价格。f) 在空头头寸中，对于第 2 根柱线之后的所有bar线，我们获得先前计算的退出价格，并从中减去前一个止损价与当前柱线最高价之间的差值的三分之一。在每个柱线结束时重复此操作将为您提供第 3 柱线及以上的退出价格。g) 我们还设置了资金管理保护性止损（止损），其金额取决于我们交易的股票/合约的数量和数量。